module.exports = {
  plugins: [
    // Import CSS files (for @import statements)
    require('postcss-import')({
      // Skip duplicate imports by default
      skipDuplicates: true,
    }),
    
    // Tailwind CSS processing
    require('tailwindcss'),
    
    // CSS optimization plugins (order matters for proper cascade handling)
    
    // 1. Combine duplicate selectors first
    require('postcss-combine-duplicated-selectors')({
      // Remove duplicate selectors but preserve cascade order
      removeDuplicatedProperties: true,
      removeDuplicatedValues: true,
    }),
    
    // 2. Merge similar rules
    require('postcss-merge-rules')({
      // Only merge rules that are safe to merge
      safe: true,
    }),
    
    // 3. Remove duplicate declarations within the same rule
    require('postcss-discard-duplicates'),
    
    // 4. Remove overridden declarations (later rules override earlier ones)
    require('postcss-discard-overridden'),
    
    // 5. Sort media queries for better compression
    require('postcss-sort-media-queries')({
      // Sort mobile-first (min-width first)
      sort: 'mobile-first',
    }),
    
    // 6. Add vendor prefixes
    require('autoprefixer'),
    
    // 7. Final minification (only in production)
    ...(process.env.NODE_ENV === 'production' ? [
      require('cssnano')({
        preset: ['default', {
          // Preserve important comments
          discardComments: {
            removeAll: false,
          },
          // Don't merge rules that might break cascade
          mergeRules: false,
          // Don't normalize whitespace in calc() functions
          normalizeWhitespace: false,
          // Preserve CSS custom properties
          reduceIdents: false,
          // Don't convert values that might break functionality
          convertValues: false,
          // Don't merge longhand properties
          mergeLonghand: false,
          // Don't discard unused rules (we want to keep all Tailwind utilities)
          discardUnused: false,
        }],
      })
    ] : []),
  ],
}
