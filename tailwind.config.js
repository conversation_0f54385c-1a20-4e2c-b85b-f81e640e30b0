/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    // PHP files
    "./**/*.php",
    "./public/**/*.php",
    "./views/**/*.php",
    "./admin/**/*.php",
    "./themes/**/*.php",
    "./includes/**/*.php",
    "./components/**/*.php",
    
    // HTML files
    "./**/*.html",
    "./public/**/*.html",
    "./views/**/*.html",
    "./templates/**/*.html",
    
    // JavaScript files
    "./**/*.js",
    "./public/**/*.js",
    "./assets/**/*.js",
    "./js/**/*.js",
    "./scripts/**/*.js",
    
    // CSS files (for @apply directives)
    "./assets/**/*.css",
    "./themes/**/*.css",
    "./css/**/*.css",
    
    // Template files
    "./**/*.twig",
    "./**/*.blade.php",
    "./**/*.vue",
    "./**/*.jsx",
    "./**/*.tsx",
    
    // Exclude node_modules and other unnecessary directories
    "!./node_modules/**/*",
    "!./vendor/**/*",
    "!./.git/**/*"
  ],
  theme: {
    // Custom container configuration
    container: {
      center: true,
      padding: '16px',
    },
    extend: {
      // Custom colors for gaming theme
      colors: {
        'gaming': {
          'purple': '#272450',
          'blue': '#5081FF',
          'deep-blue': '#3463DB',
          'light-blue': '#4B7DFF',
          'green': '#00FF88',
          'gold': '#FFD700',
          'red': '#FF6B6B',
          'orange': '#FF8C00',
          'violet': '#9C27B0',
          'dark': '#0F172A',
          'slate': '#1E293B',
          'navy': '#13112E'
        },
        'mandy': {
          '500': '#E53E3E'
        },
        'success': {
          'light': '#D4EDDA',
          'dark-light': '#155724'
        }
      },
      // Custom font families
      fontFamily: {
        'signika': ['Signika', 'sans-serif'],
        'league-spartan': ['League Spartan', 'Arial', 'sans-serif'],
        'spartan': ['League Spartan', 'Arial', 'sans-serif']
      },
      // Custom animations
      animation: {
        'shimmer': 'shimmer 2.5s infinite linear',
        'glow': 'glow 2s ease-in-out infinite',
        'pulse-glow': 'pulse-glow 3s ease-in-out infinite',
        'rotate': 'rotate 8s linear infinite',
        'float': 'float 3s ease-in-out infinite'
      },
      // Custom keyframes
      keyframes: {
        shimmer: {
          '0%': { 
            left: '-100%', 
            transform: 'translateX(-100%)' 
          },
          '100%': { 
            left: '100%', 
            transform: 'translateX(100%)' 
          }
        },
        glow: {
          '0%, 100%': { 
            boxShadow: '0 0 20px rgba(99, 102, 241, 0.3)' 
          },
          '50%': { 
            boxShadow: '0 0 30px rgba(99, 102, 241, 0.6)' 
          }
        },
        'pulse-glow': {
          '0%, 100%': { 
            boxShadow: '0 0 20px rgba(99, 102, 241, 0.4)',
            borderColor: 'rgba(99, 102, 241, 0.6)'
          },
          '50%': { 
            boxShadow: '0 0 40px rgba(99, 102, 241, 0.8)',
            borderColor: 'rgba(99, 102, 241, 1)'
          }
        },
        rotate: {
          '0%': { transform: 'rotate(0deg)' },
          '100%': { transform: 'rotate(360deg)' }
        },
        float: {
          '0%, 100%': { transform: 'translateY(0px)' },
          '50%': { transform: 'translateY(-10px)' }
        }
      },
      // Custom spacing
      spacing: {
        '18': '4.5rem',
        '88': '22rem',
        '128': '32rem'
      },
      // Custom min-height
      minHeight: {
        'home': 'calc(100vh - 80px)',
        'screen-80': '80vh'
      },
      // Custom backdrop blur
      backdropBlur: {
        'xs': '2px'
      }
    },
  },
  plugins: [
    // Add any additional plugins here if needed
    // require('@tailwindcss/forms'),
    // require('@tailwindcss/typography'),
  ],
  // Safelist important classes that might be generated dynamically
  safelist: [
    // Gaming color classes
    'bg-gaming-purple',
    'bg-gaming-blue',
    'bg-gaming-deep-blue',
    'bg-gaming-light-blue',
    'bg-gaming-green',
    'bg-gaming-gold',
    'bg-gaming-red',
    'bg-gaming-orange',
    'bg-gaming-violet',
    'bg-gaming-dark',
    'bg-gaming-slate',
    'bg-gaming-navy',

    // Border colors
    'border-gaming-purple',
    'border-gaming-blue',
    'border-gaming-deep-blue',
    'border-gaming-light-blue',
    'border-gaming-green',
    'border-gaming-gold',
    'border-gaming-red',
    'border-gaming-orange',
    'border-gaming-violet',
    'border-gaming-dark',
    'border-gaming-slate',
    'border-gaming-navy',

    // Mandy color classes (used in muanick.php)
    'bg-mandy-500',
    'bg-mandy-500/90',
    'text-mandy-500',
    'border-mandy-500',

    // Success/Error color classes
    'bg-success-light',
    'bg-success-dark-light',
    'border-success',

    // Specific hex color classes used in muanick.php
    'bg-[#272450]',
    'border-[#5081FF]',
    'hover:border-[#5081FF]',
    'shadow-[#5081FF]/40',
    'hover:shadow-[#5081FF]/40',

    // Z-index classes
    'z-[10000]',
    'z-[999998]',
    'z-[999999]',
    
    // Text colors
    'text-gaming-purple',
    'text-gaming-blue',
    'text-gaming-deep-blue',
    'text-gaming-light-blue',
    'text-gaming-green',
    'text-gaming-gold',
    'text-gaming-red',
    'text-gaming-orange',
    'text-gaming-violet',
    'text-gaming-dark',
    'text-gaming-slate',
    'text-gaming-navy',
    
    // Animation classes
    'animate-shimmer',
    'animate-glow',
    'animate-pulse-glow',
    'animate-rotate',
    'animate-float',
    
    // Font families
    'font-signika',
    'font-league-spartan',
    'font-spartan',
    
    // Common dynamic classes with arbitrary values
    'bg-[#0F172A]',
    'bg-[#1E293B]',
    'bg-[#272450]',
    'bg-[#5081FF]',
    'bg-[#3463DB]',
    'bg-[#6366F1]',
    'bg-[#8B5CF6]',
    'bg-[#06B6D4]',
    'bg-[#10B981]',
    'bg-[#F59E0B]',
    'bg-[#EF4444]',
    'text-[#FFFFFF]',
    'text-[#FFFFFFCC]',
    'text-[#C7D2FE]',
    'text-[#9F9BAB]',
    'border-[#5081FF]',
    'border-[#6366F1]',
    'border-[#3463DB]',

    // Custom CSS Module Classes
    '_box-number-account_1eg83_36',
    '_short-account-border_1eg83_17',
    '_image-container_1eg83_26',
    '_button-icon_1eg83_47',

    // Gaming component classes
    'gaming-btn-primary',
    'gaming-input',
    'gaming-select',
    'gaming-card',
    'gaming-info-card',
    'gaming-bank-card',

    // Important responsive width classes
    'lg:!w-2/5',
    'lg:!w-3/5',
    'w-full',
    'lg:w-2/5',
    'lg:w-3/5',
    // Legacy classes for backward compatibility
    'lg:!w-1/3',
    'lg:!w-2/3',
    'lg:w-1/3',
    'lg:w-2/3'
  ]
}
