/* Legacy Third-Party Component Styles */
/* This file contains CSS from previous themes for third-party libraries */
/* These styles will be processed and deduplicated by PostCSS */

/* ===== QUILL EDITOR STYLES ===== */
.ql-snow .ql-editor img {
  margin: 20px;
  height: 176px;
  width: 256px;
}

.ltr .ql-snow .ql-editor img {
  margin-left: 0;
}

.rtl .ql-snow .ql-editor img {
  margin-right: 0;
}

.dark .ql-container.ql-snow,
.dark .ql-toolbar.ql-snow {
  border-color: #17263c;
}

.dark .ql-container.ql-snow {
  background-color: #121e32;
}

.ql-toolbar.ql-snow {
  box-sizing: border-box;
  border-top-left-radius: 6px;
  border-top-right-radius: 6px;
  border-width: 1px;
  border-color: #e0e6ed !important;
  padding: 8px;
  font-family: Nunito, sans-serif;
}

.dark .ql-container.ql-snow,
.dark .ql-toolbar.ql-snow {
  border-color: #17263c !important;
}

.ql-container.ql-snow {
  border-bottom-right-radius: 6px;
  border-bottom-left-radius: 6px;
  border-width: 1px;
  border-top: 0 !important;
  border-color: #e0e6ed !important;
}

.ql-snow .ql-editor {
  max-height: 200px;
  min-height: 200px;
  overflow: auto;
}

.rtl .ql-snow .ql-editor {
  text-align: right;
}

.dark .ql-snow .ql-stroke {
  stroke: #888ea8;
}

.dark .ql-snow .ql-editor h1,
.dark .ql-snow .ql-editor p,
.dark .ql-snow .ql-picker {
  color: #888ea8;
}

.rtl .ql-snow .ql-picker:not(.ql-color-picker):not(.ql-icon-picker) svg {
  right: auto !important;
  left: 0;
}

.dark .ql-snow .ql-tooltip {
  background-color: #060818;
  border-color: #17263c;
  color: #888ea8;
}

.ql-snow .ql-tooltip input[type=text] {
  outline: none !important;
  box-shadow: none !important;
}

.dark .ql-snow .ql-tooltip input[type=text] {
  background-color: #121e32;
  border-color: #17263c;
  color: #888ea8;
}

.rtl .ql-toolbar.ql-snow .ql-formats {
  margin-right: 0 !important;
  margin-left: 15px;
}

/* ===== FULLCALENDAR STYLES ===== */
.calendar-wrapper .fc-view-harness {
  overflow: auto;
}

.calendar-wrapper .fc-view-harness-active > .fc-view {
  min-width: 450px;
}

.calendar-wrapper .fc-daygrid-body-balanced .fc-scrollgrid-sync-table {
  min-height: 450px;
}

.calendar-wrapper table th.fc-day {
  padding: 12px 16px;
  background-color: #e0e6ed4d !important;
}

.dark .calendar-wrapper table th.fc-day {
  background-color: #1a2941 !important;
}

.calendar-wrapper table td .fc-daygrid-day-number {
  padding: 16px;
}

@media only screen and (max-width: 1024px) {
  .calendar-wrapper table td .fc-daygrid-day-number {
    padding: 2px 8px;
  }
}

.fc-theme-standard .fc-scrollgrid,
.fc-theme-standard td,
.fc-theme-standard th {
  border-color: #e0e6ed66 !important;
}

.dark .fc-theme-standard .fc-scrollgrid,
.dark .fc-theme-standard td,
.dark .fc-theme-standard th {
  border-color: #191e3a !important;
}

.calendar-wrapper .fc-theme-standard .fc-scrollgrid {
  border-radius: 10px;
}

.calendar-wrapper .fc-theme-standard td {
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
}

.calendar-wrapper .fc-theme-standard th {
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
}

.calendar-wrapper .fc-button {
  text-transform: capitalize !important;
  font-weight: 500 !important;
  border-radius: 6px !important;
  padding: 8px 15px !important;
}

.ltr .calendar-wrapper .fc-button {
  margin-left: 12px !important;
}

.rtl .calendar-wrapper .fc-button {
  margin-right: 12px !important;
}

.ltr .calendar-wrapper .fc-button-group .fc-button:first-child {
  margin-left: 0 !important;
}

.rtl .calendar-wrapper .fc-button-group .fc-button:first-child {
  margin-right: 0 !important;
}

.calendar-wrapper .fc-button-primary,
.calendar-wrapper .fc-button-primary:disabled {
  color: #4361ee !important;
  border-color: #4361ee !important;
  background: #0000 !important;
  box-shadow: none !important;
  font-weight: 600 !important;
  line-height: 20px !important;
}

.calendar-wrapper .fc-button-primary:not(:disabled).fc-button-active,
.calendar-wrapper .fc-button-primary:not(:disabled):hover {
  background-color: #4361ee !important;
  color: #fff !important;
}

/* Calendar Event Colors */
.calendar-wrapper .fc-daygrid-event.info,
.calendar-wrapper .fc-timegrid-event.info {
  background-color: #2196f3cc;
  border-color: #2196f3cc;
}

.calendar-wrapper .fc-daygrid-event.info:hover,
.calendar-wrapper .fc-timegrid-event.info:hover {
  background-color: #2196f3;
  border-color: #2196f3;
}

.calendar-wrapper .fc-daygrid-event.primary,
.calendar-wrapper .fc-timegrid-event.primary {
  background-color: #4361eecc;
  border-color: #4361eecc;
}

.calendar-wrapper .fc-daygrid-event.primary:hover,
.calendar-wrapper .fc-timegrid-event.primary:hover {
  background-color: #4361ee;
  border-color: #4361ee;
}

.calendar-wrapper .fc-daygrid-event.success,
.calendar-wrapper .fc-timegrid-event.success {
  background-color: #00ab55cc;
  border-color: #00ab55cc;
}

.calendar-wrapper .fc-daygrid-event.success:hover,
.calendar-wrapper .fc-timegrid-event.success:hover {
  background-color: #00ab55;
  border-color: #00ab55;
}

.calendar-wrapper .fc-daygrid-event.danger,
.calendar-wrapper .fc-timegrid-event.danger {
  background-color: #e7515acc;
  border-color: #e7515acc;
}

.calendar-wrapper .fc-daygrid-event.danger:hover,
.calendar-wrapper .fc-timegrid-event.danger:hover {
  background-color: #e7515a;
  border-color: #e7515a;
}

.calendar-wrapper .fc-next-button,
.calendar-wrapper .fc-prev-button {
  background-color: initial !important;
  color: #4b5563 !important;
  border: 2px solid #e5e7eb !important;
  padding: 6px !important;
  display: flex !important;
  justify-content: center;
}

.dark .calendar-wrapper .fc-next-button,
.dark .calendar-wrapper .fc-prev-button {
  border-color: #374151 !important;
}

.calendar-wrapper .fc-button.fc-next-button:hover,
.calendar-wrapper .fc-button.fc-prev-button:hover,
.dark .calendar-wrapper .fc-button.fc-next-button:hover,
.dark .calendar-wrapper .fc-button.fc-prev-button:hover {
  color: #4361ee !important;
  border-color: #4361ee !important;
  background: #0000 !important;
}

.calendar-wrapper .fc-timegrid-body .fc-event-main-frame {
  flex-direction: column !important;
}

.ltr .calendar-wrapper .fc-button-group,
.ltr .calendar-wrapper .fc-event-main-frame,
.ltr .calendar-wrapper .fc-toolbar {
  flex-direction: row;
}

.rtl .calendar-wrapper .fc-button-group,
.rtl .calendar-wrapper .fc-event-main-frame,
.rtl .calendar-wrapper .fc-toolbar {
  flex-direction: row-reverse;
}

@media only screen and (max-width: 768px) {
  .calendar-wrapper .fc-toolbar {
    flex-direction: column !important;
  }
  
  .calendar-wrapper .fc-toolbar .fc-toolbar-chunk {
    margin-top: 16px;
  }
  
  .calendar-wrapper .fc-toolbar .fc-toolbar-chunk:first-child {
    margin-top: 0 !important;
  }
}

.calendar-wrapper .fc-toolbar-title {
  font-size: 20px;
}

.calendar-wrapper .fc .fc-popover {
  z-index: 10;
}

.calendar-wrapper .fc-event {
  padding: 2px 4px;
  color: #fff;
}

.calendar-wrapper .fc-timegrid-event-harness-inset .fc-timegrid-event {
  box-shadow: none;
  overflow: hidden;
}

.calendar-wrapper .fc-event-title.fc-sticky {
  font-weight: 700;
}

.calendar-wrapper .fc-daygrid-event-dot {
  display: none;
}

.calendar-wrapper .fc-daygrid-dot-event {
  border-width: 1px;
}

.calendar-wrapper .fc-event-time {
  padding: 1px !important;
  font-weight: 700;
  flex-shrink: 0;
}

.rtl .calendar-wrapper .fc-event-time {
  margin-left: 3px !important;
  margin-right: 0 !important;
}

.rtl .calendar-wrapper .fc-icon.fc-icon-chevron-left,
.rtl .calendar-wrapper .fc-icon.fc-icon-chevron-right {
  rotate: 180deg;
}

.dark .fc-theme-standard .fc-popover {
  background-color: #3b3f5c !important;
  border-color: #3b3f5c !important;
}

.dark .fc-theme-standard .fc-popover-header {
  background-color: #0e1726 !important;
  color: #888ea8 !important;
}

/* ===== SWIPER SLIDER STYLES ===== */
.swiper .swiper-button-disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

.rtl .swiper {
  direction: rtl;
}

.swiper-button-next {
  color: #4361ee;
}

#slider3 .swiper-wrapper,
#slider4 .swiper-wrapper {
  height: 320px !important;
}

#slider3 .swiper-wrapper .swiper-slide img,
#slider4 .swiper-wrapper .swiper-slide img {
  height: 100%;
  -o-object-fit: cover;
  object-fit: cover;
}

#slider3 .swiper-pagination .swiper-pagination-bullet {
  border-radius: 6px;
  width: 4px;
  height: 20px;
}

@media only screen and (max-width: 1200px) {
  #slider3 .swiper-wrapper,
  #slider4 .swiper-wrapper {
    height: 300px !important;
  }
}

@media only screen and (max-width: 600px) {
  #slider3 .swiper-wrapper,
  #slider4 .swiper-wrapper {
    height: 180px !important;
  }
}

#slider3 .swiper-pagination .swiper-pagination-bullet:hover {
  background-color: #fff;
}

#slider3 .swiper-pagination .swiper-pagination-bullet.swiper-pagination-bullet-active {
  background: #4361ee;
}

#slider4 .swiper-pagination {
  color: #fff;
}

#slider5 .swiper-pagination {
  position: relative;
  margin-top: 20px;
}

/* ===== SWEETALERT2 STYLES ===== */
.swal2-popup {
  flex-direction: column;
  justify-content: center;
  padding: 20px !important;
  box-sizing: border-box;
}

.dark .swal2-popup {
  background: #0e1726;
}

.dark .swal2-popup .swal2-title {
  color: #888ea8;
}

.swal2-popup .swal2-title {
  display: block;
  margin: 0 !important;
  width: 100%;
  color: #3b3f5c;
  font-size: 24px;
}

.swal2-popup .swal2-title:where([dir=ltr], [dir=ltr] *) {
  padding-right: 2.5rem !important;
}

.swal2-popup .swal2-title:where([dir=rtl], [dir=rtl] *) {
  padding-left: 2.5rem !important;
}

.swal2-popup .swal2-styled {
  box-shadow: 0 5px 20px 0 #0000001a !important;
  letter-spacing: 1px;
  font-size: 14px !important;
  line-height: 20px !important;
  transition: all 0.3s ease-out;
  -webkit-transition: all 0.3s ease-out;
  margin: 0 5px;
  padding: 8px 20px;
  border-radius: 6px;
}

.swal2-popup .swal2-styled.swal2-cancel {
  background-color: #fff !important;
  color: #4361ee;
  border: 1px solid #e8e8e8;
  box-shadow: none;
  padding: 7px 20px;
}

.dark .swal2-popup .swal2-styled.swal2-cancel {
  background-color: #3b3f5c !important;
  color: #e0e6ed;
  border-color: #3b3f5c;
}

.swal2-popup .swal2-styled.swal2-confirm {
  background-color: #4361ee;
}

.swal2-popup .swal2-styled.swal2-confirm:focus {
  box-shadow: none !important;
}

.swal2-popup .swal2-html-container {
  margin: 0 !important;
  color: #e95f2b;
  font-weight: 300;
}

.swal2-popup .swal2-html-container:where([dir=rtl], [dir=rtl] *) {
  padding-left: 2.5rem !important;
}

.swal2-popup .swal2-close {
  position: absolute;
  top: 16px;
  transition: color 0.1s ease-out;
  font-family: serif;
}

.swal2-popup .swal2-close:where([dir=ltr], [dir=ltr] *) {
  right: 1rem;
}

.swal2-popup .swal2-close:where([dir=rtl], [dir=rtl] *) {
  left: 1rem;
}

.dark .swal2-popup .swal2-close {
  color: #888ea8;
}

.swal2-popup.swal2-toast {
  flex-direction: row;
  align-items: center;
  width: auto !important;
  box-shadow: 0 0 0.625em #d9d9d9;
  padding: 2em;
  overflow-y: hidden;
  display: flex !important;
}

.swal2-popup.swal2-toast.swal2-show {
  animation: showSweetToast 0.5s;
}

.swal2-popup pre {
  color: #009688;
}

/* SweetAlert2 Icons */
.swal2-icon {
  position: relative;
  box-sizing: initial;
  justify-content: center;
  width: 5em;
  height: 5em;
  margin: 1.25em auto 1.875em;
  border-radius: 50%;
  border: 0.25em solid #000;
  font-family: inherit;
  line-height: 5em;
  cursor: default;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
}

.swal2-icon:not(.swal2-error):not(.swal2-success) {
  width: 2em !important;
  line-height: 119px !important;
  height: 2em !important;
  margin: 0.25em auto 0.875em !important;
}

.swal2-icon.swal2-error {
  border: 5px solid #f1f2f3 !important;
  box-shadow: 0 3px 25px 0 #716aca33;
}

.swal2-icon.swal2-warning {
  color: #fb4 !important;
}

.swal2-icon.swal2-info,
.swal2-icon.swal2-warning {
  border: 5px solid #f1f2f3 !important;
  box-shadow: 0 3px 25px 0 #716aca33;
  font-size: 60px;
  line-height: 80px;
  text-align: center;
}

.swal2-icon.swal2-info {
  color: #4361ee !important;
}

.swal2-icon.swal2-question {
  color: #805dca !important;
  border-style: solid;
  border-width: 4px !important;
  border-color: #0000 !important;
  box-shadow: 0 3px 25px 0 #716aca33;
  font-size: 60px;
  line-height: 80px;
  text-align: center;
}

.dark .swal2-icon.swal2-error,
.dark .swal2-icon.swal2-info,
.dark .swal2-icon.swal2-question,
.dark .swal2-icon.swal2-warning {
  border-color: #888ea8 !important;
  box-shadow: none !important;
}

/* ===== TIPPY.JS TOOLTIP STYLES ===== */
.tippy-box[data-theme~=primary] {
  background-color: #4361ee;
}

.tippy-box[data-theme~=primary][data-placement^=top] > .tippy-arrow:before {
  border-top-color: #4361ee;
}

.tippy-box[data-theme~=success] {
  background-color: #00ab55;
}

.tippy-box[data-theme~=success][data-placement^=top] > .tippy-arrow:before {
  border-top-color: #00ab55;
}

.tippy-box[data-theme~=info] {
  background-color: #2196f3;
}

.tippy-box[data-theme~=info][data-placement^=top] > .tippy-arrow:before {
  border-top-color: #2196f3;
}

.tippy-box[data-theme~=danger] {
  background-color: #e7515a;
}

.tippy-box[data-theme~=danger][data-placement^=top] > .tippy-arrow:before {
  border-top-color: #e7515a;
}

.tippy-box[data-theme~=warning] {
  background-color: #e2a03f;
}

.tippy-box[data-theme~=warning][data-placement^=top] > .tippy-arrow:before {
  border-top-color: #e2a03f;
}

.tippy-box[data-theme~=secondary] {
  background-color: #805dca;
}

.tippy-box[data-theme~=secondary][data-placement^=top] > .tippy-arrow:before {
  border-top-color: #805dca;
}

.tippy-box[data-theme~=dark] {
  background-color: #3b3f5c;
}

.tippy-box[data-theme~=dark][data-placement^=top] > .tippy-arrow:before {
  border-top-color: #3b3f5c;
}

/* ===== NOUISLIDER STYLES ===== */
.noUi-horizontal .noUi-handle {
  width: 25px !important;
  height: 20px !important;
  top: -8px !important;
}

.noUi-handle:after,
.noUi-handle:before {
  display: none !important;
}

.dark .noUi-connects {
  background: #1b2e4b;
}

.dark .noUi-target {
  border-color: #253b5c;
  background: #0000;
}

.dark .noUi-handle {
  background: #3b3f5c;
  border-color: #3b3f5c;
  box-shadow: none;
}

.dark .noUi-tooltip {
  background: #1b2e4b;
  border-color: #253b5c;
  color: #888ea8;
}

/* ===== FLATPICKR STYLES ===== */
.dark .flatpickr-calendar {
  border: 1px solid #0e1a2c;
  background: #0e1a2c;
  box-shadow: none;
}

.dark .flatpickr-calendar.arrowTop:after {
  border-bottom-color: #0e1a2c;
}

.dark .flatpickr-calendar.arrowBottom:after {
  border-top-color: #0e1a2c;
}

.dark .flatpickr-calendar .flatpickr-months .flatpickr-next-month svg,
.dark .flatpickr-calendar .flatpickr-months .flatpickr-prev-month svg {
  fill: #bfc9d4;
}

.flatpickr-current-month {
  font-size: 16px;
  padding: 3px 0 0;
  display: flex;
  justify-content: center;
  gap: 10px;
}

.dark .flatpickr-calendar .flatpickr-months .flatpickr-monthDropdown-months {
  color: #bfc9d4;
}

.dark .flatpickr-calendar .flatpickr-months .flatpickr-monthDropdown-months .flatpickr-monthDropdown-month {
  background-color: #1b2e4b;
}

.dark .flatpickr-calendar .flatpickr-months input.cur-year {
  color: #bfc9d4;
  height: 31px;
}

.dark .flatpickr-calendar .flatpickr-months .numInputWrapper span {
  height: 26%;
}

.dark .flatpickr-calendar .flatpickr-months .numInputWrapper span.arrowUp {
  top: 10px;
}

.dark .flatpickr-calendar .flatpickr-months .numInputWrapper span.arrowUp:after {
  border-bottom-color: #bfc9d4;
}

.dark .flatpickr-current-month .numInputWrapper span.arrowDown:after {
  border-top-color: #bfc9d4;
}

.dark .flatpickr-calendar .flatpickr-months .numInputWrapper span .arrowDown {
  top: 34%;
}

.dark .flatpickr-calendar .flatpickr-months .numInputWrapper span .arrowDown:after {
  border-top-color: #bfc9d4;
}

.dark .flatpickr-calendar .flatpickr-day {
  color: #888ea8;
  font-weight: 500;
}

.dark .flatpickr-calendar .flatpickr-day:hover {
  background: #191e3a;
  border-color: #191e3a;
}

.dark .flatpickr-calendar .flatpickr-day.flatpickr-disabled {
  color: #888ea838;
}

.dark .flatpickr-calendar .flatpickr-day.nextMonthDay,
.dark .flatpickr-calendar .flatpickr-day.prevMonthDay {
  color: #888ea838 !important;
}

.dark .flatpickr-calendar .flatpickr-day.selected {
  background: #009688;
  color: #0e1726;
  border-color: #009688;
  font-weight: 700;
}

.dark .flatpickr-calendar .flatpickr-day.today {
  border-color: #009688;
}

.dark .flatpickr-calendar .flatpickr-day.today:hover {
  border-color: #0e1726;
  background: #0e1726;
  color: #fff;
}
