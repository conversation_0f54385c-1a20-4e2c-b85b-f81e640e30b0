<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Container Configuration Test</title>
    <link rel="stylesheet" href="../assets/css/tailwind.css">
    <style>
        /* Visual helper to see the container boundaries */
        .container {
            border: 2px dashed #5081FF;
            background-color: rgba(80, 129, 255, 0.1);
        }
        
        .content-box {
            background-color: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
            padding: 1rem;
            margin: 1rem 0;
        }
        
        body {
            background-color: #0F172A;
            color: #FFFFFF;
            font-family: 'Signika', sans-serif;
        }
        
        .test-section {
            margin: 2rem 0;
        }
        
        .test-title {
            color: #5081FF;
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 1rem;
        }
        
        .measurement-info {
            background-color: rgba(80, 129, 255, 0.2);
            border: 1px solid #5081FF;
            padding: 0.75rem;
            border-radius: 0.5rem;
            margin: 1rem 0;
            font-size: 0.875rem;
        }
    </style>
</head>
<body>
    <div class="test-section">
        <h1 class="test-title text-center">Tailwind Container Configuration Test</h1>
        
        <div class="measurement-info">
            <strong>Expected Container Behavior:</strong>
            <ul class="list-disc list-inside mt-2 space-y-1">
                <li>Width: 100% on all screen sizes</li>
                <li>Margin: auto (centered)</li>
                <li>Padding: 16px horizontal on all screen sizes</li>
                <li>Max-width: responsive breakpoints (640px, 768px, 1024px, 1280px, 1536px)</li>
            </ul>
        </div>
    </div>

    <!-- Test 1: Basic Container -->
    <div class="test-section">
        <h2 class="test-title">Test 1: Basic Container</h2>
        <div class="container">
            <div class="content-box">
                <h3>Container with Default Configuration</h3>
                <p>This container should have:</p>
                <ul class="list-disc list-inside mt-2 space-y-1">
                    <li>16px horizontal padding (left and right)</li>
                    <li>Auto margins (centered)</li>
                    <li>100% width with responsive max-widths</li>
                </ul>
                <p class="mt-4">
                    <strong>Inspect this element</strong> to verify that the container has:
                    <code class="bg-gray-800 px-2 py-1 rounded">padding-left: 16px</code> and 
                    <code class="bg-gray-800 px-2 py-1 rounded">padding-right: 16px</code>
                </p>
            </div>
        </div>
    </div>

    <!-- Test 2: Container with Additional Classes -->
    <div class="test-section">
        <h2 class="test-title">Test 2: Container with Additional Classes</h2>
        <div class="container bg-gaming-slate/20 rounded-lg">
            <div class="content-box">
                <h3>Container with Gaming Theme Background</h3>
                <p>This container combines the default container behavior with additional styling:</p>
                <ul class="list-disc list-inside mt-2 space-y-1">
                    <li>Still maintains 16px horizontal padding</li>
                    <li>Has gaming theme background color</li>
                    <li>Has rounded corners</li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Test 3: Nested Content -->
    <div class="test-section">
        <h2 class="test-title">Test 3: Nested Content Test</h2>
        <div class="container">
            <div class="content-box">
                <h3>Nested Grid Layout</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mt-4">
                    <div class="bg-gaming-blue/20 p-4 rounded">
                        <h4 class="font-bold">Column 1</h4>
                        <p>Content respects container padding</p>
                    </div>
                    <div class="bg-gaming-purple/20 p-4 rounded">
                        <h4 class="font-bold">Column 2</h4>
                        <p>16px padding maintained</p>
                    </div>
                    <div class="bg-gaming-green/20 p-4 rounded">
                        <h4 class="font-bold">Column 3</h4>
                        <p>Responsive layout works</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Test 4: Multiple Containers -->
    <div class="test-section">
        <h2 class="test-title">Test 4: Multiple Containers</h2>
        
        <div class="container mb-4">
            <div class="content-box">
                <h3>First Container</h3>
                <p>Each container should maintain consistent 16px horizontal padding.</p>
            </div>
        </div>
        
        <div class="container mb-4">
            <div class="content-box">
                <h3>Second Container</h3>
                <p>All containers should be centered and have the same padding behavior.</p>
            </div>
        </div>
        
        <div class="container">
            <div class="content-box">
                <h3>Third Container</h3>
                <p>This demonstrates consistency across multiple container instances.</p>
            </div>
        </div>
    </div>

    <!-- Test 5: Responsive Behavior -->
    <div class="test-section">
        <h2 class="test-title">Test 5: Responsive Behavior</h2>
        <div class="container">
            <div class="content-box">
                <h3>Responsive Container Test</h3>
                <p>Resize your browser window to test responsive behavior:</p>
                <ul class="list-disc list-inside mt-2 space-y-1">
                    <li><strong>Mobile (< 640px):</strong> Full width with 16px padding</li>
                    <li><strong>SM (≥ 640px):</strong> Max-width 640px, centered, 16px padding</li>
                    <li><strong>MD (≥ 768px):</strong> Max-width 768px, centered, 16px padding</li>
                    <li><strong>LG (≥ 1024px):</strong> Max-width 1024px, centered, 16px padding</li>
                    <li><strong>XL (≥ 1280px):</strong> Max-width 1280px, centered, 16px padding</li>
                    <li><strong>2XL (≥ 1536px):</strong> Max-width 1536px, centered, 16px padding</li>
                </ul>
                <div class="mt-4 p-4 bg-yellow-500/20 border border-yellow-500/50 rounded">
                    <strong>Note:</strong> The 16px padding should remain consistent across all breakpoints, 
                    unlike the default Tailwind container which has no padding by default.
                </div>
            </div>
        </div>
    </div>

    <script>
        // Add some JavaScript to display current viewport information
        function updateViewportInfo() {
            const width = window.innerWidth;
            let breakpoint = 'XS';
            
            if (width >= 1536) breakpoint = '2XL';
            else if (width >= 1280) breakpoint = 'XL';
            else if (width >= 1024) breakpoint = 'LG';
            else if (width >= 768) breakpoint = 'MD';
            else if (width >= 640) breakpoint = 'SM';
            
            document.title = `Container Test - ${breakpoint} (${width}px)`;
        }
        
        updateViewportInfo();
        window.addEventListener('resize', updateViewportInfo);
    </script>
</body>
</html>
