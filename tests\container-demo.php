<?php
require_once $_SERVER['DOCUMENT_ROOT'] . '/config.php';
require_once $_SERVER['DOCUMENT_ROOT'] . '/public/includes/header.php';
require_once $_SERVER['DOCUMENT_ROOT'] . '/public/includes/navbar.php';
?>

<style>
/* Visual helper styles for demonstration */
.demo-container {
    border: 2px dashed #5081FF;
    background-color: rgba(80, 129, 255, 0.1);
    position: relative;
}

.demo-container::before {
    content: 'Container with 16px padding';
    position: absolute;
    top: -10px;
    left: 16px;
    background: #5081FF;
    color: white;
    padding: 2px 8px;
    font-size: 12px;
    border-radius: 4px;
    z-index: 10;
}

.demo-content {
    background-color: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.3);
    padding: 1rem;
    margin: 1rem 0;
    border-radius: 8px;
}

.measurement-box {
    background-color: rgba(80, 129, 255, 0.2);
    border: 1px solid #5081FF;
    padding: 0.75rem;
    border-radius: 0.5rem;
    margin: 1rem 0;
    font-size: 0.875rem;
}
</style>

<main class="flex-1 overflow-hidden relative z-[1]">
    <!-- Test 1: Basic Container Usage (like your existing pages) -->
    <div class="container demo-container md:px-[32px] lg:px-[16px]">
        <div class="my-[36px] mt-[24px] max-md:mt-0">
            <div class="demo-content">
                <h1 class="text-3xl font-bold text-gaming-blue mb-4">Container Configuration Test</h1>
                <div class="measurement-box">
                    <strong>✅ SUCCESS:</strong> This container now has built-in 16px horizontal padding!
                    <br><br>
                    <strong>Before:</strong> You had to manually add <code class="bg-gray-800 px-2 py-1 rounded">md:px-[32px] lg:px-[16px]</code>
                    <br>
                    <strong>After:</strong> The container automatically includes 16px padding on all screen sizes
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mt-6">
                    <div class="bg-gaming-blue/20 p-4 rounded-lg">
                        <h3 class="font-bold text-gaming-blue">Responsive Design</h3>
                        <p class="text-gray-300">Container maintains 16px padding across all breakpoints</p>
                    </div>
                    <div class="bg-gaming-purple/20 p-4 rounded-lg">
                        <h3 class="font-bold text-gaming-purple">Centered Layout</h3>
                        <p class="text-gray-300">Auto margins keep content centered</p>
                    </div>
                    <div class="bg-gaming-green/20 p-4 rounded-lg">
                        <h3 class="font-bold text-gaming-green">Consistent Spacing</h3>
                        <p class="text-gray-300">No more manual padding classes needed</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Test 2: Container without additional padding classes -->
    <div class="container demo-container">
        <div class="demo-content">
            <h2 class="text-2xl font-bold text-gaming-blue mb-4">Clean Container Usage</h2>
            <div class="measurement-box">
                <strong>✅ SIMPLIFIED:</strong> This container uses only the <code class="bg-gray-800 px-2 py-1 rounded">container</code> class
                <br><br>
                No additional padding classes needed - the 16px horizontal padding is built-in!
            </div>
            
            <div class="bg-gaming-slate/20 p-6 rounded-lg mt-4">
                <h3 class="text-xl font-semibold mb-3">Benefits of the New Configuration:</h3>
                <ul class="space-y-2 text-gray-300">
                    <li class="flex items-start">
                        <span class="text-gaming-green mr-2">✓</span>
                        <span>Consistent 16px horizontal padding across all screen sizes</span>
                    </li>
                    <li class="flex items-start">
                        <span class="text-gaming-green mr-2">✓</span>
                        <span>Automatic centering with margin: auto</span>
                    </li>
                    <li class="flex items-start">
                        <span class="text-gaming-green mr-2">✓</span>
                        <span>Responsive max-widths at standard breakpoints</span>
                    </li>
                    <li class="flex items-start">
                        <span class="text-gaming-green mr-2">✓</span>
                        <span>No need to manually add padding classes</span>
                    </li>
                    <li class="flex items-start">
                        <span class="text-gaming-green mr-2">✓</span>
                        <span>Cleaner, more maintainable code</span>
                    </li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Test 3: Multiple containers showing consistency -->
    <div class="space-y-6 mb-8">
        <div class="container demo-container">
            <div class="demo-content">
                <h3 class="text-xl font-bold text-gaming-blue">Container #1</h3>
                <p class="text-gray-300">All containers now have consistent behavior</p>
            </div>
        </div>
        
        <div class="container demo-container">
            <div class="demo-content">
                <h3 class="text-xl font-bold text-gaming-purple">Container #2</h3>
                <p class="text-gray-300">Same 16px padding, same centering behavior</p>
            </div>
        </div>
        
        <div class="container demo-container">
            <div class="demo-content">
                <h3 class="text-xl font-bold text-gaming-green">Container #3</h3>
                <p class="text-gray-300">Perfect for consistent layouts throughout your site</p>
            </div>
        </div>
    </div>

    <!-- Test 4: Comparison with your existing usage -->
    <div class="container demo-container">
        <div class="demo-content">
            <h2 class="text-2xl font-bold text-gaming-blue mb-4">Before vs After Comparison</h2>
            
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div class="bg-red-500/10 border border-red-500/30 p-4 rounded-lg">
                    <h3 class="text-lg font-bold text-red-400 mb-3">❌ Before (Manual Padding)</h3>
                    <code class="text-sm bg-gray-800 p-3 rounded block">
                        &lt;div class="container md:px-[32px] lg:px-[16px]"&gt;<br>
                        &nbsp;&nbsp;&lt;!-- Your content --&gt;<br>
                        &lt;/div&gt;
                    </code>
                    <p class="text-gray-400 mt-2 text-sm">
                        Required manual padding classes for each container
                    </p>
                </div>
                
                <div class="bg-green-500/10 border border-green-500/30 p-4 rounded-lg">
                    <h3 class="text-lg font-bold text-green-400 mb-3">✅ After (Built-in Padding)</h3>
                    <code class="text-sm bg-gray-800 p-3 rounded block">
                        &lt;div class="container"&gt;<br>
                        &nbsp;&nbsp;&lt;!-- Your content --&gt;<br>
                        &lt;/div&gt;
                    </code>
                    <p class="text-gray-400 mt-2 text-sm">
                        Clean, simple, with automatic 16px padding
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Test 5: Gaming theme integration -->
    <div class="container demo-container">
        <div class="demo-content">
            <h2 class="text-2xl font-bold text-gaming-blue mb-4">Gaming Theme Integration</h2>
            <div class="measurement-box">
                <strong>🎮 GAMING READY:</strong> The container works perfectly with your gaming theme components
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
                <div class="gaming-card p-6">
                    <h3 class="text-lg font-bold text-white mb-3">Gaming Card Component</h3>
                    <p class="text-gray-300 mb-4">Your existing gaming components work seamlessly with the new container configuration.</p>
                    <button class="gaming-btn-primary">
                        Test Button
                    </button>
                </div>
                
                <div class="gaming-card p-6">
                    <h3 class="text-lg font-bold text-white mb-3">Consistent Spacing</h3>
                    <p class="text-gray-300 mb-4">The 16px container padding provides perfect spacing for your gaming UI elements.</p>
                    <div class="flex gap-2">
                        <span class="bg-gaming-blue px-3 py-1 rounded text-sm">Tag 1</span>
                        <span class="bg-gaming-purple px-3 py-1 rounded text-sm">Tag 2</span>
                        <span class="bg-gaming-green px-3 py-1 rounded text-sm">Tag 3</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>

<script>
// Display current viewport information
function updateViewportInfo() {
    const width = window.innerWidth;
    let breakpoint = 'XS';
    
    if (width >= 1536) breakpoint = '2XL';
    else if (width >= 1280) breakpoint = 'XL';
    else if (width >= 1024) breakpoint = 'LG';
    else if (width >= 768) breakpoint = 'MD';
    else if (width >= 640) breakpoint = 'SM';
    
    // Update page title with current breakpoint
    document.title = `Container Demo - ${breakpoint} (${width}px) - ${document.title.split(' - ')[2] || 'Gaming Site'}`;
    
    // Add viewport info to page
    const existingInfo = document.getElementById('viewport-info');
    if (existingInfo) existingInfo.remove();
    
    const info = document.createElement('div');
    info.id = 'viewport-info';
    info.className = 'fixed top-4 right-4 bg-gaming-blue text-white px-3 py-2 rounded-lg text-sm font-bold z-50';
    info.textContent = `${breakpoint}: ${width}px`;
    document.body.appendChild(info);
}

updateViewportInfo();
window.addEventListener('resize', updateViewportInfo);

// Add some interactive feedback
document.addEventListener('DOMContentLoaded', function() {
    const containers = document.querySelectorAll('.demo-container');
    
    containers.forEach(container => {
        container.addEventListener('mouseenter', function() {
            this.style.borderColor = '#4B7DFF';
            this.style.backgroundColor = 'rgba(75, 125, 255, 0.15)';
        });
        
        container.addEventListener('mouseleave', function() {
            this.style.borderColor = '#5081FF';
            this.style.backgroundColor = 'rgba(80, 129, 255, 0.1)';
        });
    });
});
</script>

<?php require_once $_SERVER['DOCUMENT_ROOT'] . '/public/includes/footer.php'; ?>
