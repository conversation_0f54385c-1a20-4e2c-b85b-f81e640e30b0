.ql-snow .ql-editor img{
  height:176px;
  margin:20px;
  width:256px;
}
.ltr .ql-snow .ql-editor img{
  margin-left:0;
}
.rtl .ql-snow .ql-editor img{
  margin-right:0;
}
.dark .ql-container.ql-snow,.dark .ql-toolbar.ql-snow{
  border-color:#17263c !important;
}
.dark .ql-container.ql-snow{
  background-color:#121e32;
}
.ql-toolbar.ql-snow{
  border-color:#e0e6ed !important;
  border-top-left-radius:6px;
  border-top-right-radius:6px;
  border-width:1px;
  box-sizing:border-box;
  font-family:Nunito,sans-serif;
  padding:8px;
}
.ql-container.ql-snow{
  border-bottom-left-radius:6px;
  border-bottom-right-radius:6px;
  border-width:1px;
  border-top:0 !important;
  border-color:#e0e6ed !important;
}
.ql-snow .ql-editor{
  max-height:200px;
  min-height:200px;
  overflow:auto;
}
.rtl .ql-snow .ql-editor{
  text-align:right;
}
.dark .ql-snow .ql-stroke{
  stroke:#888ea8;
}
.dark .ql-snow .ql-editor h1,.dark .ql-snow .ql-editor p,.dark .ql-snow .ql-picker{
  color:#888ea8;
}
.rtl .ql-snow .ql-picker:not(.ql-color-picker):not(.ql-icon-picker) svg{
  left:0;
  right:auto !important;
}
.dark .ql-snow .ql-tooltip{
  background-color:#060818;
  border-color:#17263c;
  color:#888ea8;
}
.ql-snow .ql-tooltip input[type=text]{
  box-shadow:none !important;
  outline:none !important;
}
.dark .ql-snow .ql-tooltip input[type=text]{
  background-color:#121e32;
  border-color:#17263c;
  color:#888ea8;
}
.rtl .ql-toolbar.ql-snow .ql-formats{
  margin-left:15px;
  margin-right:0 !important;
}
.calendar-wrapper .fc-view-harness{
  overflow:auto;
}
.calendar-wrapper .fc-view-harness-active>.fc-view{
  min-width:450px;
}
.calendar-wrapper .fc-daygrid-body-balanced .fc-scrollgrid-sync-table{
  min-height:450px;
}
.calendar-wrapper table th.fc-day{
  background-color:#e0e6ed4d !important;
  padding:12px 16px;
}
.dark .calendar-wrapper table th.fc-day{
  background-color:#1a2941 !important;
}
.calendar-wrapper table td .fc-daygrid-day-number{
  padding:16px;
}
.fc-theme-standard .fc-scrollgrid,.fc-theme-standard td,.fc-theme-standard th{
  border-color:#e0e6ed66 !important;
}
.dark .fc-theme-standard .fc-scrollgrid,.dark .fc-theme-standard td,.dark .fc-theme-standard th{
  border-color:#191e3a !important;
}
.calendar-wrapper .fc-theme-standard .fc-scrollgrid{
  border-radius:10px;
}
.calendar-wrapper .fc-theme-standard td{
  border-bottom-left-radius:10px;
  border-bottom-right-radius:10px;
}
.calendar-wrapper .fc-theme-standard th{
  border-top-left-radius:10px;
  border-top-right-radius:10px;
}
.calendar-wrapper .fc-button{
  border-radius:6px !important;
  font-weight:500 !important;
  padding:8px 15px !important;
  text-transform:capitalize !important;
}
.ltr .calendar-wrapper .fc-button{
  margin-left:12px !important;
}
.rtl .calendar-wrapper .fc-button{
  margin-right:12px !important;
}
.ltr .calendar-wrapper .fc-button-group .fc-button:first-child{
  margin-left:0 !important;
}
.rtl .calendar-wrapper .fc-button-group .fc-button:first-child{
  margin-right:0 !important;
}
.calendar-wrapper .fc-button-primary,.calendar-wrapper .fc-button-primary:disabled{
  background:#0000 !important;
  border-color:#4361ee !important;
  box-shadow:none !important;
  color:#4361ee !important;
  font-weight:600 !important;
  line-height:20px !important;
}
.calendar-wrapper .fc-button-primary:not(:disabled).fc-button-active,.calendar-wrapper .fc-button-primary:not(:disabled):hover{
  background-color:#4361ee !important;
  color:#fff !important;
}
.calendar-wrapper .fc-daygrid-event.info,.calendar-wrapper .fc-timegrid-event.info{
  background-color:#2196f3cc;
  border-color:#2196f3cc;
}
.calendar-wrapper .fc-daygrid-event.info:hover,.calendar-wrapper .fc-timegrid-event.info:hover{
  background-color:#2196f3;
  border-color:#2196f3;
}
.calendar-wrapper .fc-daygrid-event.primary,.calendar-wrapper .fc-timegrid-event.primary{
  background-color:#4361eecc;
  border-color:#4361eecc;
}
.calendar-wrapper .fc-daygrid-event.primary:hover,.calendar-wrapper .fc-timegrid-event.primary:hover{
  background-color:#4361ee;
  border-color:#4361ee;
}
.calendar-wrapper .fc-daygrid-event.success,.calendar-wrapper .fc-timegrid-event.success{
  background-color:#00ab55cc;
  border-color:#00ab55cc;
}
.calendar-wrapper .fc-daygrid-event.success:hover,.calendar-wrapper .fc-timegrid-event.success:hover{
  background-color:#00ab55;
  border-color:#00ab55;
}
.calendar-wrapper .fc-daygrid-event.danger,.calendar-wrapper .fc-timegrid-event.danger{
  background-color:#e7515acc;
  border-color:#e7515acc;
}
.calendar-wrapper .fc-daygrid-event.danger:hover,.calendar-wrapper .fc-timegrid-event.danger:hover{
  background-color:#e7515a;
  border-color:#e7515a;
}
.calendar-wrapper .fc-next-button,.calendar-wrapper .fc-prev-button{
  background-color:initial !important;
  border:2px solid #e5e7eb !important;
  color:#4b5563 !important;
  display:flex !important;
  justify-content:center;
  padding:6px !important;
}
.dark .calendar-wrapper .fc-next-button,.dark .calendar-wrapper .fc-prev-button{
  border-color:#374151 !important;
}
.calendar-wrapper .fc-button.fc-next-button:hover,.calendar-wrapper .fc-button.fc-prev-button:hover,.dark .calendar-wrapper .fc-button.fc-next-button:hover,.dark .calendar-wrapper .fc-button.fc-prev-button:hover{
  background:#0000 !important;
  border-color:#4361ee !important;
  color:#4361ee !important;
}
.calendar-wrapper .fc-timegrid-body .fc-event-main-frame{
  flex-direction:column !important;
}
.ltr .calendar-wrapper .fc-button-group,.ltr .calendar-wrapper .fc-event-main-frame,.ltr .calendar-wrapper .fc-toolbar{
  flex-direction:row;
}
.rtl .calendar-wrapper .fc-button-group,.rtl .calendar-wrapper .fc-event-main-frame,.rtl .calendar-wrapper .fc-toolbar{
  flex-direction:row-reverse;
}
.calendar-wrapper .fc-toolbar-title{
  font-size:20px;
}
.calendar-wrapper .fc .fc-popover{
  z-index:10;
}
.calendar-wrapper .fc-event{
  color:#fff;
  padding:2px 4px;
}
.calendar-wrapper .fc-timegrid-event-harness-inset .fc-timegrid-event{
  box-shadow:none;
  overflow:hidden;
}
.calendar-wrapper .fc-event-title.fc-sticky{
  font-weight:700;
}
.calendar-wrapper .fc-daygrid-event-dot{
  display:none;
}
.calendar-wrapper .fc-daygrid-dot-event{
  border-width:1px;
}
.calendar-wrapper .fc-event-time{
  flex-shrink:0;
  font-weight:700;
  padding:1px !important;
}
.rtl .calendar-wrapper .fc-event-time{
  margin-left:3px !important;
  margin-right:0 !important;
}
.rtl .calendar-wrapper .fc-icon.fc-icon-chevron-left,.rtl .calendar-wrapper .fc-icon.fc-icon-chevron-right{
  rotate:180deg;
}
.dark .fc-theme-standard .fc-popover{
  background-color:#3b3f5c !important;
  border-color:#3b3f5c !important;
}
.dark .fc-theme-standard .fc-popover-header{
  background-color:#0e1726 !important;
  color:#888ea8 !important;
}
.swiper .swiper-button-disabled{
  cursor:not-allowed;
  opacity:0.6;
}
.rtl .swiper{
  direction:rtl;
}
.swiper-button-next{
  color:#4361ee;
}
#slider3 .swiper-wrapper,#slider4 .swiper-wrapper{
  height:320px !important;
}
#slider3 .swiper-wrapper .swiper-slide img,#slider4 .swiper-wrapper .swiper-slide img{
  height:100%;
  -o-object-fit:cover;
  object-fit:cover;
}
#slider3 .swiper-pagination .swiper-pagination-bullet{
  border-radius:6px;
  height:20px;
  width:4px;
}
#slider3 .swiper-pagination .swiper-pagination-bullet:hover{
  background-color:#fff;
}
#slider3 .swiper-pagination .swiper-pagination-bullet.swiper-pagination-bullet-active{
  background:#4361ee;
}
#slider4 .swiper-pagination{
  color:#fff;
}
#slider5 .swiper-pagination{
  margin-top:20px;
  position:relative;
}
.swal2-popup{
  box-sizing:border-box;
  flex-direction:column;
  justify-content:center;
  padding:20px !important;
}
.dark .swal2-popup{
  background:#0e1726;
}
.dark .swal2-popup .swal2-title{
  color:#888ea8;
}
.swal2-popup .swal2-title{
  color:#3b3f5c;
  display:block;
  font-size:24px;
  margin:0 !important;
  width:100%;
}
.swal2-popup .swal2-title:where([dir=ltr],[dir=ltr] *){
  padding-right:2.5rem !important;
}
.swal2-popup .swal2-title:where([dir=rtl],[dir=rtl] *){
  padding-left:2.5rem !important;
}
.swal2-popup .swal2-styled{
  border-radius:6px;
  box-shadow:0 5px 20px 0 #0000001a !important;
  font-size:14px !important;
  letter-spacing:1px;
  line-height:20px !important;
  margin:0 5px;
  padding:8px 20px;
  transition:all 0.3s ease-out;
  -webkit-transition:all 0.3s ease-out;
}
.swal2-popup .swal2-styled.swal2-cancel{
  background-color:#fff !important;
  border:1px solid #e8e8e8;
  box-shadow:none;
  color:#4361ee;
  padding:7px 20px;
}
.dark .swal2-popup .swal2-styled.swal2-cancel{
  background-color:#3b3f5c !important;
  border-color:#3b3f5c;
  color:#e0e6ed;
}
.swal2-popup .swal2-styled.swal2-confirm{
  background-color:#4361ee;
}
.swal2-popup .swal2-styled.swal2-confirm:focus{
  box-shadow:none !important;
}
.swal2-popup .swal2-html-container{
  color:#e95f2b;
  font-weight:300;
  margin:0 !important;
}
.swal2-popup .swal2-html-container:where([dir=rtl],[dir=rtl] *){
  padding-left:2.5rem !important;
}
.swal2-popup .swal2-close{
  font-family:serif;
  position:absolute;
  top:16px;
  transition:color 0.1s ease-out;
}
.swal2-popup .swal2-close:where([dir=ltr],[dir=ltr] *){
  right:1rem;
}
.swal2-popup .swal2-close:where([dir=rtl],[dir=rtl] *){
  left:1rem;
}
.dark .swal2-popup .swal2-close{
  color:#888ea8;
}
.swal2-popup.swal2-toast{
  align-items:center;
  box-shadow:0 0 0.625em #d9d9d9;
  display:flex !important;
  flex-direction:row;
  overflow-y:hidden;
  padding:2em;
  width:auto !important;
}
.swal2-popup.swal2-toast.swal2-show{
  animation:showSweetToast 0.5s;
}
.swal2-popup pre{
  color:#009688;
}
.swal2-icon{
  border:0.25em solid #000;
  border-radius:50%;
  box-sizing:initial;
  cursor:default;
  font-family:inherit;
  height:5em;
  justify-content:center;
  line-height:5em;
  margin:1.25em auto 1.875em;
  position:relative;
  -webkit-user-select:none;
  -moz-user-select:none;
  user-select:none;
  width:5em;
}
.swal2-icon:not(.swal2-error):not(.swal2-success){
  height:2em !important;
  line-height:119px !important;
  margin:0.25em auto 0.875em !important;
  width:2em !important;
}
.swal2-icon.swal2-error{
  border:5px solid #f1f2f3 !important;
  box-shadow:0 3px 25px 0 #716aca33;
}
.swal2-icon.swal2-warning{
  color:#fb4 !important;
}
.swal2-icon.swal2-info,.swal2-icon.swal2-warning{
  border:5px solid #f1f2f3 !important;
  box-shadow:0 3px 25px 0 #716aca33;
  font-size:60px;
  line-height:80px;
  text-align:center;
}
.swal2-icon.swal2-info{
  color:#4361ee !important;
}
.swal2-icon.swal2-question{
  border-color:#0000 !important;
  border-style:solid;
  border-width:4px !important;
  box-shadow:0 3px 25px 0 #716aca33;
  color:#805dca !important;
  font-size:60px;
  line-height:80px;
  text-align:center;
}
.dark .swal2-icon.swal2-error,.dark .swal2-icon.swal2-info,.dark .swal2-icon.swal2-question,.dark .swal2-icon.swal2-warning{
  border-color:#888ea8 !important;
  box-shadow:none !important;
}
.tippy-box[data-theme~=primary]{
  background-color:#4361ee;
}
.tippy-box[data-theme~=primary][data-placement^=top]>.tippy-arrow:before{
  border-top-color:#4361ee;
}
.tippy-box[data-theme~=success]{
  background-color:#00ab55;
}
.tippy-box[data-theme~=success][data-placement^=top]>.tippy-arrow:before{
  border-top-color:#00ab55;
}
.tippy-box[data-theme~=info]{
  background-color:#2196f3;
}
.tippy-box[data-theme~=info][data-placement^=top]>.tippy-arrow:before{
  border-top-color:#2196f3;
}
.tippy-box[data-theme~=danger]{
  background-color:#e7515a;
}
.tippy-box[data-theme~=danger][data-placement^=top]>.tippy-arrow:before{
  border-top-color:#e7515a;
}
.tippy-box[data-theme~=warning]{
  background-color:#e2a03f;
}
.tippy-box[data-theme~=warning][data-placement^=top]>.tippy-arrow:before{
  border-top-color:#e2a03f;
}
.tippy-box[data-theme~=secondary]{
  background-color:#805dca;
}
.tippy-box[data-theme~=secondary][data-placement^=top]>.tippy-arrow:before{
  border-top-color:#805dca;
}
.tippy-box[data-theme~=dark]{
  background-color:#3b3f5c;
}
.tippy-box[data-theme~=dark][data-placement^=top]>.tippy-arrow:before{
  border-top-color:#3b3f5c;
}
.noUi-horizontal .noUi-handle{
  height:20px !important;
  top:-8px !important;
  width:25px !important;
}
.noUi-handle:after,.noUi-handle:before{
  display:none !important;
}
.dark .noUi-connects{
  background:#1b2e4b;
}
.dark .noUi-target{
  background:#0000;
  border-color:#253b5c;
}
.dark .noUi-handle{
  background:#3b3f5c;
  border-color:#3b3f5c;
  box-shadow:none;
}
.dark .noUi-tooltip{
  background:#1b2e4b;
  border-color:#253b5c;
  color:#888ea8;
}
.dark .flatpickr-calendar{
  background:#0e1a2c;
  border:1px solid #0e1a2c;
  box-shadow:none;
}
.dark .flatpickr-calendar.arrowTop:after{
  border-bottom-color:#0e1a2c;
}
.dark .flatpickr-calendar.arrowBottom:after{
  border-top-color:#0e1a2c;
}
.dark .flatpickr-calendar .flatpickr-months .flatpickr-next-month svg,.dark .flatpickr-calendar .flatpickr-months .flatpickr-prev-month svg{
  fill:#bfc9d4;
}
.flatpickr-current-month{
  display:flex;
  font-size:16px;
  gap:10px;
  justify-content:center;
  padding:3px 0 0;
}
.dark .flatpickr-calendar .flatpickr-months .flatpickr-monthDropdown-months{
  color:#bfc9d4;
}
.dark .flatpickr-calendar .flatpickr-months .flatpickr-monthDropdown-months .flatpickr-monthDropdown-month{
  background-color:#1b2e4b;
}
.dark .flatpickr-calendar .flatpickr-months input.cur-year{
  color:#bfc9d4;
  height:31px;
}
.dark .flatpickr-calendar .flatpickr-months .numInputWrapper span{
  height:26%;
}
.dark .flatpickr-calendar .flatpickr-months .numInputWrapper span.arrowUp{
  top:10px;
}
.dark .flatpickr-calendar .flatpickr-months .numInputWrapper span.arrowUp:after{
  border-bottom-color:#bfc9d4;
}
.dark .flatpickr-current-month .numInputWrapper span.arrowDown:after{
  border-top-color:#bfc9d4;
}
.dark .flatpickr-calendar .flatpickr-months .numInputWrapper span .arrowDown{
  top:34%;
}
.dark .flatpickr-calendar .flatpickr-months .numInputWrapper span .arrowDown:after{
  border-top-color:#bfc9d4;
}
.dark .flatpickr-calendar .flatpickr-day{
  color:#888ea8;
  font-weight:500;
}
.dark .flatpickr-calendar .flatpickr-day:hover{
  background:#191e3a;
  border-color:#191e3a;
}
.dark .flatpickr-calendar .flatpickr-day.flatpickr-disabled{
  color:#888ea838;
}
.dark .flatpickr-calendar .flatpickr-day.nextMonthDay,.dark .flatpickr-calendar .flatpickr-day.prevMonthDay{
  color:#888ea838 !important;
}
.dark .flatpickr-calendar .flatpickr-day.selected{
  background:#009688;
  border-color:#009688;
  color:#0e1726;
  font-weight:700;
}
.dark .flatpickr-calendar .flatpickr-day.today{
  border-color:#009688;
}
.dark .flatpickr-calendar .flatpickr-day.today:hover{
  background:#0e1726;
  border-color:#0e1726;
  color:#fff;
}
*,:after,:before{--tw-border-spacing-x:0;--tw-border-spacing-y:0;--tw-translate-x:0;--tw-translate-y:0;--tw-rotate:0;--tw-skew-x:0;--tw-skew-y:0;--tw-scale-x:1;--tw-scale-y:1;--tw-pan-x:;--tw-pan-y:;--tw-pinch-zoom:;--tw-scroll-snap-strictness:proximity;--tw-gradient-from-position:;--tw-gradient-via-position:;--tw-gradient-to-position:;--tw-ordinal:;--tw-slashed-zero:;--tw-numeric-figure:;--tw-numeric-spacing:;--tw-numeric-fraction:;--tw-ring-inset:;--tw-ring-offset-width:0px;--tw-ring-offset-color:#fff;--tw-ring-color:rgba(59,130,246,.5);--tw-ring-offset-shadow:0 0 #0000;--tw-ring-shadow:0 0 #0000;--tw-shadow:0 0 #0000;--tw-shadow-colored:0 0 #0000;--tw-blur:;--tw-brightness:;--tw-contrast:;--tw-grayscale:;--tw-hue-rotate:;--tw-invert:;--tw-saturate:;--tw-sepia:;--tw-drop-shadow:;--tw-backdrop-blur:;--tw-backdrop-brightness:;--tw-backdrop-contrast:;--tw-backdrop-grayscale:;--tw-backdrop-hue-rotate:;--tw-backdrop-invert:;--tw-backdrop-opacity:;--tw-backdrop-saturate:;--tw-backdrop-sepia:;--tw-contain-size:;--tw-contain-layout:;--tw-contain-paint:;--tw-contain-style:;
  border-color:#e5e7eb;
  border-style:solid;
  border-width:0;}
::backdrop{--tw-ring-color:rgba(59,130,246,.5);--tw-border-spacing-x:0;--tw-border-spacing-y:0;--tw-translate-x:0;--tw-translate-y:0;--tw-rotate:0;--tw-skew-x:0;--tw-skew-y:0;--tw-scale-x:1;--tw-scale-y:1;--tw-pan-x:;--tw-pan-y:;--tw-pinch-zoom:;--tw-scroll-snap-strictness:proximity;--tw-gradient-from-position:;--tw-gradient-via-position:;--tw-gradient-to-position:;--tw-ordinal:;--tw-slashed-zero:;--tw-numeric-figure:;--tw-numeric-spacing:;--tw-numeric-fraction:;--tw-ring-inset:;--tw-ring-offset-width:0px;--tw-ring-offset-color:#fff;--tw-ring-color:rgb(59 130 246/0.5);--tw-ring-offset-shadow:0 0 #0000;--tw-ring-shadow:0 0 #0000;--tw-shadow:0 0 #0000;--tw-shadow-colored:0 0 #0000;--tw-blur:;--tw-brightness:;--tw-contrast:;--tw-grayscale:;--tw-hue-rotate:;--tw-invert:;--tw-saturate:;--tw-sepia:;--tw-drop-shadow:;--tw-backdrop-blur:;--tw-backdrop-brightness:;--tw-backdrop-contrast:;--tw-backdrop-grayscale:;--tw-backdrop-hue-rotate:;--tw-backdrop-invert:;--tw-backdrop-opacity:;--tw-backdrop-saturate:;--tw-backdrop-sepia:;--tw-contain-size:;--tw-contain-layout:;--tw-contain-paint:;--tw-contain-style:;}
/*! tailwindcss v3.4.17 | MIT License | https://tailwindcss.com*/
:host,html{line-height:1.5;-webkit-text-size-adjust:100%;font-family:ui-sans-serif,system-ui,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji;font-feature-settings:normal;font-variation-settings:normal;-moz-tab-size:4;-o-tab-size:4;tab-size:4;-webkit-tap-highlight-color:transparent;
}
body{
    background-color:#0f172a;
    color:#fff;
    font-family:Signika,ui-sans-serif,system-ui,sans-serif;line-height:inherit;margin:0;
}
hr{border-top-width:1px;color:inherit;height:0;
}
abbr:where([title]){-webkit-text-decoration:underline dotted;text-decoration:underline dotted;
}
h1,h2,h3,h4,h5,h6{font-size:inherit;font-weight:inherit;
}
a{color:inherit;text-decoration:inherit;
}
b,strong{font-weight:bolder;
}
code,kbd,pre,samp{font-family:ui-monospace,SFMono-Regular,Menlo,Monaco,Consolas,Liberation Mono,Courier New,monospace;font-feature-settings:normal;font-size:1em;font-variation-settings:normal;
}
small{font-size:80%;
}
sub,sup{font-size:75%;line-height:0;position:relative;vertical-align:baseline;
}
sub{
  bottom:-0.25em;bottom:-.25em;
}
sup{
  top:-0.5em;top:-.5em;
}
table{border-collapse:collapse;border-color:inherit;text-indent:0;
}
button,input,optgroup,select,textarea{color:inherit;font-family:inherit;font-feature-settings:inherit;font-size:100%;font-variation-settings:inherit;font-weight:inherit;letter-spacing:inherit;line-height:inherit;margin:0;padding:0;
}
button,select{text-transform:none;
}
button,input:where([type=button]),input:where([type=reset]),input:where([type=submit]){-webkit-appearance:button;background-color:transparent;background-image:none;
}
:-moz-focusring{outline:auto;
}
:-moz-ui-invalid{box-shadow:none;
}
progress{vertical-align:baseline;
}
::-webkit-inner-spin-button,::-webkit-outer-spin-button{height:auto;
}
[type=search]{-webkit-appearance:textfield;outline-offset:-2px;
}
::-webkit-search-decoration{-webkit-appearance:none;
}
::-webkit-file-upload-button{-webkit-appearance:button;font:inherit;
}
summary{display:list-item;
}
blockquote,dd,dl,figure,h1,h2,h3,h4,h5,h6,hr,p,pre{margin:0;
}
fieldset{margin:0;
}
menu,ol,ul{list-style:none;margin:0;padding:0;
}
dialog{padding:0;
}
textarea{resize:vertical;
}
input::placeholder,textarea::placeholder{color:#9ca3af;opacity:1;
}
[role=button],button{cursor:pointer;
}
:disabled{cursor:default;
}
audio,canvas,embed,iframe,img,object,svg,video{display:block;vertical-align:middle;
}
img,video{height:auto;max-width:100%;
}
[hidden]:where(:not([hidden=until-found])){display:none;
}
@font-face{
    font-display:swap;
    font-family:Signika;
    font-style:normal;
    font-weight:600;
    src:url(/assets/webfonts/Signika-SemiBold.ttf) format("truetype");
  }
@font-face{
    font-display:swap;
    font-family:League Spartan;
    font-style:normal;
    font-weight:400;
    src:url(/assets/webfonts/LeagueSpartan-Regular.ttf) format("truetype");
  }
*{
    font-family:Signika,ui-sans-serif,system-ui,sans-serif;
    font-family:Signika,sans-serif;font-weight:300;
  }
html{
    scroll-behavior:smooth;
  }
.\!container{margin-left:auto !important;margin-right:auto !important;padding-left:16px !important;padding-right:16px !important;width:100% !important;}
.container{margin-left:auto;margin-right:auto;padding-left:16px;padding-right:16px;width:100%;}
.gaming-btn{align-items:center;border-radius:.5rem;display:inline-flex;font-weight:600;justify-content:center;padding-bottom:.5rem;padding-left:1rem;padding-right:1rem;padding-top:.5rem;transform:translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));transition-duration:300ms;transition-duration:.3s;transition-property:all;transition-timing-function:cubic-bezier(0.4, 0, 0.2, 1);transition-timing-function:cubic-bezier(.4,0,.2,1);}
.gaming-btn:focus{outline:2px solid transparent;outline-offset:2px;--tw-ring-offset-shadow:var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);--tw-ring-shadow:var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);box-shadow:var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);--tw-ring-offset-width:2px}
.gaming-btn:hover{--tw-scale-x:1.05;--tw-scale-y:1.05;}
.gaming-btn-primary,.gaming-btn:hover{transform:translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));}
.gaming-btn-primary{align-items:center;background-image:linear-gradient(to right, var(--tw-gradient-stops));border-radius:.5rem;display:inline-flex;justify-content:center;padding-bottom:.5rem;padding-left:1rem;padding-right:1rem;padding-top:.5rem;transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));transition-duration:300ms;transition-duration:.3s;transition-property:all;transition-timing-function:cubic-bezier(0.4, 0, 0.2, 1);transition-timing-function:cubic-bezier(.4,0,.2,1);--tw-gradient-from:#5081ff var(--tw-gradient-from-position);--tw-gradient-to:rgba(80,129,255,0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to);--tw-gradient-to:#3463db var(--tw-gradient-to-position);color:rgb(255 255 255 / var(--tw-text-opacity, 1));--tw-shadow:0 10px 15px -3px rgba(0,0,0,.1), 0 4px 6px -4px rgba(0,0,0,.1);--tw-shadow-colored:0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);background-image:linear-gradient(to right,var(--tw-gradient-stops));box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);--tw-text-opacity:1;
    background:linear-gradient(135deg, #5081ff, #3463db);
    background:linear-gradient(135deg, #3b82f6, #1d4ed8);
    border:2px solid rgba(80,129,255,.3);
    border:none;
    border-radius:0.75rem;
    box-shadow:0 10px 25px -5px rgba(59,130,246,.3);color:rgb(255 255 255/var(--tw-text-opacity,1));
    color:#fff;
    font-weight:600;
    letter-spacing:0.025em;
    overflow:hidden;
    padding:0.75rem 1.5rem;
    padding:0.875rem 1.5rem;
    position:relative;
    text-transform:uppercase;
    transition:all 0.3s cubic-bezier(0.4, 0, 0.2, 1);}
.gaming-btn-primary:focus{outline:2px solid transparent;outline-offset:2px;--tw-ring-offset-shadow:var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);--tw-ring-shadow:var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);box-shadow:var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);--tw-ring-offset-width:2px}
.gaming-btn-primary:hover{--tw-scale-x:1.05;--tw-scale-y:1.05;transform:translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));--tw-gradient-from:#3463db var(--tw-gradient-from-position);--tw-gradient-to:rgba(52,99,219,0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to);--tw-gradient-to:#5081ff var(--tw-gradient-to-position);--tw-shadow:0 20px 25px -5px rgba(0,0,0,.1), 0 8px 10px -6px rgba(0,0,0,.1);--tw-shadow-colored:0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);
    background:linear-gradient(135deg, #6366f1, #4f46e5);
    background:linear-gradient(135deg, #1d4ed8, #1e40af);
    border-color:rgba(99,102,241,.5);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
    box-shadow:0 10px 25px rgba(80,129,255,.3),0 0 20px rgba(80,129,255,.2);
    box-shadow:0 20px 40px -10px rgba(59,130,246,.4);
    transform:translateY(-2px);}
.gaming-btn-secondary{align-items:center;background-image:linear-gradient(to right, var(--tw-gradient-stops));border-radius:.5rem;display:inline-flex;font-weight:600;justify-content:center;padding-bottom:.5rem;padding-left:1rem;padding-right:1rem;padding-top:.5rem;transform:translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));transition-duration:300ms;transition-duration:.3s;transition-property:all;transition-timing-function:cubic-bezier(0.4, 0, 0.2, 1);transition-timing-function:cubic-bezier(.4,0,.2,1);--tw-gradient-from:#272450 var(--tw-gradient-from-position);--tw-gradient-to:rgba(39,36,80,0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to);--tw-gradient-to:#9c27b0 var(--tw-gradient-to-position);color:rgb(255 255 255 / var(--tw-text-opacity, 1));--tw-shadow:0 10px 15px -3px rgba(0,0,0,.1), 0 4px 6px -4px rgba(0,0,0,.1);--tw-shadow-colored:0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);background-image:linear-gradient(to right,var(--tw-gradient-stops));box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);--tw-text-opacity:1;color:rgb(255 255 255/var(--tw-text-opacity,1));}
.gaming-btn-secondary:focus{outline:2px solid transparent;outline-offset:2px;--tw-ring-offset-shadow:var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);--tw-ring-shadow:var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);box-shadow:var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);--tw-ring-offset-width:2px}
.gaming-btn-secondary:hover{--tw-scale-x:1.05;--tw-scale-y:1.05;transform:translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));--tw-gradient-from:#9c27b0 var(--tw-gradient-from-position);--tw-gradient-to:rgba(156,39,176,0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to);--tw-gradient-to:#272450 var(--tw-gradient-to-position);--tw-shadow:0 20px 25px -5px rgba(0,0,0,.1), 0 8px 10px -6px rgba(0,0,0,.1);--tw-shadow-colored:0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);}
.gaming-btn-secondary:hover,.gaming-card{box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);}
.gaming-card{background-image:linear-gradient(to bottom right, var(--tw-gradient-stops));border-color:rgba(80,129,255,.3);--tw-gradient-from:rgba(30,41,59,.9) var(--tw-gradient-from-position);--tw-gradient-to:rgba(30,41,59,0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to);--tw-gradient-to:rgba(15,23,42,0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), rgba(15,23,42,.8) var(--tw-gradient-via-position), var(--tw-gradient-to);--tw-gradient-to:rgba(30,41,59,.9) var(--tw-gradient-to-position);--tw-shadow:0 20px 25px -5px rgba(0,0,0,.1), 0 8px 10px -6px rgba(0,0,0,.1);--tw-shadow-colored:0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);--tw-backdrop-blur:blur(4px);-webkit-backdrop-filter:var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);backdrop-filter:var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);border-radius:.75rem;border-width:1px;overflow:hidden;}
.gaming-card,.gaming-image-gallery .gaming-card{position:relative;
  }
.gaming-image-gallery .gaming-card{
    align-items:center;
    display:flex;
    height:auto;
    justify-content:center;
  
    max-height:85vh;
    visibility:visible;
    z-index:1;
  }
.gaming-card-hover{background-image:linear-gradient(to bottom right, var(--tw-gradient-stops));border-color:rgba(80,129,255,.3);--tw-gradient-from:rgba(30,41,59,.9) var(--tw-gradient-from-position);--tw-gradient-to:rgba(30,41,59,0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to);--tw-gradient-to:rgba(15,23,42,0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), rgba(15,23,42,.8) var(--tw-gradient-via-position), var(--tw-gradient-to);--tw-gradient-to:rgba(30,41,59,.9) var(--tw-gradient-to-position);--tw-shadow:0 20px 25px -5px rgba(0,0,0,.1), 0 8px 10px -6px rgba(0,0,0,.1);--tw-shadow-colored:0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);--tw-backdrop-blur:blur(4px);-webkit-backdrop-filter:var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);backdrop-filter:var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);border-radius:.75rem;
  border-radius:12px;border-width:1px;
  overflow:hidden;
  position:relative;transform:translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  transform-origin:center;transition-duration:300ms;transition-duration:.3s;transition-property:all;transition-timing-function:cubic-bezier(0.4, 0, 0.2, 1);transition-timing-function:cubic-bezier(.4,0,.2,1);
  transition:all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  will-change:transform, box-shadow, border-color;}
.gaming-image-gallery .gaming-card-hover{
    align-items:center;
    display:flex;
    height:auto;
    justify-content:center;
  
    max-height:85vh;
    position:relative;
    visibility:visible;
    z-index:1;
  }
.gaming-card-hover:hover{--tw-translate-y:-0.25rem;--tw-scale-x:1.05;--tw-scale-y:1.05;transform:translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));--tw-border-opacity:1;border-color:rgb(80 129 255 / var(--tw-border-opacity, 1));--tw-shadow:0 25px 50px -12px rgba(0,0,0,.25);--tw-shadow-colored:0 25px 50px -12px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);--tw-shadow-color:rgba(80,129,255,.5);--tw-shadow:var(--tw-shadow-colored);}
.gaming-input{background-color:rgba(30,41,59,.5);border-color:rgba(80,129,255,.3);border-radius:0.5rem;border-radius:.5rem;border-width:1px;color:rgb(255 255 255 / var(--tw-text-opacity, 1));padding-bottom:0.75rem;padding-bottom:.75rem;padding-left:1rem;padding-right:1rem;padding-top:0.75rem;padding-top:.75rem;transition-duration:300ms;transition-timing-function:cubic-bezier(0.4, 0, 0.2, 1);width:100%;--tw-text-opacity:1;color:rgb(255 255 255/var(--tw-text-opacity,1));transition-duration:.3s;transition-property:all;transition-timing-function:cubic-bezier(.4,0,.2,1);}
.gaming-input::-moz-placeholder{--tw-placeholder-opacity:1;color:rgb(156 163 175 / var(--tw-placeholder-opacity, 1));}
.gaming-input::placeholder{--tw-placeholder-opacity:1;color:rgb(156 163 175 / var(--tw-placeholder-opacity, 1));}
.gaming-input:focus{--tw-border-opacity:1;border-color:rgb(80 129 255 / var(--tw-border-opacity, 1));outline:2px solid transparent;outline-offset:2px;--tw-ring-offset-shadow:var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);--tw-ring-shadow:var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);box-shadow:var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);--tw-ring-color:rgba(80,129,255,.2)}
.gaming-select{background-color:rgba(30,41,59,.5);border-color:rgba(80,129,255,.3);border-radius:0.5rem;border-radius:.5rem;border-width:1px;color:rgb(255 255 255 / var(--tw-text-opacity, 1));padding-bottom:0.75rem;padding-bottom:.75rem;padding-left:1rem;padding-right:1rem;padding-top:0.75rem;padding-top:.75rem;transition-duration:300ms;transition-timing-function:cubic-bezier(0.4, 0, 0.2, 1);width:100%;--tw-text-opacity:1;-webkit-appearance:none;-moz-appearance:none;appearance:none;
    background-image:url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3E%3Cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3E%3C/svg%3E");background-position:100%;background-repeat:no-repeat;background-size:16px 16px;color:rgb(255 255 255/var(--tw-text-opacity,1));padding-right:2.5rem;transition-duration:.3s;transition-property:all;transition-timing-function:cubic-bezier(.4,0,.2,1);}
.gaming-select::-moz-placeholder{--tw-placeholder-opacity:1;color:rgb(156 163 175 / var(--tw-placeholder-opacity, 1));}
.gaming-select::placeholder{--tw-placeholder-opacity:1;color:rgb(156 163 175 / var(--tw-placeholder-opacity, 1));}
.gaming-select:focus{--tw-border-opacity:1;border-color:rgb(80 129 255 / var(--tw-border-opacity, 1));outline:2px solid transparent;outline-offset:2px;--tw-ring-offset-shadow:var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);--tw-ring-shadow:var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);box-shadow:var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);--tw-ring-color:rgba(80,129,255,.2);
  box-shadow:0 4px 12px rgba(99,102,241,.3);
  transform:translateY(-1px)}
.gaming-nav-item{--tw-text-opacity:1;border-radius:.5rem;color:rgb(209 213 219 / var(--tw-text-opacity, 1));padding-bottom:.5rem;padding-left:1rem;padding-right:1rem;padding-top:.5rem;position:relative;transition-duration:300ms;transition-duration:.3s;transition-property:all;transition-timing-function:cubic-bezier(0.4, 0, 0.2, 1);transition-timing-function:cubic-bezier(.4,0,.2,1);}
.gaming-nav-item:hover{background-color:rgba(80,129,255,.2);color:rgb(255 255 255/var(--tw-text-opacity,1));--tw-text-opacity:1;color:rgb(255 255 255 / var(--tw-text-opacity, 1))}
.gaming-image-gallery .gaming-nav-item{
    align-items:center;
    display:flex;
    height:auto;
    justify-content:center;
  
    max-height:85vh;
    position:relative;
    visibility:visible;
    z-index:1;
  }
.gaming-nav-item.active{background-image:linear-gradient(to right, var(--tw-gradient-stops));--tw-gradient-from:#5081ff var(--tw-gradient-from-position);--tw-gradient-to:rgba(80,129,255,0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to);--tw-gradient-to:#3463db var(--tw-gradient-to-position);color:rgb(255 255 255 / var(--tw-text-opacity, 1));--tw-shadow:0 10px 15px -3px rgba(0,0,0,.1), 0 4px 6px -4px rgba(0,0,0,.1);--tw-shadow-colored:0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);background-image:linear-gradient(to right,var(--tw-gradient-stops));box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);--tw-text-opacity:1;color:rgb(255 255 255/var(--tw-text-opacity,1));}
.gaming-modal{inset:0px;--tw-backdrop-blur:blur(4px);align-items:center;-webkit-backdrop-filter:var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);backdrop-filter:var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
    background-color:rgba(0,0,0,.5);display:flex;inset:0;justify-content:center;padding:1rem;position:fixed;z-index:50;}
.gaming-modal-content{background-image:linear-gradient(to bottom right, var(--tw-gradient-stops));border-color:rgba(80,129,255,.4);max-width:28rem;--tw-gradient-from:#0f172a var(--tw-gradient-from-position);--tw-gradient-to:rgba(15,23,42,0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to);--tw-gradient-to:rgba(30,41,59,0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), #1e293b var(--tw-gradient-via-position), var(--tw-gradient-to);--tw-gradient-to:#0f172a var(--tw-gradient-to-position);--tw-shadow:0 25px 50px -12px rgba(0,0,0,.25);--tw-shadow-colored:0 25px 50px -12px var(--tw-shadow-color);border-radius:1rem;border-width:1px;box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);padding:1.5rem;width:100%;}
.gaming-image-gallery .gaming-modal-content,.gaming-modal-content{position:relative;
  }
.gaming-image-gallery .gaming-modal-content{
    align-items:center;
    display:flex;
    height:auto;
    justify-content:center;
  
    max-height:85vh;
    visibility:visible;
    z-index:1;
  }
.gaming-table{background-color:rgba(30,41,59,.5);border-color:rgba(80,129,255,.3);border-radius:.5rem;border-width:1px;overflow:hidden;width:100%;}
.gaming-table th{border-color:rgba(80,129,255,.3);--tw-bg-opacity:1;background-color:rgb(15 23 42 / var(--tw-bg-opacity, 1)); background-color:#0f172a;
  background:linear-gradient(135deg, rgba(80,129,255,.2), rgba(52,99,219,.2));border-bottom-width:1px;
  border-bottom:2px solid rgba(80,129,255,.3);font-size:.75rem;
  font-size:12px;font-weight:600;
  font-weight:700;
  letter-spacing:0.05em;line-height:1rem;padding-bottom:1rem;padding-left:1.5rem;padding-right:1.5rem;padding-top:1rem;
  padding:16px 12px;text-align:left;
  text-transform:uppercase;}
.gaming-table td,.gaming-table th{--tw-text-opacity:1;color:rgb(209 213 219 / var(--tw-text-opacity, 1));
  color:#ffffffcc;}
.gaming-table td{border-bottom-width:1px;border-color:rgba(80,129,255,.1);
  border-bottom:1px solid rgba(80,129,255,.1);font-size:.875rem;line-height:1.25rem;padding-bottom:1rem;padding-left:1.5rem;padding-right:1.5rem;padding-top:1rem;
  padding:12px;
  transition:all 0.3s ease;}
.gaming-table tr:hover{background-color:rgba(80,129,255,.1);}
.sr-only{height:1px;margin:-1px;overflow:hidden;padding:0;position:absolute;width:1px;clip:rect(0, 0, 0, 0);border-width:0;white-space:nowrap;}
.pointer-events-none{pointer-events:none;}
.pointer-events-auto{pointer-events:auto;}
.\!visible{visibility:visible !important;}
.visible{visibility:visible;}
.invisible{visibility:hidden;}
.collapse{visibility:collapse;}
.static{position:static;}
.fixed{position:fixed;}
.absolute{position:absolute;}
.relative{position:relative;}
.sticky{position:sticky;}
.inset-0{inset:0px;inset:0;}
.inset-1{inset:0.25rem;}
.inset-auto{inset:auto;}
.inset-x-0{left:0px;right:0px;}
.inset-y-0{bottom:0px;top:0px;}
.-bottom-1{bottom:-0.25rem;}
.-bottom-12{bottom:-3rem;}
.-bottom-2{bottom:-0.5rem;}
.-bottom-5{bottom:-1.25rem;}
.-end-6{inset-inline-end:-1.5rem;}
.-left-2{left:-0.5rem;}
.-right-1{right:-0.25rem;}
.-right-2{right:-0.5rem;}
.-top-0{top:-0px;}
.-top-1{top:-0.25rem;}
.-top-2{top:-0.5rem;}
.-top-3{top:-0.75rem;}
.-top-8{top:-2rem;}
.bottom-0{bottom:0px;bottom:0;}
.bottom-1{bottom:0.25rem;}
.bottom-10{bottom:2.5rem;}
.bottom-12{bottom:3rem;}
.bottom-14{bottom:3.5rem;}
.bottom-2{bottom:0.5rem;}
.bottom-20{bottom:5rem;}
.bottom-4{bottom:1rem;}
.bottom-5{bottom:1.25rem;}
.bottom-6{bottom:1.5rem;}
.bottom-8{bottom:2rem;}
.bottom-\[12px\]{bottom:12px;}
.bottom-full{bottom:100%;}
.end-0{inset-inline-end:0px;inset-inline-end:0;}
.end-1{inset-inline-end:0.25rem;}
.end-10{inset-inline-end:2.5rem;}
.end-5{inset-inline-end:1.25rem;}
.end-6{inset-inline-end:1.5rem;}
.end-auto{inset-inline-end:auto;}
.left-0{left:0px;left:0;}
.left-1{left:0.25rem;}
.left-1\/2{left:50%;}
.left-1\/4{left:25%;}
.left-10{left:2.5rem;}
.left-2{left:0.5rem;}
.left-20{left:5rem;}
.left-24{left:6rem;}
.left-3{left:0.75rem;}
.left-4{left:1rem;}
.left-5{left:1.25rem;}
.left-6{left:1.5rem;}
.left-8{left:2rem;}
.left-\[2px\]{left:2px;}
.left-\[50\%\]{left:50%;}
.right-0{right:0px;right:0;}
.right-1{right:0.25rem;}
.right-1\/3{right:33.333333%;}
.right-1\/4{right:25%;}
.right-10{right:2.5rem;}
.right-14{right:3.5rem;}
.right-2{right:0.5rem;}
.right-20{right:5rem;}
.right-3{right:0.75rem;right:.75rem;}
.right-4{right:1rem;}
.right-6{right:1.5rem;}
.right-8{right:2rem;}
.start-0{inset-inline-start:0px;}
.start-1{inset-inline-start:0.25rem;}
.start-4{inset-inline-start:1rem;}
.top-0{top:0px;top:0;}
.top-1{top:0.25rem;}
.top-1\/2{top:50%;}
.top-1\/3{top:33.333333%;}
.top-1\/4{top:25%;}
.top-10{top:2.5rem;}
.top-11{top:2.75rem;}
.top-2{top:0.5rem;}
.top-20{top:5rem;}
.top-3{top:0.75rem;}
.top-3\/4{top:75%;}
.top-4{top:1rem;}
.top-5{top:1.25rem;}
.top-6{top:1.5rem;}
.top-\[50\%\]{top:50%;}
.top-full{top:100%;}
.isolate{isolation:isolate;}
.-z-10{z-index:-10;}
.-z-20{z-index:-20;}
.z-0{z-index:0;}
.z-10{z-index:10;}
.z-20{z-index:20;}
.z-30{z-index:30;}
.z-40{z-index:40;}
.z-50{z-index:50;}
.z-\[1000000\]{z-index:1000000;}
.z-\[1000001\]{z-index:1000001;}
.z-\[10000\]{z-index:10000;}
.z-\[1\]{z-index:1;}
.z-\[2\]{z-index:2;}
.z-\[3\]{z-index:3;}
.z-\[5\]{z-index:5;}
.z-\[60\]{z-index:60;}
.z-\[999997\]{z-index:999997;}
.z-\[999998\]{z-index:999998;}
.z-\[999999\]{z-index:999999;}
.z-\[9999\]{z-index:9999;}
.order-1{order:1;}
.order-2{order:2;}
.order-last{order:9999;}
.col-span-1{grid-column:span 1 / span 1;}
.col-span-2{grid-column:span 2 / span 2;}
.col-span-3{grid-column:span 3 / span 3;grid-column:span 3 / span 3;}
.col-span-4{grid-column:span 4 / span 4;}
.col-span-5{grid-column:span 5 / span 5;}
.col-span-6{grid-column:span 6 / span 6;grid-column:span 6 / span 6;}
.col-span-9{grid-column:span 9 / span 9;}
.col-span-full{grid-column:1 /  -1;}
.col-start-2{grid-column-start:2;}
.row-span-2{grid-row:span 2 / span 2;}
.row-span-3{grid-row:span 3 / span 3;}
.row-start-1{grid-row-start:1;}
.row-start-3{grid-row-start:3;}
.-m-2{margin:-0.5rem;}
.-m-5{margin:-1.25rem;}
.m-0{margin:0px;}
.m-1{margin:0.25rem;}
.m-6{margin:1.5rem;}
.m-8{margin:2rem;}
.m-auto{margin:auto;}
.-mx-1{margin-left:-0.25rem;margin-right:-0.25rem;}
.-mx-2{margin-left:-0.5rem;margin-right:-0.5rem;}
.-mx-4{margin-left:-1rem;margin-right:-1rem;}
.-mx-5{margin-left:-1.25rem;margin-right:-1.25rem;}
.-mx-6{margin-left:-1.5rem;margin-right:-1.5rem;}
.mx-0{margin-left:0px;margin-right:0px;}
.mx-1{margin-left:0.25rem;margin-right:0.25rem;}
.mx-10{margin-left:2.5rem;margin-right:2.5rem;}
.mx-2{margin-left:0.5rem;margin-right:0.5rem;}
.mx-3{margin-left:0.75rem;margin-right:0.75rem;}
.mx-4{margin-left:1rem;margin-right:1rem;}
.mx-\[24px\]{margin-left:24px;margin-right:24px;}
.mx-auto{margin-left:auto;margin-right:auto;}
.my-0{margin-bottom:0px;margin-top:0px;}
.my-1{margin-bottom:0.25rem;margin-top:0.25rem;}
.my-10{margin-bottom:2.5rem;margin-top:2.5rem;}
.my-2{margin-bottom:0.5rem;margin-top:0.5rem;}
.my-3{margin-bottom:0.75rem;margin-bottom:.75rem;margin-top:0.75rem;margin-top:.75rem;}
.my-32{margin-bottom:8rem;margin-top:8rem;}
.my-4{margin-bottom:1rem;margin-top:1rem;}
.my-5{margin-bottom:1.25rem;margin-top:1.25rem;}
.my-6{margin-bottom:1.5rem;margin-top:1.5rem;}
.my-7{margin-bottom:1.75rem;margin-top:1.75rem;}
.my-8{margin-bottom:2rem;margin-top:2rem;}
.my-\[36px\]{margin-bottom:36px;margin-top:36px;}
.my-auto{margin-bottom:auto;margin-top:auto;}
.\!mb-0{margin-bottom:0px !important;margin-bottom:0!important;}
.\!mb-3{margin-bottom:0.75rem !important;margin-bottom:.75rem!important;}
.\!mt-0{margin-top:0px !important;}
.\!mt-2{margin-top:0.5rem !important;}
.-ml-2{margin-left:-0.5rem;}
.-ml-\[16px\]{margin-left:-16px;}
.-mr-3{margin-right:-0.75rem;}
.-mr-\[16px\]{margin-right:-16px;}
.-ms-28{margin-inline-start:-7rem;}
.-ms-32{margin-inline-start:-8rem;}
.-ms-60{margin-inline-start:-15rem;}
.-mt-10{margin-top:-2.5rem;}
.-mt-12{margin-top:-3rem;}
.-mt-14{margin-top:-3.5rem;}
.-mt-2{margin-top:-0.5rem;}
.-mt-20{margin-top:-5rem;}
.-mt-3{margin-top:-0.75rem;margin-top:-.75rem;}
.-mt-7{margin-top:-1.75rem;}
.-mt-8{margin-top:-2rem;}
.mb-0{margin-bottom:0px;}
.mb-1{margin-bottom:0.25rem;margin-bottom:.25rem;}
.mb-10{margin-bottom:2.5rem;}
.mb-12{margin-bottom:3rem;}
.mb-16{margin-bottom:4rem;}
.mb-2{margin-bottom:0.5rem;}
.mb-20{margin-bottom:5rem;}
.mb-24{margin-bottom:6rem;}
.mb-3{margin-bottom:0.75rem;margin-bottom:.75rem;}
.mb-32{margin-bottom:8rem;}
.mb-4{margin-bottom:1rem;}
.mb-5{margin-bottom:1.25rem;}
.mb-6{margin-bottom:1.5rem;}
.mb-7{margin-bottom:1.75rem;}
.mb-8{margin-bottom:2rem;}
.mb-9{margin-bottom:2.25rem;}
.mb-\[14px\]{margin-bottom:14px;}
.mb-px{margin-bottom:1px;}
.me-1{margin-inline-end:0.25rem;}
.me-2{margin-inline-end:0.5rem;}
.me-3{margin-inline-end:0.75rem;}
.me-4{margin-inline-end:1rem;}
.me-auto{margin-inline-end:auto;}
.ml-0{margin-left:0px;}
.ml-0\.5{margin-left:0.125rem;}
.ml-1{margin-left:0.25rem;margin-left:.25rem;}
.ml-2{margin-left:0.5rem;margin-left:.5rem;}
.ml-3{margin-left:0.75rem;}
.ml-4{margin-left:1rem;}
.ml-5{margin-left:1.25rem;}
.ml-6{margin-left:1.5rem;}
.ml-auto{margin-left:auto;}
.mr-0{margin-right:0px;}
.mr-1{margin-right:0.25rem;margin-right:.25rem;}
.mr-1\.5{margin-right:0.375rem;margin-right:.375rem;}
.mr-2{margin-right:0.5rem;}
.mr-3{margin-right:0.75rem;}
.mr-4{margin-right:1rem;}
.mr-5{margin-right:1.25rem;}
.ms-1{margin-inline-start:0.25rem;}
.ms-10{margin-inline-start:2.5rem;}
.ms-2{margin-inline-start:0.5rem;}
.ms-3{margin-inline-start:0.75rem;}
.ms-5{margin-inline-start:1.25rem;}
.ms-auto{margin-inline-start:auto;}
.mt-0{margin-top:0px;}
.mt-1{margin-top:0.25rem;margin-top:.25rem;}
.mt-1\.5{margin-top:0.375rem;}
.mt-10{margin-top:2.5rem;}
.mt-12{margin-top:3rem;}
.mt-16{margin-top:4rem;}
.mt-2{margin-top:0.5rem;margin-top:.5rem;}
.mt-20{margin-top:5rem;}
.mt-24{margin-top:6rem;}
.mt-3{margin-top:0.75rem;margin-top:.75rem;}
.mt-4{margin-top:1rem;}
.mt-5{margin-top:1.25rem;}
.mt-6{margin-top:1.5rem;}
.mt-7{margin-top:1.75rem;}
.mt-8{margin-top:2rem;}
.mt-\[16px\]{margin-top:16px;}
.mt-\[24px\]{margin-top:24px;}
.mt-\[4px\]{margin-top:4px;}
.mt-\[8px\]{margin-top:8px;}
.mt-auto{margin-top:auto;}
.box-border{box-sizing:border-box;}
.line-clamp-1{-webkit-line-clamp:1;}
.line-clamp-1,.line-clamp-2{display:-webkit-box;overflow:hidden;-webkit-box-orient:vertical;}
.line-clamp-2{-webkit-line-clamp:2;}
.line-clamp-3{-webkit-line-clamp:3;}
.line-clamp-3,.line-clamp-4{display:-webkit-box;overflow:hidden;-webkit-box-orient:vertical;}
.line-clamp-4{-webkit-line-clamp:4;}
.block{display:block;}
.inline-block{display:inline-block;}
.inline{display:inline;}
.flex{display:flex;}
.inline-flex{display:inline-flex;}
.table{display:table;}
.table-caption{display:table-caption;}
.table-cell{display:table-cell;}
.\!grid{display:grid!important;}
.grid{display:grid;}
.contents{display:contents;}
.list-item{display:list-item;}
.\!hidden{display:none!important;}
.hidden{display:none;}
.aspect-\[375\/156\]{aspect-ratio:375/156;}
.aspect-\[98\/131\]{aspect-ratio:98/131;}
.aspect-square{aspect-ratio:1 / 1;aspect-ratio:1/1;}
.size-1{height:0.25rem;width:0.25rem;}
.size-12{height:3rem;width:3rem;}
.size-3{height:0.75rem;width:0.75rem;}
.size-4{height:1rem;width:1rem;}
.size-5{height:1.25rem;width:1.25rem;}
.size-8{height:2rem;width:2rem;}
.size-9{height:2.25rem;width:2.25rem;}
.size-full{height:100%;width:100%;}
.\!h-3{height:0.75rem !important;height:.75rem!important;}
.\!h-6{height:1.5rem!important;}
.\!h-9{height:2.25rem!important;}
.h-0{height:0px;height:0;}
.h-0\.5{height:0.125rem;}
.h-1{height:0.25rem;height:.25rem;}
.h-10{height:2.5rem;}
.h-11{height:2.75rem;}
.h-12{height:3rem;}
.h-14{height:3.5rem;}
.h-16{height:4rem;}
.h-18{height:4.5rem;}
.h-2{height:0.5rem;}
.h-20{height:5rem;}
.h-24{height:6rem;}
.h-3{height:0.75rem;}
.h-3\.5{height:0.875rem;}
.h-32{height:8rem;}
.h-36{height:9rem;}
.h-4{height:1rem;}
.h-40{height:10rem;}
.h-44{height:11rem;}
.h-48{height:12rem;}
.h-5{height:1.25rem;}
.h-6{height:1.5rem;}
.h-7{height:1.75rem;}
.h-8{height:2rem;}
.h-80{height:20rem;}
.h-9{height:2.25rem;}
.h-96{height:24rem;}
.h-\[12px\]{height:12px;}
.h-\[16px\]{height:16px;}
.h-\[200px\]{height:200px;}
.h-\[24px\]{height:24px;}
.h-\[32px\]{height:32px;}
.h-\[40px\]{height:40px;}
.h-\[4px\]{height:4px;}
.h-\[56px\]{height:56px;}
.h-\[64px\]{height:64px;}
.h-\[calc\(100\%-72px\)\]{height:calc(100% - 72px);}
.h-auto{height:auto;}
.h-fit{height:-moz-fit-content;height:fit-content;}
.h-full{height:100%;}
.h-max{height:-moz-max-content;height:max-content;}
.h-px{height:1px;}
.h-screen{height:100vh;}
.max-h-20{max-height:5rem;}
.max-h-40{max-height:10rem;}
.max-h-52{max-height:13rem;}
.max-h-56{max-height:14rem;}
.max-h-6{max-height:1.5rem;}
.max-h-60{max-height:15rem;}
.max-h-80{max-height:20rem;}
.max-h-96{max-height:24rem;}
.max-h-\[44px\]{max-height:44px;}
.max-h-\[95px\]{max-height:95px;}
.min-h-0{min-height:0px;}
.min-h-12{min-height:3rem;}
.min-h-16{min-height:4rem;}
.min-h-28{min-height:7rem;}
.min-h-52{min-height:13rem;}
.min-h-6{min-height:1.5rem;}
.min-h-60{min-height:15rem;}
.min-h-9{min-height:2.25rem;}
.min-h-96{min-height:24rem;}
.min-h-\[150px\]{min-height:150px;}
.min-h-\[2\.5rem\]{min-height:2.5rem;}
.min-h-\[320px\]{min-height:320px;}
.min-h-\[380px\]{min-height:380px;}
.min-h-\[65px\]{min-height:65px;}
.min-h-full{min-height:100%;}
.min-h-home{
    min-height:calc(100vh - 80px);}
.min-h-screen{min-height:100vh;}
.min-h-screen-80{
    min-height:80vh;}
.\!w-1{width:0.25rem !important;}
.\!w-2{width:0.5rem !important;}
.\!w-3{width:0.75rem !important;}
.w-0{width:0px;width:0;}
.w-1{width:0.25rem;}
.w-1\/2{width:50%;}
.w-1\/3{width:33.333333%;}
.w-1\/6{width:16.666667%;}
.w-10{width:2.5rem;}
.w-11{width:2.75rem;}
.w-12{width:3rem;}
.w-14{width:3.5rem;}
.w-16{width:4rem;}
.w-18{width:4.5rem;}
.w-2{width:0.5rem;}
.w-2\/3{width:66.666667%;}
.w-2\/5{width:40%;}
.w-20{width:5rem;}
.w-24{width:6rem;}
.w-28{width:7rem;}
.w-3{width:0.75rem;}
.w-3\.5{width:0.875rem;}
.w-3\/4{width:75%;}
.w-32{width:8rem;}
.w-4{width:1rem;}
.w-4\/5{width:80%;}
.w-44{width:11rem;}
.w-48{width:12rem;}
.w-5{width:1.25rem;}
.w-52{width:13rem;}
.w-56{width:14rem;}
.w-6{width:1.5rem;}
.w-64{width:16rem;}
.w-7{width:1.75rem;}
.w-72{width:18rem;}
.w-8{width:2rem;}
.w-80{width:20rem;}
.w-9{width:2.25rem;}
.w-96{width:24rem;}
.w-\[120px\]{width:120px;}
.w-\[12px\]{width:12px;}
.w-\[16px\]{width:16px;}
.w-\[18px\]{width:18px;}
.w-\[1px\]{width:1px;}
.w-\[200px\]{width:200px;}
.w-\[230px\]{width:230px;}
.w-\[24px\]{width:24px;}
.w-\[28px\]{width:28px;}
.w-\[32px\]{width:32px;}
.w-\[36px\]{width:36px;}
.w-\[371px\]{width:371px;}
.w-\[40px\]{width:40px;}
.w-\[80\%\]{width:80%;}
.w-\[85\%\]{width:85%;}
.w-\[93\.333333\%\]{width:93.333333%;}
.w-\[98px\]{width:98px;}
.w-auto{width:auto;}
.w-fit{width:-moz-fit-content;width:fit-content;}
.w-full{width:100%;}
.w-max{width:-moz-max-content;width:max-content;}
.w-screen{width:100vw;}
.min-w-20{min-width:5rem;}
.min-w-\[12rem\]{min-width:12rem;}
.min-w-\[20rem\]{min-width:20rem;}
.min-w-full{min-width:100%;}
.min-w-max{min-width:-moz-max-content;min-width:max-content;}
.max-w-12{max-width:3rem;}
.max-w-2xl{max-width:42rem;}
.max-w-3xl{max-width:48rem;}
.max-w-4xl{max-width:56rem;}
.max-w-52{max-width:13rem;}
.max-w-5xl{max-width:64rem;}
.max-w-7xl{max-width:80rem;}
.max-w-fit{max-width:-moz-fit-content;max-width:fit-content;}
.max-w-full{max-width:100%;}
.max-w-lg{max-width:32rem;}
.max-w-md{max-width:28rem;}
.max-w-none{max-width:none;}
.max-w-sm{max-width:24rem;}
.max-w-xl{max-width:36rem;}
.max-w-xs{max-width:20rem;}
.flex-1{flex:1 1 0%;}
.flex-auto{flex:1 1 auto;}
.flex-initial{flex:0 1 auto;}
.flex-none{flex:none;}
.flex-shrink{flex-shrink:1;}
.flex-shrink-0{flex-shrink:0;}
.shrink{flex-shrink:1;}
.shrink-0{flex-shrink:0;}
.flex-grow{flex-grow:1;}
.flex-grow-0{flex-grow:0;}
.grow{flex-grow:1;}
.table-auto{table-layout:auto;}
.table-fixed{table-layout:fixed;}
.caption-bottom{caption-side:bottom;}
.border-collapse{border-collapse:collapse;}
.origin-bottom-right{transform-origin:bottom right;}
.origin-center{transform-origin:center;}
.origin-top-right{transform-origin:top right;}
.-translate-x-0{--tw-translate-x:-0px;}
.-translate-x-0,.-translate-x-1{transform:translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));}
.-translate-x-1{--tw-translate-x:-0.25rem;}
.-translate-x-1\/2,.-translate-x-\[50\%\]{--tw-translate-x:-50%;}
.-translate-x-1\/2,.-translate-x-\[50\%\],.-translate-x-full{transform:translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));}
.-translate-x-full{--tw-translate-x:-100%;}
.-translate-y-0{--tw-translate-y:-0px;}
.-translate-y-0,.-translate-y-1{transform:translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));}
.-translate-y-1{--tw-translate-y:-0.25rem;}
.-translate-y-1\/2{--tw-translate-y:-50%;}
.-translate-y-1\/2,.-translate-y-2{transform:translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));}
.-translate-y-2{--tw-translate-y:-0.5rem;}
.-translate-y-8{--tw-translate-y:-2rem;}
.-translate-y-8,.-translate-y-\[50\%\]{transform:translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));}
.-translate-y-\[50\%\]{--tw-translate-y:-50%;}
.-translate-y-full{--tw-translate-y:-100%;}
.-translate-y-full,.translate-x-0{transform:translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));}
.translate-x-0{--tw-translate-x:0px;}
.translate-x-1{--tw-translate-x:0.25rem;}
.translate-x-1,.translate-x-2{transform:translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));}
.translate-x-2{--tw-translate-x:0.5rem;}
.translate-x-full{--tw-translate-x:100%;}
.translate-x-full,.translate-y-0{transform:translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));}
.translate-y-0{--tw-translate-y:0px;}
.translate-y-40{--tw-translate-y:10rem;}
.translate-y-40,.translate-y-\[-10px\]{transform:translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));}
.translate-y-\[-10px\]{--tw-translate-y:-10px;}
.translate-y-full{--tw-translate-y:100%;}
.-rotate-12,.translate-y-full{transform:translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));}
.-rotate-12{--tw-rotate:-12deg;}
.-rotate-45{--tw-rotate:-45deg;}
.-rotate-45,.rotate-0{transform:translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));}
.rotate-0{--tw-rotate:0deg;}
.rotate-12{--tw-rotate:12deg;}
.rotate-12,.rotate-180{transform:translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));}
.rotate-180{--tw-rotate:180deg;}
.rotate-45{--tw-rotate:45deg;}
.rotate-45,.rotate-90{transform:translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));}
.rotate-90{--tw-rotate:90deg;}
.-skew-y-1{--tw-skew-y:-1deg;}
.-skew-y-1,.scale-0{transform:translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));}
.scale-0{--tw-scale-x:0;--tw-scale-y:0;}
.scale-100{--tw-scale-x:1;--tw-scale-y:1;}
.scale-100,.scale-105{transform:translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));}
.scale-105{--tw-scale-x:1.05;--tw-scale-y:1.05;}
.scale-110{--tw-scale-x:1.1;--tw-scale-y:1.1;}
.scale-110,.scale-125{transform:translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));}
.scale-125{--tw-scale-x:1.25;--tw-scale-y:1.25;}
.scale-90{--tw-scale-x:.9;--tw-scale-y:.9;}
.scale-90,.scale-95{transform:translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));}
.scale-95{--tw-scale-x:.95;--tw-scale-y:.95;}
.scale-x-0{--tw-scale-x:0;}
.scale-x-0,.scale-x-100{transform:translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));}
.scale-x-100{--tw-scale-x:1;}
.transform{transform:translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));}
@keyframes bounce{0%,to{animation-timing-function:cubic-bezier(0.8,0,1,1);transform:translateY(-25%);}50%{animation-timing-function:cubic-bezier(0,0,0.2,1);transform:none;}}
.animate-bounce{animation:bounce 1s infinite;}
.animate-float{
    animation:float 3s ease-in-out infinite;}
.animate-glow{
    animation:glow 2s ease-in-out infinite;}
.animate-ping{animation:ping 1s cubic-bezier(0, 0, 0.2, 1) infinite;}
.animate-pulse{animation:pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;animation:pulse 2s cubic-bezier(.4,0,.6,1) infinite;}
.animate-pulse-glow{
    animation:pulse-glow 3s ease-in-out infinite;}
.animate-rotate{animation:rotate 8s linear infinite;}
.animate-shimmer{
  animation:shimmer 2.5s linear infinite;
    animation:shimmer 2s infinite;}
.animate-spin{animation:spin 1s linear infinite;}
.\!cursor-not-allowed{cursor:not-allowed!important;}
.cursor-auto{cursor:auto;}
.cursor-default{cursor:default;}
.cursor-grab{cursor:grab;}
.cursor-move{cursor:move;}
.cursor-not-allowed{cursor:not-allowed;}
.cursor-pointer{cursor:pointer;}
.cursor-text{cursor:text;}
.select-none{
  -webkit-user-select:none;
     -moz-user-select:none;
          user-select:none;}
.select-all{
  -webkit-user-select:all;
     -moz-user-select:all;
          user-select:all;}
.resize-none{resize:none;}
.resize{resize:both;}
.snap-x{scroll-snap-type:x var(--tw-scroll-snap-strictness);}
.snap-mandatory{--tw-scroll-snap-strictness:mandatory;}
.snap-start{scroll-snap-align:start;}
.snap-center{scroll-snap-align:center;}
.list-inside{list-style-position:inside;}
.list-decimal{list-style-type:decimal;}
.list-disc{list-style-type:disc;}
.appearance-none{-webkit-appearance:none;-moz-appearance:none;appearance:none;}
.appearance-auto{-webkit-appearance:auto;-moz-appearance:auto;appearance:auto;}
.grid-flow-row{grid-auto-flow:row;}
.grid-cols-1{grid-template-columns:repeat(1, minmax(0, 1fr));}
.grid-cols-12{grid-template-columns:repeat(12, minmax(0, 1fr));}
.grid-cols-2{grid-template-columns:repeat(2, minmax(0, 1fr));grid-template-columns:repeat(2,minmax(0,1fr));}
.grid-cols-3{grid-template-columns:repeat(3, minmax(0, 1fr));grid-template-columns:repeat(3,minmax(0,1fr));}
.grid-cols-4{grid-template-columns:repeat(4, minmax(0, 1fr));grid-template-columns:repeat(4,minmax(0,1fr));}
.grid-cols-5{grid-template-columns:repeat(5, minmax(0, 1fr));}
.grid-cols-6{grid-template-columns:repeat(6, minmax(0, 1fr));grid-template-columns:repeat(6,minmax(0,1fr));}
.grid-cols-7{grid-template-columns:repeat(7, minmax(0, 1fr));grid-template-columns:repeat(7,minmax(0,1fr));}
.grid-cols-8{grid-template-columns:repeat(8, minmax(0, 1fr));}
.grid-rows-1{grid-template-rows:repeat(1, minmax(0, 1fr));}
.grid-rows-4{grid-template-rows:repeat(4, minmax(0, 1fr));grid-template-rows:repeat(4,minmax(0,1fr));}
.grid-rows-6{grid-template-rows:repeat(6, minmax(0, 1fr));grid-template-rows:repeat(6,minmax(0,1fr));}
.flex-row{flex-direction:row;}
.flex-row-reverse{flex-direction:row-reverse;}
.flex-col{flex-direction:column;}
.flex-col-reverse{flex-direction:column-reverse;}
.flex-wrap{flex-wrap:wrap;}
.flex-wrap-reverse{flex-wrap:wrap-reverse;}
.\!flex-nowrap{flex-wrap:nowrap!important;}
.flex-nowrap{flex-wrap:nowrap;}
.place-content-center{place-content:center;}
.place-items-center{place-items:center;}
.content-center{align-content:center;}
.content-between{align-content:space-between;}
.items-start{align-items:flex-start;}
.items-end{align-items:flex-end;}
.\!items-center{align-items:center !important;}
.items-center{align-items:center;}
.items-baseline{align-items:baseline;}
.items-stretch{align-items:stretch;}
.justify-start{justify-content:flex-start;}
.justify-end{justify-content:flex-end;}
.justify-center{justify-content:center;}
.justify-between{justify-content:space-between;}
.justify-around{justify-content:space-around;}
.justify-evenly{justify-content:space-evenly;}
.justify-items-center{justify-items:center;}
.gap-0{gap:0px;}
.gap-1{gap:0.25rem;gap:.25rem;}
.gap-1\.5{gap:0.375rem;}
.gap-10{gap:2.5rem;}
.gap-12{gap:3rem;}
.gap-14{gap:3.5rem;}
.gap-2{gap:0.5rem;gap:.5rem;}
.gap-20{gap:5rem;}
.gap-3{gap:0.75rem;gap:.75rem;}
.gap-4{gap:1rem;}
.gap-5{gap:1.25rem;}
.gap-6{gap:1.5rem;}
.gap-7{gap:1.75rem;}
.gap-8{gap:2rem;}
.gap-\[12px\]{gap:12px;}
.gap-\[16px\]{gap:16px;}
.gap-\[24px\]{gap:24px;}
.gap-\[4px\]{gap:4px;}
.gap-\[8px\]{gap:8px;}
.gap-x-12{
  -moz-column-gap:3rem;
       column-gap:3rem;}
.gap-x-2{column-gap:0.5rem;-moz-column-gap:.5rem;column-gap:.5rem;}
.gap-x-2\.5{
  -moz-column-gap:0.625rem;
       column-gap:0.625rem;}
.gap-x-3{column-gap:0.75rem;-moz-column-gap:.75rem;column-gap:.75rem;}
.gap-x-3\.5{
  -moz-column-gap:0.875rem;
       column-gap:0.875rem;}
.gap-x-4{
  -moz-column-gap:1rem;
       column-gap:1rem;}
.gap-x-5{
  -moz-column-gap:1.25rem;
       column-gap:1.25rem;}
.gap-x-6{-moz-column-gap:1.5rem;column-gap:1.5rem;}
.gap-x-\[24px\]{-moz-column-gap:24px;column-gap:24px;}
.gap-y-1{row-gap:0.25rem;}
.gap-y-2{row-gap:0.5rem;}
.gap-y-3{row-gap:0.75rem;row-gap:.75rem;}
.gap-y-4{row-gap:1rem;}
.-space-x-2>:not([hidden])~:not([hidden]){--tw-space-x-reverse:0;margin-left:calc(-0.5rem*(1 - var(--tw-space-x-reverse)));margin-right:calc(-0.5rem*var(--tw-space-x-reverse));}
.-space-x-3>:not([hidden])~:not([hidden]){--tw-space-x-reverse:0;margin-left:calc(-0.75rem*(1 - var(--tw-space-x-reverse)));margin-right:calc(-0.75rem*var(--tw-space-x-reverse));}
.-space-x-4>:not([hidden])~:not([hidden]){--tw-space-x-reverse:0;margin-left:calc(-1rem*(1 - var(--tw-space-x-reverse)));margin-right:calc(-1rem*var(--tw-space-x-reverse));}
.space-x-1>:not([hidden])~:not([hidden]){margin-left:calc(0.25rem*(1 - var(--tw-space-x-reverse)));margin-right:calc(0.25rem*var(--tw-space-x-reverse));--tw-space-x-reverse:0;margin-left:calc(0.25rem*(1 - var(--tw-space-x-reverse)));margin-right:calc(0.25rem*var(--tw-space-x-reverse));}
.space-x-2>:not([hidden])~:not([hidden]){--tw-space-x-reverse:0;margin-left:calc(0.5rem*(1 - var(--tw-space-x-reverse)));margin-right:calc(0.5rem*var(--tw-space-x-reverse));}
.space-x-3>:not([hidden])~:not([hidden]){--tw-space-x-reverse:0;margin-left:calc(0.75rem*(1 - var(--tw-space-x-reverse)));margin-right:calc(0.75rem*var(--tw-space-x-reverse));}
.space-x-4>:not([hidden])~:not([hidden]){--tw-space-x-reverse:0;margin-left:calc(1rem*(1 - var(--tw-space-x-reverse)));margin-right:calc(1rem*var(--tw-space-x-reverse));}
.space-y-0>:not([hidden])~:not([hidden]){--tw-space-y-reverse:0;margin-bottom:calc(0px*var(--tw-space-y-reverse));margin-top:calc(0px*(1 - var(--tw-space-y-reverse)));}
.space-y-1>:not([hidden])~:not([hidden]){margin-bottom:calc(0.25rem*var(--tw-space-y-reverse));margin-top:calc(0.25rem*(1 - var(--tw-space-y-reverse)));--tw-space-y-reverse:0;margin-bottom:calc(0.25rem*var(--tw-space-y-reverse));margin-top:calc(0.25rem*(1 - var(--tw-space-y-reverse)));}
.space-y-14>:not([hidden])~:not([hidden]){--tw-space-y-reverse:0;margin-bottom:calc(3.5rem*var(--tw-space-y-reverse));margin-top:calc(3.5rem*(1 - var(--tw-space-y-reverse)));}
.space-y-2>:not([hidden])~:not([hidden]){margin-bottom:calc(0.5rem*var(--tw-space-y-reverse));margin-top:calc(0.5rem*(1 - var(--tw-space-y-reverse)));--tw-space-y-reverse:0;margin-bottom:calc(0.5rem*var(--tw-space-y-reverse));margin-top:calc(0.5rem*(1 - var(--tw-space-y-reverse)));}
.space-y-3>:not([hidden])~:not([hidden]){margin-bottom:calc(0.75rem*var(--tw-space-y-reverse));margin-top:calc(0.75rem*(1 - var(--tw-space-y-reverse)));--tw-space-y-reverse:0;margin-bottom:calc(0.75rem*var(--tw-space-y-reverse));margin-top:calc(0.75rem*(1 - var(--tw-space-y-reverse)));}
.space-y-4>:not([hidden])~:not([hidden]){--tw-space-y-reverse:0;margin-bottom:calc(1rem*var(--tw-space-y-reverse));margin-top:calc(1rem*(1 - var(--tw-space-y-reverse)));}
.space-y-5>:not([hidden])~:not([hidden]){--tw-space-y-reverse:0;margin-bottom:calc(1.25rem*var(--tw-space-y-reverse));margin-top:calc(1.25rem*(1 - var(--tw-space-y-reverse)));}
.space-y-6>:not([hidden])~:not([hidden]){--tw-space-y-reverse:0;margin-bottom:calc(1.5rem*var(--tw-space-y-reverse));margin-top:calc(1.5rem*(1 - var(--tw-space-y-reverse)));}
.space-y-7>:not([hidden])~:not([hidden]){--tw-space-y-reverse:0;margin-bottom:calc(1.75rem*var(--tw-space-y-reverse));margin-top:calc(1.75rem*(1 - var(--tw-space-y-reverse)));}
.space-y-8>:not([hidden])~:not([hidden]){--tw-space-y-reverse:0;margin-bottom:calc(2rem*var(--tw-space-y-reverse));margin-top:calc(2rem*(1 - var(--tw-space-y-reverse)));}
.space-y-9>:not([hidden])~:not([hidden]){--tw-space-y-reverse:0;margin-bottom:calc(2.25rem*var(--tw-space-y-reverse));margin-top:calc(2.25rem*(1 - var(--tw-space-y-reverse)));}
.divide-y>:not([hidden])~:not([hidden]){--tw-divide-y-reverse:0;border-bottom-width:calc(1px*var(--tw-divide-y-reverse));border-top-width:calc(1px*(1 - var(--tw-divide-y-reverse)));}
.divide-white>:not([hidden])~:not([hidden]){--tw-divide-opacity:1;border-color:rgb(255 255 255 / var(--tw-divide-opacity, 1));}
.self-end{align-self:flex-end;}
.self-center{align-self:center;}
.overflow-auto{overflow:auto;}
.overflow-hidden{overflow:hidden;}
.overflow-visible{overflow:visible;}
.overflow-scroll{overflow:scroll;}
.overflow-x-auto{overflow-x:auto;}
.overflow-y-auto{overflow-y:auto;}
.overflow-x-hidden{overflow-x:hidden;}
.overflow-y-hidden{overflow-y:hidden;}
.overflow-x-scroll{overflow-x:scroll;}
.overflow-y-scroll{overflow-y:scroll;}
.truncate{overflow:hidden;white-space:nowrap;}
.text-ellipsis,.truncate{text-overflow:ellipsis;}
.whitespace-normal{white-space:normal;}
.whitespace-nowrap{white-space:nowrap;}
.whitespace-pre{white-space:pre;}
.whitespace-pre-wrap{white-space:pre-wrap;}
.text-wrap{text-wrap:wrap;}
.text-nowrap{text-wrap:nowrap;}
.break-words{overflow-wrap:break-word;}
.break-all{word-break:break-all;}
.rounded{border-radius:0.25rem;border-radius:.25rem;}
.rounded-2xl{border-radius:1rem;}
.rounded-3xl{border-radius:1.5rem;}
.rounded-\[100px\]{border-radius:100px;}
.rounded-\[10px\]{border-radius:10px;}
.rounded-\[12px\]{border-radius:12px;}
.rounded-\[16px\]{border-radius:16px;}
.rounded-\[24px\]{border-radius:24px;}
.rounded-\[48px\]{border-radius:48px;}
.rounded-\[8px\]{border-radius:8px;}
.rounded-full{border-radius:9999px;}
.rounded-lg{border-radius:0.5rem;border-radius:.5rem;}
.rounded-md{border-radius:0.375rem;border-radius:.375rem;}
.rounded-none{border-radius:0px;border-radius:0;}
.rounded-sm{border-radius:0.125rem;border-radius:.125rem;}
.rounded-xl{border-radius:0.75rem;border-radius:.75rem;}
.rounded-b-3xl{border-bottom-left-radius:1.5rem;border-bottom-right-radius:1.5rem;}
.rounded-b-md{border-bottom-left-radius:0.375rem;border-bottom-right-radius:0.375rem;}
.rounded-b-none{border-bottom-left-radius:0px;border-bottom-right-radius:0px;}
.rounded-e-3xl{border-end-end-radius:1.5rem;border-start-end-radius:1.5rem;}
.rounded-r-full{border-bottom-right-radius:9999px;border-top-right-radius:9999px;}
.rounded-s-3xl{border-end-start-radius:1.5rem;border-start-start-radius:1.5rem;}
.rounded-t-3xl{border-top-left-radius:1.5rem;border-top-right-radius:1.5rem;}
.rounded-t-md{border-top-left-radius:0.375rem;border-top-right-radius:0.375rem;}
.rounded-t-none{border-top-left-radius:0px;border-top-right-radius:0px;}
.rounded-bl-\[8px\]{border-bottom-left-radius:8px;}
.rounded-bl-full{border-bottom-left-radius:9999px;}
.rounded-bl-md{border-bottom-left-radius:0.375rem;}
.rounded-br-md{border-bottom-right-radius:0.375rem;}
.rounded-tl{border-top-left-radius:0.25rem;}
.rounded-tl-\[8px\]{border-top-left-radius:8px;}
.rounded-tl-md{border-top-left-radius:0.375rem;}
.rounded-tl-none{border-top-left-radius:0px;}
.rounded-tr{border-top-right-radius:0.25rem;}
.rounded-tr-md{border-top-right-radius:0.375rem;}
.\!border{border-width:1px!important;}
.border{border-width:1px;}
.border-0{border-width:0px;border-width:0;}
.border-2{border-width:2px;}
.border-4{border-width:4px;}
.border-8{border-width:8px;}
.border-x-4{border-left-width:4px;border-right-width:4px;}
.border-y{border-bottom-width:1px;border-top-width:1px;}
.\!border-t-0{border-top-width:0px !important;border-top-width:0!important;}
.border-b{border-bottom-width:1px;}
.border-b-0{border-bottom-width:0px;}
.border-b-2{border-bottom-width:2px;}
.border-b-4{border-bottom-width:4px;}
.border-e{border-inline-end-width:1px;}
.border-l{border-left-width:1px;}
.border-l-0{border-left-width:0px;}
.border-l-2{border-left-width:2px;}
.border-l-4{border-left-width:4px;}
.border-r{border-right-width:1px;}
.border-r-0{border-right-width:0px;}
.border-r-2{border-right-width:2px;}
.border-t{border-top-width:1px;}
.border-t-0{border-top-width:0px;}
.border-t-2{border-top-width:2px;}
.border-t-4{border-top-width:4px;}
.border-dashed{border-style:dashed;}
.\!border-none{border-style:none!important;}
.border-none{border-style:none;}
.border-\[\#00FF88\]{--tw-border-opacity:1;border-color:rgb(0 255 136 / var(--tw-border-opacity, 1));}
.border-\[\#00FF88\]\/30{border-color:rgba(0,255,136,.3);}
.border-\[\#00FFFF\]\/60{border-color:rgba(0,255,255,.6);}
.border-\[\#06B6D4\]{--tw-border-opacity:1;border-color:rgb(6 182 212 / var(--tw-border-opacity, 1));}
.border-\[\#06B6D4\]\/80{border-color:rgba(6,182,212,.8);}
.border-\[\#10B981\]{--tw-border-opacity:1;border-color:rgb(16 185 129 / var(--tw-border-opacity, 1));}
.border-\[\#13112E\]{--tw-border-opacity:1;border-color:rgb(19 17 46 / var(--tw-border-opacity, 1));}
.border-\[\#3463DB\]{border-color:rgb(52 99 219 / var(--tw-border-opacity, 1));--tw-border-opacity:1;border-color:rgb(52 99 219/var(--tw-border-opacity,1));}
.border-\[\#4B7DFF\]\/30{border-color:rgba(75,125,255,.3);}
.border-\[\#4B7DFF\]\/60{border-color:rgba(75,125,255,.6);}
.border-\[\#5081FF33\]{border-color:#5081ff33;}
.border-\[\#5081FF\]{border-color:rgb(80 129 255 / var(--tw-border-opacity, 1));--tw-border-opacity:1;border-color:rgb(80 129 255/var(--tw-border-opacity,1));}
.border-\[\#5081FF\]\/20{border-color:rgba(80,129,255,.2);}
.border-\[\#5081FF\]\/30{border-color:rgba(80,129,255,.3);}
.border-\[\#5081FF\]\/50{border-color:rgba(80,129,255,.5);}
.border-\[\#5081FF\]\/60{border-color:rgba(80,129,255,.6);}
.border-\[\#5081ff33\]{border-color:#5081ff33;}
.border-\[\#6366F1\]{--tw-border-opacity:1;border-color:rgb(99 102 241 / var(--tw-border-opacity, 1));}
.border-\[\#6366F1\]\/30{border-color:rgba(99,102,241,.3);}
.border-\[\#6366F1\]\/40{border-color:rgba(99,102,241,.4);}
.border-\[\#6366F1\]\/80{border-color:rgba(99,102,241,.8);}
.border-\[\#8B5CF6\]{--tw-border-opacity:1;border-color:rgb(139 92 246 / var(--tw-border-opacity, 1));}
.border-\[\#9C27B0\]\/30{border-color:rgba(156,39,176,.3);}
.border-\[\#EF4444\]{--tw-border-opacity:1;border-color:rgb(239 68 68 / var(--tw-border-opacity, 1));}
.border-\[\#F59E0B\]{--tw-border-opacity:1;border-color:rgb(245 158 11 / var(--tw-border-opacity, 1));}
.border-\[\#FF6B6B\]{--tw-border-opacity:1;border-color:rgb(255 107 107 / var(--tw-border-opacity, 1));}
.border-\[\#FF6B6B\]\/30{border-color:hsla(0,100%,71%,.3);}
.border-\[\#FFD700\]\/30{border-color:rgba(255,215,0,.3);}
.border-\[\#FFFFFF1F\]{border-color:#ffffff1f;}
.border-\[\#ff062e\]\/30{border-color:rgba(255,6,46,.3);}
.border-black{--tw-border-opacity:1;border-color:rgb(0 0 0 / var(--tw-border-opacity, 1));}
.border-blue-400{--tw-border-opacity:1;border-color:rgb(96 165 250 / var(--tw-border-opacity, 1));}
.border-blue-500{--tw-border-opacity:1;border-color:rgb(59 130 246 / var(--tw-border-opacity, 1));}
.border-blue-500\/50{border-color:rgba(59,130,246,.5);}
.border-emerald-500{--tw-border-opacity:1;border-color:rgb(16 185 129 / var(--tw-border-opacity, 1));}
.border-emerald-500\/50{border-color:rgba(16,185,129,.5);}
.border-gaming-blue{--tw-border-opacity:1;border-color:rgb(80 129 255 / var(--tw-border-opacity, 1)); border-color:#5081ff !important;}
.border-gaming-blue\/20{border-color:rgba(80,129,255,.2);}
.border-gaming-blue\/30{border-color:rgba(80,129,255,.3);}
.border-gaming-dark{--tw-border-opacity:1;border-color:rgb(15 23 42 / var(--tw-border-opacity, 1)); border-color:#0f172a !important;}
.border-gaming-deep-blue{--tw-border-opacity:1;border-color:rgb(52 99 219 / var(--tw-border-opacity, 1)); border-color:#3463db !important;}
.border-gaming-gold{--tw-border-opacity:1;border-color:rgb(255 215 0 / var(--tw-border-opacity, 1)); border-color:gold !important;}
.border-gaming-green{--tw-border-opacity:1;border-color:rgb(0 255 136 / var(--tw-border-opacity, 1)); border-color:#0f8 !important;}
.border-gaming-green\/30{border-color:rgba(0,255,136,.3);}
.border-gaming-light-blue{--tw-border-opacity:1;border-color:rgb(75 125 255 / var(--tw-border-opacity, 1)); border-color:#4b7dff !important;}
.border-gaming-navy{--tw-border-opacity:1;border-color:rgb(19 17 46 / var(--tw-border-opacity, 1)); border-color:#13112e !important;}
.border-gaming-orange{--tw-border-opacity:1;border-color:rgb(255 140 0 / var(--tw-border-opacity, 1)); border-color:#ff8c00 !important;}
.border-gaming-purple{--tw-border-opacity:1;border-color:rgb(39 36 80 / var(--tw-border-opacity, 1)); border-color:#272450 !important;}
.border-gaming-red{--tw-border-opacity:1;border-color:rgb(255 107 107 / var(--tw-border-opacity, 1)); border-color:#ff6b6b !important;}
.border-gaming-slate{--tw-border-opacity:1;border-color:rgb(30 41 59 / var(--tw-border-opacity, 1)); border-color:#1e293b !important;}
.border-gaming-violet{--tw-border-opacity:1;border-color:rgb(156 39 176 / var(--tw-border-opacity, 1)); border-color:#9c27b0 !important;}
.border-gray-200{--tw-border-opacity:1;border-color:rgb(229 231 235 / var(--tw-border-opacity, 1));}
.border-gray-300{--tw-border-opacity:1;border-color:rgb(209 213 219 / var(--tw-border-opacity, 1));}
.border-gray-400{--tw-border-opacity:1;border-color:rgb(156 163 175 / var(--tw-border-opacity, 1));}
.border-gray-500{--tw-border-opacity:1;border-color:rgb(107 114 128 / var(--tw-border-opacity, 1));}
.border-gray-600{--tw-border-opacity:1;border-color:rgb(75 85 99 / var(--tw-border-opacity, 1));}
.border-gray-600\/30{border-color:rgba(75,85,99,.3);}
.border-gray-600\/50{border-color:rgba(75,85,99,.5);}
.border-gray-700{--tw-border-opacity:1;border-color:rgb(55 65 81 / var(--tw-border-opacity, 1));}
.border-green-500{--tw-border-opacity:1;border-color:rgb(34 197 94 / var(--tw-border-opacity, 1));}
.border-green-500\/30{border-color:rgba(34,197,94,.3);}
.border-mandy-500{--tw-border-opacity:1;border-color:rgb(229 62 62 / var(--tw-border-opacity, 1));}
.border-mandy-500\/30{border-color:rgba(229,62,62,.3);}
.border-orange-500{--tw-border-opacity:1;border-color:rgb(249 115 22 / var(--tw-border-opacity, 1));}
.border-orange-500\/50{border-color:rgba(249,115,22,.5);}
.border-purple-500{--tw-border-opacity:1;border-color:rgb(168 85 247 / var(--tw-border-opacity, 1));}
.border-purple-500\/50{border-color:rgba(168,85,247,.5);}
.border-red-500{--tw-border-opacity:1;border-color:rgb(239 68 68 / var(--tw-border-opacity, 1));}
.border-red-500\/30{border-color:rgba(239,68,68,.3);}
.border-teal-500{--tw-border-opacity:1;border-color:rgb(20 184 166 / var(--tw-border-opacity, 1));}
.border-transparent{border-color:transparent;}
.border-white{--tw-border-opacity:1;border-color:rgb(255 255 255 / var(--tw-border-opacity, 1));}
.border-white\/20{border-color:hsla(0,0%,100%,.2);}
.border-white\/60{border-color:hsla(0,0%,100%,.6);}
.border-yellow-500{--tw-border-opacity:1;border-color:rgb(234 179 8 / var(--tw-border-opacity, 1));}
.border-y-\[\#5081ff33\]{border-bottom-color:#5081ff33;border-top-color:#5081ff33;}
.\!border-l-transparent{border-left-color:transparent !important;}
.border-l-black{--tw-border-opacity:1;border-left-color:rgb(0 0 0 / var(--tw-border-opacity, 1));}
.border-l-transparent{border-left-color:transparent;}
.border-t-\[\#5081FF33\]{border-top-color:#5081ff33;}
.border-t-\[\#5081ff33\]{border-top-color:#5081ff33;}
.border-t-transparent{border-top-color:transparent;}
.border-opacity-20{--tw-border-opacity:0.2;}
.\!bg-transparent{background-color:transparent!important;}
.bg-\[\#********\]{background-color:#********;}
.bg-\[\#0068FF\]{--tw-bg-opacity:1;background-color:rgb(0 104 255 / var(--tw-bg-opacity, 1));}
.bg-\[\#00FFFF\]{--tw-bg-opacity:1;background-color:rgb(0 255 255 / var(--tw-bg-opacity, 1));}
.bg-\[\#06B6D4\]{--tw-bg-opacity:1;background-color:rgb(6 182 212 / var(--tw-bg-opacity, 1));}
.bg-\[\#0E0A2F\]{background-color:rgb(14 10 47 / var(--tw-bg-opacity, 1));--tw-bg-opacity:1;background-color:rgb(14 10 47/var(--tw-bg-opacity,1));}
.bg-\[\#0E0A2F\]\/50{background-color:rgba(14,10,47,.5);}
.bg-\[\#0F172A\]{--tw-bg-opacity:1;background-color:rgb(15 23 42 / var(--tw-bg-opacity, 1));}
.bg-\[\#10B981\]{--tw-bg-opacity:1;background-color:rgb(16 185 129 / var(--tw-bg-opacity, 1));}
.bg-\[\#13112E\]{--tw-bg-opacity:1;background-color:rgb(19 17 46 / var(--tw-bg-opacity, 1));}
.bg-\[\#1E293B\]{--tw-bg-opacity:1;background-color:rgb(30 41 59 / var(--tw-bg-opacity, 1));}
.bg-\[\#1E293B\]\/50{background-color:rgba(30,41,59,.5);}
.bg-\[\#272450\]{background-color:rgb(39 36 80 / var(--tw-bg-opacity, 1));--tw-bg-opacity:1;background-color:rgb(39 36 80/var(--tw-bg-opacity,1));}
.bg-\[\#2b22b3\]{--tw-bg-opacity:1;background-color:rgb(43 34 179 / var(--tw-bg-opacity, 1));}
.bg-\[\#3463DB\]{--tw-bg-opacity:1;background-color:rgb(52 99 219 / var(--tw-bg-opacity, 1));}
.bg-\[\#4B7DFF\]{background-color:rgb(75 125 255 / var(--tw-bg-opacity, 1));--tw-bg-opacity:1;background-color:rgb(75 125 255/var(--tw-bg-opacity,1));}
.bg-\[\#4B7DFF\]\/20{background-color:rgba(75,125,255,.2);}
.bg-\[\#5081FF\]{background-color:rgb(80 129 255 / var(--tw-bg-opacity, 1));--tw-bg-opacity:1;background-color:rgb(80 129 255/var(--tw-bg-opacity,1));}
.bg-\[\#5081FF\]\/20{background-color:rgba(80,129,255,.2);}
.bg-\[\#5081FF\]\/30{background-color:rgba(80,129,255,.3);}
.bg-\[\#5865F2\]{--tw-bg-opacity:1;background-color:rgb(88 101 242 / var(--tw-bg-opacity, 1));}
.bg-\[\#6366F1\]{--tw-bg-opacity:1;background-color:rgb(99 102 241 / var(--tw-bg-opacity, 1));}
.bg-\[\#8B5CF6\]{--tw-bg-opacity:1;background-color:rgb(139 92 246 / var(--tw-bg-opacity, 1));}
.bg-\[\#EF4444\]{--tw-bg-opacity:1;background-color:rgb(239 68 68 / var(--tw-bg-opacity, 1));}
.bg-\[\#F59E0B\]{--tw-bg-opacity:1;background-color:rgb(245 158 11 / var(--tw-bg-opacity, 1));}
.bg-\[\#FFFFFF0A\]{background-color:#ffffff0a;}
.bg-\[\#FFFFFF1F\]{background-color:#ffffff1f;}
.bg-\[\#FFFFFF33\]{background-color:#ffffff33;}
.bg-\[\#FFFFFF7A\]{background-color:#ffffff7a;}
.bg-\[\#fafafa\]{--tw-bg-opacity:1;background-color:rgb(250 250 250 / var(--tw-bg-opacity, 1));}
.bg-\[\#fbfbfb\]{--tw-bg-opacity:1;background-color:rgb(251 251 251 / var(--tw-bg-opacity, 1));}
.bg-\[\#ff062e\]\/10{background-color:rgba(255,6,46,.1);}
.bg-\[\#ff062e\]\/20{background-color:rgba(255,6,46,.2);}
.bg-\[\#fff9\]{background-color:#fff9;}
.bg-\[\#ffffff1f\]{background-color:#ffffff1f;}
.bg-\[black\]\/60{background-color:rgba(0,0,0,.6);}
.bg-\[rgba\(0\2c 0\2c 0\2c 0\.5\)\]{background-color:rgba(0,0,0,.5);}
.bg-black{background-color:rgb(0 0 0 / var(--tw-bg-opacity, 1));--tw-bg-opacity:1;background-color:rgb(0 0 0/var(--tw-bg-opacity,1));}
.bg-black\/80{background-color:rgba(0,0,0,.8);}
.bg-blue-400{--tw-bg-opacity:1;background-color:rgb(96 165 250 / var(--tw-bg-opacity, 1));}
.bg-blue-400\/10{background-color:rgba(96,165,250,.1);}
.bg-blue-500{--tw-bg-opacity:1;background-color:rgb(59 130 246 / var(--tw-bg-opacity, 1));}
.bg-blue-600{background-color:rgb(37 99 235 / var(--tw-bg-opacity, 1));--tw-bg-opacity:1;background-color:rgb(37 99 235/var(--tw-bg-opacity,1));}
.bg-blue-700{background-color:rgb(29 78 216 / var(--tw-bg-opacity, 1));--tw-bg-opacity:1;background-color:rgb(29 78 216/var(--tw-bg-opacity,1));}
.bg-blue-800{background-color:rgb(30 64 175 / var(--tw-bg-opacity, 1));--tw-bg-opacity:1;background-color:rgb(30 64 175/var(--tw-bg-opacity,1));}
.bg-emerald-300{--tw-bg-opacity:1;background-color:rgb(110 231 183 / var(--tw-bg-opacity, 1));}
.bg-emerald-400{--tw-bg-opacity:1;background-color:rgb(52 211 153 / var(--tw-bg-opacity, 1));}
.bg-emerald-400\/10{background-color:rgba(52,211,153,.1);}
.bg-emerald-50{--tw-bg-opacity:1;background-color:rgb(236 253 245 / var(--tw-bg-opacity, 1));}
.bg-gaming-blue{--tw-bg-opacity:1;background-color:rgb(80 129 255 / var(--tw-bg-opacity, 1)); background-color:#5081ff !important;}
.bg-gaming-blue\/20{background-color:rgba(80,129,255,.2);}
.bg-gaming-dark{--tw-bg-opacity:1;background-color:rgb(15 23 42 / var(--tw-bg-opacity, 1)); background-color:#0f172a !important;}
.bg-gaming-deep-blue{--tw-bg-opacity:1;background-color:rgb(52 99 219 / var(--tw-bg-opacity, 1)); background-color:#3463db !important;}
.bg-gaming-gold{--tw-bg-opacity:1;background-color:rgb(255 215 0 / var(--tw-bg-opacity, 1)); background-color:gold !important;}
.bg-gaming-green{--tw-bg-opacity:1;background-color:rgb(0 255 136 / var(--tw-bg-opacity, 1)); background-color:#0f8 !important;}
.bg-gaming-green\/20{background-color:rgba(0,255,136,.2);}
.bg-gaming-light-blue{--tw-bg-opacity:1;background-color:rgb(75 125 255 / var(--tw-bg-opacity, 1)); background-color:#4b7dff !important;}
.bg-gaming-navy{--tw-bg-opacity:1;background-color:rgb(19 17 46 / var(--tw-bg-opacity, 1)); background-color:#13112e !important;}
.bg-gaming-orange{--tw-bg-opacity:1;background-color:rgb(255 140 0 / var(--tw-bg-opacity, 1)); background-color:#ff8c00 !important;}
.bg-gaming-purple{--tw-bg-opacity:1;background-color:rgb(39 36 80 / var(--tw-bg-opacity, 1)); background-color:#272450 !important;}
.bg-gaming-purple\/20{background-color:rgba(39,36,80,.2);}
.bg-gaming-red{--tw-bg-opacity:1;background-color:rgb(255 107 107 / var(--tw-bg-opacity, 1)); background-color:#ff6b6b !important;}
.bg-gaming-slate{--tw-bg-opacity:1;background-color:rgb(30 41 59 / var(--tw-bg-opacity, 1)); background-color:#1e293b !important;}
.bg-gaming-slate\/20{background-color:rgba(30,41,59,.2);}
.bg-gaming-slate\/50{background-color:rgba(30,41,59,.5);}
.bg-gaming-violet{--tw-bg-opacity:1;background-color:rgb(156 39 176 / var(--tw-bg-opacity, 1)); background-color:#9c27b0 !important;}
.bg-gray-100{background-color:rgb(243 244 246 / var(--tw-bg-opacity, 1));
    --tw-bg-opacity:1;
    background-color:rgb(243 244 246 / var(--tw-bg-opacity));}
.bg-gray-200{background-color:rgb(229 231 235 / var(--tw-bg-opacity, 1));background-color:rgb(229 231 235/var(--tw-bg-opacity,1));
    --tw-bg-opacity:1;
    background-color:rgb(229 231 235 / var(--tw-bg-opacity));}
.bg-gray-300{background-color:rgb(209 213 219 / var(--tw-bg-opacity, 1));
    --tw-bg-opacity:1;
    background-color:rgb(209 213 219 / var(--tw-bg-opacity));}
.bg-gray-400{background-color:rgb(156 163 175 / var(--tw-bg-opacity, 1));
    --tw-bg-opacity:1;
    background-color:rgb(156 163 175 / var(--tw-bg-opacity));}
.bg-gray-50{background-color:rgb(249 250 251 / var(--tw-bg-opacity, 1));
    --tw-bg-opacity:1;
    background-color:rgb(249 250 251 / var(--tw-bg-opacity));}
.bg-gray-500{background-color:rgb(107 114 128 / var(--tw-bg-opacity, 1));
    --tw-bg-opacity:1;
    background-color:rgb(107 114 128 / var(--tw-bg-opacity));}
.bg-gray-500\/50{background-color:hsla(220,9%,46%,.5);}
.bg-gray-600{background-color:rgb(75 85 99 / var(--tw-bg-opacity, 1));
    --tw-bg-opacity:1;
    background-color:rgb(75 85 99 / var(--tw-bg-opacity));}
.bg-gray-600\/50{background-color:rgba(75,85,99,.5);}
.bg-gray-700{background-color:rgb(55 65 81 / var(--tw-bg-opacity, 1));
    --tw-bg-opacity:1;
    background-color:rgb(55 65 81 / var(--tw-bg-opacity));}
.bg-gray-700\/20{background-color:rgba(55,65,81,.2);}
.bg-gray-800{background-color:rgb(31 41 55 / var(--tw-bg-opacity, 1));
    --tw-bg-opacity:1;
    background-color:rgb(31 41 55 / var(--tw-bg-opacity));}
.bg-gray-900{background-color:rgb(17 24 39 / var(--tw-bg-opacity, 1));
    --tw-bg-opacity:1;
    background-color:rgb(17 24 39 / var(--tw-bg-opacity));}
.bg-gray-950{--tw-bg-opacity:1;background-color:rgb(3 7 18 / var(--tw-bg-opacity, 1));}
.bg-green-500{--tw-bg-opacity:1;background-color:rgb(34 197 94 / var(--tw-bg-opacity, 1));}
.bg-green-500\/10{background-color:rgba(34,197,94,.1);}
.bg-green-700{background-color:rgb(21 128 61 / var(--tw-bg-opacity, 1));--tw-bg-opacity:1;background-color:rgb(21 128 61/var(--tw-bg-opacity,1));}
.bg-mandy-500{--tw-bg-opacity:1;background-color:rgb(229 62 62 / var(--tw-bg-opacity, 1));}
.bg-mandy-500\/90{background-color:rgba(229,62,62,.9);}
.bg-red-400{--tw-bg-opacity:1;background-color:rgb(248 113 113 / var(--tw-bg-opacity, 1));}
.bg-red-500{--tw-bg-opacity:1;background-color:rgb(239 68 68 / var(--tw-bg-opacity, 1));}
.bg-red-500\/10{background-color:rgba(239,68,68,.1);}
.bg-stone-950{--tw-bg-opacity:1;background-color:rgb(12 10 9 / var(--tw-bg-opacity, 1));}
.bg-success-dark-light{--tw-bg-opacity:1;background-color:rgb(21 87 36 / var(--tw-bg-opacity, 1));}
.bg-success-light{--tw-bg-opacity:1;background-color:rgb(212 237 218 / var(--tw-bg-opacity, 1));}
.bg-transparent{
    background-color:transparent;}
.bg-white{background-color:rgb(255 255 255 / var(--tw-bg-opacity, 1));background-color:rgb(255 255 255/var(--tw-bg-opacity,1));
    --tw-bg-opacity:1;
    background-color:rgb(255 255 255 / var(--tw-bg-opacity));}
.bg-white\/10{
    background-color:hsla(0,0%,100%,.1);}
.bg-yellow-300{--tw-bg-opacity:1;background-color:rgb(253 224 71 / var(--tw-bg-opacity, 1));}
.bg-yellow-500{--tw-bg-opacity:1;background-color:rgb(234 179 8 / var(--tw-bg-opacity, 1));}
.bg-opacity-60{--tw-bg-opacity:0.6;}
.bg-gradient-to-b{background-image:linear-gradient(to bottom, var(--tw-gradient-stops));background-image:linear-gradient(to bottom,var(--tw-gradient-stops));}
.bg-gradient-to-br{background-image:linear-gradient(to bottom right, var(--tw-gradient-stops));}
.bg-gradient-to-l{background-image:linear-gradient(to left, var(--tw-gradient-stops));}
.bg-gradient-to-r{background-image:linear-gradient(to right, var(--tw-gradient-stops));background-image:linear-gradient(to right,var(--tw-gradient-stops));}
.bg-gradient-to-t{background-image:linear-gradient(to top, var(--tw-gradient-stops));}
.from-\[\#00FF88\]{--tw-gradient-from:#0f8 var(--tw-gradient-from-position);--tw-gradient-to:rgba(0,255,136,0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to);}
.from-\[\#00FF88\]\/20{--tw-gradient-from:rgba(0,255,136,.2) var(--tw-gradient-from-position);--tw-gradient-to:rgba(0,255,136,0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to);}
.from-\[\#00FFFF\]{--tw-gradient-from:#0ff var(--tw-gradient-from-position);--tw-gradient-to:rgba(0,255,255,0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to);}
.from-\[\#06B6D4\]{--tw-gradient-from:#06b6d4 var(--tw-gradient-from-position);--tw-gradient-to:rgba(6,182,212,0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to);}
.from-\[\#0F172A\]{--tw-gradient-from:#0f172a var(--tw-gradient-from-position);--tw-gradient-to:rgba(15,23,42,0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to);}
.from-\[\#0F172A\]\/80{--tw-gradient-from:rgba(15,23,42,.8) var(--tw-gradient-from-position);--tw-gradient-to:rgba(15,23,42,0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to);}
.from-\[\#0F172A\]\/90{--tw-gradient-from:rgba(15,23,42,.9) var(--tw-gradient-from-position);--tw-gradient-to:rgba(15,23,42,0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to);}
.from-\[\#0F172A\]\/95{--tw-gradient-from:rgba(15,23,42,.95) var(--tw-gradient-from-position);--tw-gradient-to:rgba(15,23,42,0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to);}
.from-\[\#13112E\]{--tw-gradient-from:#13112e var(--tw-gradient-from-position);--tw-gradient-to:rgba(19,17,46,0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to);}
.from-\[\#1E293B\]\/50{--tw-gradient-from:rgba(30,41,59,.5) var(--tw-gradient-from-position);--tw-gradient-to:rgba(30,41,59,0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to);}
.from-\[\#1E293B\]\/60{--tw-gradient-from:rgba(30,41,59,.6) var(--tw-gradient-from-position);--tw-gradient-to:rgba(30,41,59,0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to);}
.from-\[\#1E293B\]\/80{--tw-gradient-from:rgba(30,41,59,.8) var(--tw-gradient-from-position);--tw-gradient-to:rgba(30,41,59,0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to);}
.from-\[\#1E293B\]\/95{--tw-gradient-from:rgba(30,41,59,.95) var(--tw-gradient-from-position);--tw-gradient-to:rgba(30,41,59,0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to);}
.from-\[\#1a1640\]\/80{--tw-gradient-from:rgba(26,22,64,.8) var(--tw-gradient-from-position);--tw-gradient-to:rgba(26,22,64,0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to);}
.from-\[\#2A2D4F\]{--tw-gradient-from:#2a2d4f var(--tw-gradient-from-position);--tw-gradient-to:rgba(42,45,79,0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to);}
.from-\[\#4B7DFF\]{--tw-gradient-from:#4b7dff var(--tw-gradient-from-position);--tw-gradient-to:rgba(75,125,255,0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to);}
.from-\[\#4B7DFF\]\/10{--tw-gradient-from:rgba(75,125,255,.1) var(--tw-gradient-from-position);--tw-gradient-to:rgba(75,125,255,0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to);}
.from-\[\#4B7DFF\]\/20{--tw-gradient-from:rgba(75,125,255,.2) var(--tw-gradient-from-position);--tw-gradient-to:rgba(75,125,255,0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to);}
.from-\[\#4B7DFF\]\/5{--tw-gradient-from:rgba(75,125,255,.05) var(--tw-gradient-from-position);--tw-gradient-to:rgba(75,125,255,0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to);}
.from-\[\#5081FF\]{--tw-gradient-from:#5081ff var(--tw-gradient-from-position);--tw-gradient-to:rgba(80,129,255,0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to);}
.from-\[\#5081FF\]\/10{--tw-gradient-from:rgba(80,129,255,.1) var(--tw-gradient-from-position);--tw-gradient-to:rgba(80,129,255,0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to);}
.from-\[\#5081FF\]\/20{--tw-gradient-from:rgba(80,129,255,.2) var(--tw-gradient-from-position);--tw-gradient-to:rgba(80,129,255,0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to);}
.from-\[\#6366F1\]{--tw-gradient-from:#6366f1 var(--tw-gradient-from-position);--tw-gradient-to:rgba(99,102,241,0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to);}
.from-\[\#6366F1\]\/10{--tw-gradient-from:rgba(99,102,241,.1) var(--tw-gradient-from-position);--tw-gradient-to:rgba(99,102,241,0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to);}
.from-\[\#6366F1\]\/20{--tw-gradient-from:rgba(99,102,241,.2) var(--tw-gradient-from-position);--tw-gradient-to:rgba(99,102,241,0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to);}
.from-\[\#8B5CF6\]\/10{--tw-gradient-from:rgba(139,92,246,.1) var(--tw-gradient-from-position);--tw-gradient-to:rgba(139,92,246,0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to);}
.from-\[\#8B5CF6\]\/30{--tw-gradient-from:rgba(139,92,246,.3) var(--tw-gradient-from-position);--tw-gradient-to:rgba(139,92,246,0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to);}
.from-\[\#9C27B0\]{--tw-gradient-from:#9c27b0 var(--tw-gradient-from-position);--tw-gradient-to:rgba(156,39,176,0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to);}
.from-\[\#9C27B0\]\/20{--tw-gradient-from:rgba(156,39,176,.2) var(--tw-gradient-from-position);--tw-gradient-to:rgba(156,39,176,0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to);}
.from-\[\#F59E0B\]{--tw-gradient-from:#f59e0b var(--tw-gradient-from-position);--tw-gradient-to:rgba(245,158,11,0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to);}
.from-\[\#FF6B6B\]{--tw-gradient-from:#ff6b6b var(--tw-gradient-from-position);--tw-gradient-to:hsla(0,100%,71%,0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to);}
.from-\[\#FF6B6B\]\/20{--tw-gradient-from:hsla(0,100%,71%,.2) var(--tw-gradient-from-position);--tw-gradient-to:hsla(0,100%,71%,0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to);}
.from-\[\#FFD700\]{--tw-gradient-from:gold var(--tw-gradient-from-position);--tw-gradient-to:rgba(255,215,0,0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to);}
.from-\[\#FFD700\]\/20{--tw-gradient-from:rgba(255,215,0,.2) var(--tw-gradient-from-position);--tw-gradient-to:rgba(255,215,0,0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to);}
.from-amber-500{--tw-gradient-from:#f59e0b var(--tw-gradient-from-position);--tw-gradient-to:rgba(245,158,11,0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to);}
.from-black{--tw-gradient-from:#000 var(--tw-gradient-from-position);--tw-gradient-to:transparent var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to);}
.from-black\/15{--tw-gradient-from:rgba(0,0,0,.15) var(--tw-gradient-from-position);--tw-gradient-to:transparent var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to);}
.from-black\/20{--tw-gradient-from:rgba(0,0,0,.2) var(--tw-gradient-from-position);--tw-gradient-to:transparent var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to);}
.from-black\/90{--tw-gradient-from:rgba(0,0,0,.9) var(--tw-gradient-from-position);--tw-gradient-to:transparent var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to);}
.from-blue-400{--tw-gradient-from:#60a5fa var(--tw-gradient-from-position);--tw-gradient-to:rgba(96,165,250,0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to);}
.from-blue-500{--tw-gradient-from:#3b82f6 var(--tw-gradient-from-position);--tw-gradient-to:rgba(59,130,246,0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to);}
.from-blue-500\/10{--tw-gradient-from:rgba(59,130,246,.1) var(--tw-gradient-from-position);--tw-gradient-to:rgba(59,130,246,0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to);}
.from-blue-500\/20{--tw-gradient-from:rgba(59,130,246,.2) var(--tw-gradient-from-position);--tw-gradient-to:rgba(59,130,246,0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to);}
.from-blue-500\/30{--tw-gradient-from:rgba(59,130,246,.3) var(--tw-gradient-from-position);--tw-gradient-to:rgba(59,130,246,0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to);}
.from-blue-500\/5{--tw-gradient-from:rgba(59,130,246,.05) var(--tw-gradient-from-position);--tw-gradient-to:rgba(59,130,246,0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to);}
.from-blue-900{--tw-gradient-from:#1e3a8a var(--tw-gradient-from-position);--tw-gradient-to:rgba(30,58,138,0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to);}
.from-blue-900\/95{--tw-gradient-from:rgba(30,58,138,.95) var(--tw-gradient-from-position);--tw-gradient-to:rgba(30,58,138,0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to);}
.from-cyan-500{--tw-gradient-from:#06b6d4 var(--tw-gradient-from-position);--tw-gradient-to:rgba(6,182,212,0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to);}
.from-emerald-500{--tw-gradient-from:#10b981 var(--tw-gradient-from-position);--tw-gradient-to:rgba(16,185,129,0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to);}
.from-emerald-900{--tw-gradient-from:#064e3b var(--tw-gradient-from-position);--tw-gradient-to:rgba(6,78,59,0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to);}
.from-emerald-900\/95{--tw-gradient-from:rgba(6,78,59,.95) var(--tw-gradient-from-position);--tw-gradient-to:rgba(6,78,59,0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to);}
.from-fuchsia-500{--tw-gradient-from:#d946ef var(--tw-gradient-from-position);--tw-gradient-to:rgba(217,70,239,0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to);}
.from-gaming-blue{--tw-gradient-from:#5081ff var(--tw-gradient-from-position);--tw-gradient-to:rgba(80,129,255,0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to);}
.from-gaming-dark{--tw-gradient-from:#0f172a var(--tw-gradient-from-position);--tw-gradient-to:rgba(15,23,42,0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to);}
.from-gaming-gold{--tw-gradient-from:gold var(--tw-gradient-from-position);--tw-gradient-to:rgba(255,215,0,0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to);}
.from-gaming-green{--tw-gradient-from:#0f8 var(--tw-gradient-from-position);--tw-gradient-to:rgba(0,255,136,0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to);}
.from-gaming-purple{--tw-gradient-from:#272450 var(--tw-gradient-from-position);--tw-gradient-to:rgba(39,36,80,0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to);}
.from-gaming-slate{--tw-gradient-from:#1e293b var(--tw-gradient-from-position);--tw-gradient-to:rgba(30,41,59,0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to);}
.from-gaming-slate\/60{--tw-gradient-from:rgba(30,41,59,.6) var(--tw-gradient-from-position);--tw-gradient-to:rgba(30,41,59,0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to);}
.from-gaming-violet{--tw-gradient-from:#9c27b0 var(--tw-gradient-from-position);--tw-gradient-to:rgba(156,39,176,0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to);}
.from-gray-700{--tw-gradient-from:#374151 var(--tw-gradient-from-position);--tw-gradient-to:rgba(55,65,81,0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to);}
.from-gray-700\/80{--tw-gradient-from:rgba(55,65,81,.8) var(--tw-gradient-from-position);--tw-gradient-to:rgba(55,65,81,0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to);}
.from-gray-800{--tw-gradient-from:#1f2937 var(--tw-gradient-from-position);--tw-gradient-to:rgba(31,41,55,0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to);}
.from-gray-800\/50{--tw-gradient-from:rgba(31,41,55,.5) var(--tw-gradient-from-position);--tw-gradient-to:rgba(31,41,55,0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to);}
.from-gray-800\/60{--tw-gradient-from:rgba(31,41,55,.6) var(--tw-gradient-from-position);--tw-gradient-to:rgba(31,41,55,0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to);}
.from-gray-800\/80{--tw-gradient-from:rgba(31,41,55,.8) var(--tw-gradient-from-position);--tw-gradient-to:rgba(31,41,55,0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to);}
.from-green-500{--tw-gradient-from:#22c55e var(--tw-gradient-from-position);--tw-gradient-to:rgba(34,197,94,0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to);}
.from-green-500\/20{--tw-gradient-from:rgba(34,197,94,.2) var(--tw-gradient-from-position);--tw-gradient-to:rgba(34,197,94,0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to);}
.from-indigo-900{--tw-gradient-from:#312e81 var(--tw-gradient-from-position);--tw-gradient-to:rgba(49,46,129,0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to);}
.from-orange-900{--tw-gradient-from:#7c2d12 var(--tw-gradient-from-position);--tw-gradient-to:rgba(124,45,18,0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to);}
.from-orange-900\/95{--tw-gradient-from:rgba(124,45,18,.95) var(--tw-gradient-from-position);--tw-gradient-to:rgba(124,45,18,0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to);}
.from-pink-500{--tw-gradient-from:#ec4899 var(--tw-gradient-from-position);--tw-gradient-to:rgba(236,72,153,0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to);}
.from-purple-900{--tw-gradient-from:#581c87 var(--tw-gradient-from-position);--tw-gradient-to:rgba(88,28,135,0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to);}
.from-purple-900\/95{--tw-gradient-from:rgba(88,28,135,.95) var(--tw-gradient-from-position);--tw-gradient-to:rgba(88,28,135,0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to);}
.from-red-500{--tw-gradient-from:#ef4444 var(--tw-gradient-from-position);--tw-gradient-to:rgba(239,68,68,0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to);}
.from-red-600{--tw-gradient-from:#dc2626 var(--tw-gradient-from-position);--tw-gradient-to:rgba(220,38,38,0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to);}
.from-teal-500{--tw-gradient-from:#14b8a6 var(--tw-gradient-from-position);--tw-gradient-to:rgba(20,184,166,0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to);}
.from-transparent{--tw-gradient-from:transparent var(--tw-gradient-from-position);--tw-gradient-to:transparent var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to);}
.from-violet-500{--tw-gradient-from:#8b5cf6 var(--tw-gradient-from-position);--tw-gradient-to:rgba(139,92,246,0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to);}
.from-yellow-400{--tw-gradient-from:#facc15 var(--tw-gradient-from-position);--tw-gradient-to:rgba(250,204,21,0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to);}
.from-yellow-500{--tw-gradient-from:#eab308 var(--tw-gradient-from-position);--tw-gradient-to:rgba(234,179,8,0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to);}
.from-yellow-500\/10{--tw-gradient-from:rgba(234,179,8,.1) var(--tw-gradient-from-position);--tw-gradient-to:rgba(234,179,8,0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to);}
.via-\[\#00FF88\]\/60{--tw-gradient-to:rgba(0,255,136,0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), rgba(0,255,136,.6) var(--tw-gradient-via-position), var(--tw-gradient-to);}
.via-\[\#00FFFF\]{--tw-gradient-to:rgba(0,255,255,0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), #0ff var(--tw-gradient-via-position), var(--tw-gradient-to);}
.via-\[\#1E293B\]{--tw-gradient-to:rgba(30,41,59,0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), #1e293b var(--tw-gradient-via-position), var(--tw-gradient-to);}
.via-\[\#1E293B\]\/80{--tw-gradient-to:rgba(30,41,59,0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), rgba(30,41,59,.8) var(--tw-gradient-via-position), var(--tw-gradient-to);}
.via-\[\#1E293B\]\/90{--tw-gradient-to:rgba(30,41,59,0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), rgba(30,41,59,.9) var(--tw-gradient-via-position), var(--tw-gradient-to);}
.via-\[\#1a1640\]{--tw-gradient-to:rgba(26,22,64,0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), #1a1640 var(--tw-gradient-via-position), var(--tw-gradient-to);}
.via-\[\#374151\]\/85{--tw-gradient-to:rgba(55,65,81,0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), rgba(55,65,81,.85) var(--tw-gradient-via-position), var(--tw-gradient-to);}
.via-\[\#4B7DFF\]\/20{--tw-gradient-to:rgba(75,125,255,0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), rgba(75,125,255,.2) var(--tw-gradient-via-position), var(--tw-gradient-to);}
.via-\[\#5081FF\]{--tw-gradient-to:rgba(80,129,255,0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), #5081ff var(--tw-gradient-via-position), var(--tw-gradient-to);}
.via-\[\#5081FF\]\/10{--tw-gradient-to:rgba(80,129,255,0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), rgba(80,129,255,.1) var(--tw-gradient-via-position), var(--tw-gradient-to);}
.via-\[\#5081FF\]\/30{--tw-gradient-to:rgba(80,129,255,0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), rgba(80,129,255,.3) var(--tw-gradient-via-position), var(--tw-gradient-to);}
.via-\[\#6366F1\]\/15{--tw-gradient-to:rgba(99,102,241,0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), rgba(99,102,241,.15) var(--tw-gradient-via-position), var(--tw-gradient-to);}
.via-\[\#8B5CF6\]{--tw-gradient-to:rgba(139,92,246,0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), #8b5cf6 var(--tw-gradient-via-position), var(--tw-gradient-to);}
.via-\[\#8B5CF6\]\/10{--tw-gradient-to:rgba(139,92,246,0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), rgba(139,92,246,.1) var(--tw-gradient-via-position), var(--tw-gradient-to);}
.via-\[\#9C27B0\]\/60{--tw-gradient-to:rgba(156,39,176,0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), rgba(156,39,176,.6) var(--tw-gradient-via-position), var(--tw-gradient-to);}
.via-\[\#FF6B6B\]\/60{--tw-gradient-to:hsla(0,100%,71%,0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), hsla(0,100%,71%,.6) var(--tw-gradient-via-position), var(--tw-gradient-to);}
.via-\[\#FFD700\]\/60{--tw-gradient-to:rgba(255,215,0,0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), rgba(255,215,0,.6) var(--tw-gradient-via-position), var(--tw-gradient-to);}
.via-black{--tw-gradient-to:transparent var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), #000 var(--tw-gradient-via-position), var(--tw-gradient-to);}
.via-gaming-dark{--tw-gradient-to:rgba(15,23,42,0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), #0f172a var(--tw-gradient-via-position), var(--tw-gradient-to);}
.via-gaming-slate{--tw-gradient-to:rgba(30,41,59,0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), #1e293b var(--tw-gradient-via-position), var(--tw-gradient-to);}
.via-gray-900{--tw-gradient-to:rgba(17,24,39,0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), #111827 var(--tw-gradient-via-position), var(--tw-gradient-to);}
.via-gray-900\/95{--tw-gradient-to:rgba(17,24,39,0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), rgba(17,24,39,.95) var(--tw-gradient-via-position), var(--tw-gradient-to);}
.via-purple-400{--tw-gradient-to:rgba(192,132,252,0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), #c084fc var(--tw-gradient-via-position), var(--tw-gradient-to);}
.via-purple-500{--tw-gradient-to:rgba(168,85,247,0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), #a855f7 var(--tw-gradient-via-position), var(--tw-gradient-to);}
.via-purple-500\/20{--tw-gradient-to:rgba(168,85,247,0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), rgba(168,85,247,.2) var(--tw-gradient-via-position), var(--tw-gradient-to);}
.via-purple-500\/30{--tw-gradient-to:rgba(168,85,247,0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), rgba(168,85,247,.3) var(--tw-gradient-via-position), var(--tw-gradient-to);}
.via-purple-500\/5{--tw-gradient-to:rgba(168,85,247,0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), rgba(168,85,247,.05) var(--tw-gradient-via-position), var(--tw-gradient-to);}
.via-transparent{--tw-gradient-to:transparent var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), transparent var(--tw-gradient-via-position), var(--tw-gradient-to);}
.via-white{--tw-gradient-to:hsla(0,0%,100%,0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), #fff var(--tw-gradient-via-position), var(--tw-gradient-to);}
.via-white\/10{--tw-gradient-to:hsla(0,0%,100%,0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), hsla(0,0%,100%,.1) var(--tw-gradient-via-position), var(--tw-gradient-to);}
.via-white\/20{--tw-gradient-to:hsla(0,0%,100%,0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), hsla(0,0%,100%,.2) var(--tw-gradient-via-position), var(--tw-gradient-to);}
.via-white\/30{--tw-gradient-to:hsla(0,0%,100%,0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), hsla(0,0%,100%,.3) var(--tw-gradient-via-position), var(--tw-gradient-to);}
.to-\[\#0099CC\]{--tw-gradient-to:#09c var(--tw-gradient-to-position);}
.to-\[\#00CC6A\]{--tw-gradient-to:#00cc6a var(--tw-gradient-to-position);}
.to-\[\#00FF88\]\/20{--tw-gradient-to:rgba(0,255,136,.2) var(--tw-gradient-to-position);}
.to-\[\#06B6D4\]{--tw-gradient-to:#06b6d4 var(--tw-gradient-to-position);}
.to-\[\#06B6D4\]\/10{--tw-gradient-to:rgba(6,182,212,.1) var(--tw-gradient-to-position);}
.to-\[\#0F172A\]{--tw-gradient-to:#0f172a var(--tw-gradient-to-position);}
.to-\[\#0F172A\]\/50{--tw-gradient-to:rgba(15,23,42,.5) var(--tw-gradient-to-position);}
.to-\[\#0F172A\]\/80{--tw-gradient-to:rgba(15,23,42,.8) var(--tw-gradient-to-position);}
.to-\[\#0F172A\]\/90{--tw-gradient-to:rgba(15,23,42,.9) var(--tw-gradient-to-position);}
.to-\[\#0F172A\]\/95{--tw-gradient-to:rgba(15,23,42,.95) var(--tw-gradient-to-position);}
.to-\[\#10B981\]{--tw-gradient-to:#10b981 var(--tw-gradient-to-position);}
.to-\[\#13112E\]{--tw-gradient-to:#13112e var(--tw-gradient-to-position);}
.to-\[\#13112E\]\/90{--tw-gradient-to:rgba(19,17,46,.9) var(--tw-gradient-to-position);}
.to-\[\#1E293B\]\/90{--tw-gradient-to:rgba(30,41,59,.9) var(--tw-gradient-to-position);}
.to-\[\#1E293B\]\/95{--tw-gradient-to:rgba(30,41,59,.95) var(--tw-gradient-to-position);}
.to-\[\#1a1640\]{--tw-gradient-to:#1a1640 var(--tw-gradient-to-position);}
.to-\[\#3463DB\]{--tw-gradient-to:#3463db var(--tw-gradient-to-position);}
.to-\[\#3463DB\]\/10{--tw-gradient-to:rgba(52,99,219,.1) var(--tw-gradient-to-position);}
.to-\[\#3463DB\]\/20{--tw-gradient-to:rgba(52,99,219,.2) var(--tw-gradient-to-position);}
.to-\[\#4B7DFF\]{--tw-gradient-to:#4b7dff var(--tw-gradient-to-position);}
.to-\[\#4B7DFF\]\/20{--tw-gradient-to:rgba(75,125,255,.2) var(--tw-gradient-to-position);}
.to-\[\#4B7DFF\]\/5{--tw-gradient-to:rgba(75,125,255,.05) var(--tw-gradient-to-position);}
.to-\[\#5081FF\]{--tw-gradient-to:#5081ff var(--tw-gradient-to-position);}
.to-\[\#5081FF\]\/10{--tw-gradient-to:rgba(80,129,255,.1) var(--tw-gradient-to-position);}
.to-\[\#5081FF\]\/20{--tw-gradient-to:rgba(80,129,255,.2) var(--tw-gradient-to-position);}
.to-\[\#6366F1\]{--tw-gradient-to:#6366f1 var(--tw-gradient-to-position);}
.to-\[\#6B46C1\]{--tw-gradient-to:#6b46c1 var(--tw-gradient-to-position);}
.to-\[\#7B1FA2\]{--tw-gradient-to:#7b1fa2 var(--tw-gradient-to-position);}
.to-\[\#8B5CF6\]{--tw-gradient-to:#8b5cf6 var(--tw-gradient-to-position);}
.to-\[\#9C27B0\]\/20{--tw-gradient-to:rgba(156,39,176,.2) var(--tw-gradient-to-position);}
.to-\[\#EF4444\]{--tw-gradient-to:#ef4444 var(--tw-gradient-to-position);}
.to-\[\#FF5252\]{--tw-gradient-to:#ff5252 var(--tw-gradient-to-position);}
.to-\[\#FF6B6B\]\/20{--tw-gradient-to:hsla(0,100%,71%,.2) var(--tw-gradient-to-position);}
.to-\[\#FF8C00\]{--tw-gradient-to:#ff8c00 var(--tw-gradient-to-position);}
.to-\[\#FFA500\]{--tw-gradient-to:orange var(--tw-gradient-to-position);}
.to-\[\#FFD700\]\/20{--tw-gradient-to:rgba(255,215,0,.2) var(--tw-gradient-to-position);}
.to-black{--tw-gradient-to:#000 var(--tw-gradient-to-position);}
.to-black\/90{--tw-gradient-to:rgba(0,0,0,.9) var(--tw-gradient-to-position);}
.to-blue-400{--tw-gradient-to:#60a5fa var(--tw-gradient-to-position);}
.to-blue-500{--tw-gradient-to:#3b82f6 var(--tw-gradient-to-position);}
.to-blue-500\/20{--tw-gradient-to:rgba(59,130,246,.2) var(--tw-gradient-to-position);}
.to-blue-500\/30{--tw-gradient-to:rgba(59,130,246,.3) var(--tw-gradient-to-position);}
.to-blue-500\/5{--tw-gradient-to:rgba(59,130,246,.05) var(--tw-gradient-to-position);}
.to-cyan-400{--tw-gradient-to:#22d3ee var(--tw-gradient-to-position);}
.to-emerald-500{--tw-gradient-to:#10b981 var(--tw-gradient-to-position);}
.to-emerald-500\/20{--tw-gradient-to:rgba(16,185,129,.2) var(--tw-gradient-to-position);}
.to-emerald-600{--tw-gradient-to:#059669 var(--tw-gradient-to-position);}
.to-fuchsia-400{--tw-gradient-to:#e879f9 var(--tw-gradient-to-position);}
.to-gaming-blue{--tw-gradient-to:#5081ff var(--tw-gradient-to-position);}
.to-gaming-dark{--tw-gradient-to:#0f172a var(--tw-gradient-to-position);}
.to-gaming-dark\/80{--tw-gradient-to:rgba(15,23,42,.8) var(--tw-gradient-to-position);}
.to-gaming-deep-blue{--tw-gradient-to:#3463db var(--tw-gradient-to-position);}
.to-gaming-orange{--tw-gradient-to:#ff8c00 var(--tw-gradient-to-position);}
.to-gaming-slate{--tw-gradient-to:#1e293b var(--tw-gradient-to-position);}
.to-gaming-violet{--tw-gradient-to:#9c27b0 var(--tw-gradient-to-position);}
.to-gray-700{--tw-gradient-to:#374151 var(--tw-gradient-to-position);}
.to-gray-800{--tw-gradient-to:#1f2937 var(--tw-gradient-to-position);}
.to-gray-800\/90{--tw-gradient-to:rgba(31,41,55,.9) var(--tw-gradient-to-position);}
.to-gray-900{--tw-gradient-to:#111827 var(--tw-gradient-to-position);}
.to-gray-900\/50{--tw-gradient-to:rgba(17,24,39,.5) var(--tw-gradient-to-position);}
.to-gray-900\/60{--tw-gradient-to:rgba(17,24,39,.6) var(--tw-gradient-to-position);}
.to-gray-900\/80{--tw-gradient-to:rgba(17,24,39,.8) var(--tw-gradient-to-position);}
.to-gray-900\/95{--tw-gradient-to:rgba(17,24,39,.95) var(--tw-gradient-to-position);}
.to-indigo-900{--tw-gradient-to:#312e81 var(--tw-gradient-to-position);}
.to-orange-400{--tw-gradient-to:#fb923c var(--tw-gradient-to-position);}
.to-orange-500{--tw-gradient-to:#f97316 var(--tw-gradient-to-position);}
.to-orange-500\/10{--tw-gradient-to:rgba(249,115,22,.1) var(--tw-gradient-to-position);}
.to-purple-500{--tw-gradient-to:#a855f7 var(--tw-gradient-to-position);}
.to-purple-500\/10{--tw-gradient-to:rgba(168,85,247,.1) var(--tw-gradient-to-position);}
.to-purple-600{--tw-gradient-to:#9333ea var(--tw-gradient-to-position);}
.to-red-600{--tw-gradient-to:#dc2626 var(--tw-gradient-to-position);}
.to-red-700{--tw-gradient-to:#b91c1c var(--tw-gradient-to-position);}
.to-teal-500{--tw-gradient-to:#14b8a6 var(--tw-gradient-to-position);}
.to-transparent{--tw-gradient-to:transparent var(--tw-gradient-to-position);}
.to-violet-400{--tw-gradient-to:#a78bfa var(--tw-gradient-to-position);}
.bg-contain{background-size:contain;}
.bg-cover{background-size:cover;}
.bg-clip-text{
  -webkit-background-clip:text;
          background-clip:text;}
.bg-center{background-position:50%;background-position:50%;}
.bg-left-top{background-position:0 0;}
.bg-right{background-position:100%;}
.bg-repeat{background-repeat:repeat;}
.bg-no-repeat{background-repeat:no-repeat;}
.bg-origin-border{background-origin:border-box;}
.fill-white{fill:#fff;}
.object-contain{-o-object-fit:contain;object-fit:contain;}
.object-cover{-o-object-fit:cover;object-fit:cover;}
.\!p-0{padding:0px !important;padding:0!important;}
.\!p-6{padding:1.5rem!important;}
.p-0{padding:0px;padding:0;}
.p-1{padding:0.25rem;}
.p-1\.5{padding:0.375rem;}
.p-10{padding:2.5rem;}
.p-2{padding:0.5rem;padding:.5rem;}
.p-3{padding:0.75rem;padding:.75rem;}
.p-4{padding:1rem;}
.p-5{padding:1.25rem;}
.p-6{padding:1.5rem;}
.p-7{padding:1.75rem;}
.p-8{padding:2rem;}
.p-9{padding:2.25rem;}
.p-\[16px\]{padding:16px;}
.p-\[24px\]{padding:24px;}
.p-\[4px\]{padding:4px;}
.p-px{padding:1px;}
.\!px-2{padding-left:0.5rem !important;padding-right:0.5rem !important;}
.\!px-\[20px\]{padding-left:20px!important;padding-right:20px!important;}
.px-0{padding-left:0px;padding-right:0px;}
.px-1{padding-left:0.25rem;padding-right:0.25rem;}
.px-10{padding-left:2.5rem;padding-right:2.5rem;}
.px-11{padding-left:2.75rem;padding-right:2.75rem;}
.px-12{padding-left:3rem;padding-right:3rem;}
.px-14{padding-left:3.5rem;padding-right:3.5rem;}
.px-16{padding-left:4rem;padding-right:4rem;}
.px-2{padding-left:0.5rem;padding-left:.5rem;padding-right:0.5rem;padding-right:.5rem;}
.px-20{padding-left:5rem;padding-right:5rem;}
.px-3{padding-left:0.75rem;padding-left:.75rem;padding-right:0.75rem;padding-right:.75rem;}
.px-4{padding-left:1rem;padding-right:1rem;}
.px-40{padding-left:10rem;padding-right:10rem;}
.px-5{padding-left:1.25rem;padding-right:1.25rem;}
.px-6{padding-left:1.5rem;padding-right:1.5rem;}
.px-7{padding-left:1.75rem;padding-right:1.75rem;}
.px-8{padding-left:2rem;padding-right:2rem;}
.px-9{padding-left:2.25rem;padding-right:2.25rem;}
.px-\[12px\]{padding-left:12px;padding-right:12px;}
.px-\[16px\]{padding-left:16px;padding-right:16px;}
.px-\[24px\]{padding-left:24px;padding-right:24px;}
.py-0{padding-bottom:0px;padding-bottom:0;padding-top:0px;padding-top:0;}
.py-0\.5{padding-bottom:0.125rem;padding-bottom:.125rem;padding-top:0.125rem;padding-top:.125rem;}
.py-1{padding-bottom:0.25rem;padding-top:0.25rem;}
.py-1\.5{padding-bottom:0.375rem;padding-top:0.375rem;}
.py-10{padding-bottom:2.5rem;padding-top:2.5rem;}
.py-12{padding-bottom:3rem;padding-top:3rem;}
.py-16{padding-bottom:4rem;padding-top:4rem;}
.py-2{padding-bottom:0.5rem;padding-bottom:.5rem;padding-top:0.5rem;padding-top:.5rem;}
.py-2\.5{padding-bottom:0.625rem;padding-bottom:.625rem;padding-top:0.625rem;padding-top:.625rem;}
.py-20{padding-bottom:5rem;padding-top:5rem;}
.py-3{padding-bottom:0.75rem;padding-bottom:.75rem;padding-top:0.75rem;padding-top:.75rem;}
.py-4{padding-bottom:1rem;padding-top:1rem;}
.py-5{padding-bottom:1.25rem;padding-top:1.25rem;}
.py-6{padding-bottom:1.5rem;padding-top:1.5rem;}
.py-7{padding-bottom:1.75rem;padding-top:1.75rem;}
.py-8{padding-bottom:2rem;padding-top:2rem;}
.py-9{padding-bottom:2.25rem;padding-top:2.25rem;}
.py-\[11px\]{padding-bottom:11px;padding-top:11px;}
.py-\[12px\]{padding-bottom:12px;padding-top:12px;}
.py-\[14px\]{padding-bottom:14px;padding-top:14px;}
.py-\[16px\]{padding-bottom:16px;padding-top:16px;}
.py-\[9px\]{padding-bottom:9px;padding-top:9px;}
.py-px{padding-bottom:1px;padding-top:1px;}
.pb-0{padding-bottom:0px;padding-bottom:0;}
.pb-10{padding-bottom:2.5rem;}
.pb-12{padding-bottom:3rem;}
.pb-16{padding-bottom:4rem;}
.pb-2{padding-bottom:0.5rem;padding-bottom:.5rem;}
.pb-20{padding-bottom:5rem;}
.pb-24{padding-bottom:6rem;}
.pb-3{padding-bottom:0.75rem;}
.pb-4{padding-bottom:1rem;}
.pb-5{padding-bottom:1.25rem;}
.pb-8{padding-bottom:2rem;}
.pb-\[6px\]{padding-bottom:6px;}
.pb-\[7px\]{padding-bottom:7px;}
.pb-\[8px\]{padding-bottom:8px;}
.pe-1{padding-inline-end:0.25rem;}
.pe-32{padding-inline-end:8rem;}
.pl-10{padding-left:2.5rem;}
.pl-2{padding-left:0.5rem;padding-left:.5rem;}
.pl-3{padding-left:0.75rem;}
.pl-4{padding-left:1rem;}
.pl-8{padding-left:2rem;}
.pr-2{padding-right:0.5rem;}
.pr-3{padding-right:0.75rem;padding-right:.75rem;}
.pr-4{padding-right:1rem;}
.pr-9{padding-right:2.25rem;}
.ps-10{padding-inline-start:2.5rem;}
.ps-5{padding-inline-start:1.25rem;}
.pt-0{padding-top:0px;}
.pt-1{padding-top:0.25rem;padding-top:.25rem;}
.pt-12{padding-top:3rem;}
.pt-14{padding-top:3.5rem;}
.pt-2{padding-top:0.5rem;}
.pt-3{padding-top:0.75rem;}
.pt-4{padding-top:1rem;}
.pt-5{padding-top:1.25rem;}
.pt-6{padding-top:1.5rem;}
.pt-\[10px\]{padding-top:10px;}
.pt-\[8px\]{padding-top:8px;}
.text-left{text-align:left;}
.text-center{text-align:center;}
.text-right{text-align:right;}
.text-justify{text-align:justify;}
.text-start{text-align:start;}
.text-end{text-align:end;}
.align-top{vertical-align:top;}
.align-middle{vertical-align:middle;}
.align-text-bottom{vertical-align:text-bottom;}
.align-\[-\.125em\]{vertical-align:-.125em;}
.font-league-spartan{font-family:League Spartan,Arial,sans-serif;
    font-family:League Spartan,Arial,ui-sans-serif,system-ui,sans-serif;}
.font-mono{font-family:ui-monospace,SFMono-Regular,Menlo,Monaco,Consolas,Liberation Mono,Courier New,monospace;}
.font-sans{font-family:ui-sans-serif,system-ui,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji;}
.font-signika{font-family:Signika,sans-serif;
    font-family:Signika,sans-serif;}
.font-spartan{font-family:League Spartan,Arial,sans-serif;
    font-family:League Spartan,Arial,ui-sans-serif,system-ui,sans-serif;}
.\!text-\[12px\]{font-size:12px!important;}
.text-2xl{font-size:1.5rem;line-height:2rem;}
.text-3xl{font-size:1.875rem;line-height:2.25rem;}
.text-4xl{font-size:2.25rem;line-height:2.5rem;}
.text-5xl{font-size:3rem;line-height:1;}
.text-6xl{font-size:3.75rem;line-height:1;}
.text-\[0\.813rem\]{font-size:0.813rem;}
.text-\[10px\]{font-size:10px;}
.text-\[11px\]{font-size:11px;}
.text-\[12px\]{font-size:12px;}
.text-\[14px\]{font-size:14px;}
.text-\[16px\]{font-size:16px;}
.text-\[20px\]{font-size:20px;}
.text-\[24px\]{font-size:24px;}
.text-\[28px\]{font-size:28px;}
.text-base{font-size:1rem;line-height:1.5rem;}
.text-lg{font-size:1.125rem;line-height:1.75rem;}
.text-lg\/none{font-size:1.125rem;line-height:1;}
.text-sm{font-size:0.875rem;font-size:.875rem;line-height:1.25rem;}
.text-xl{font-size:1.25rem;line-height:1.75rem;}
.text-xs{font-size:0.75rem;font-size:.75rem;line-height:1rem;}
.font-\[300\]{font-weight:300;}
.font-\[400\]{font-weight:400;}
.font-black{font-weight:900;}
.font-bold{font-weight:700;}
.font-extrabold{font-weight:800;}
.font-medium{font-weight:500;}
.font-normal{font-weight:400;}
.font-semibold{font-weight:600;}
.uppercase{text-transform:uppercase;}
.lowercase{text-transform:lowercase;}
.capitalize{text-transform:capitalize;}
.\!italic{font-style:italic!important;}
.italic{font-style:italic;}
.not-italic{font-style:normal;}
.ordinal{--tw-ordinal:ordinal;font-variant-numeric:var(--tw-ordinal) var(--tw-slashed-zero) var(--tw-numeric-figure) var(--tw-numeric-spacing) var(--tw-numeric-fraction);}
.leading-3{line-height:.75rem;}
.leading-4{line-height:1rem;}
.leading-5{line-height:1.25rem;}
.leading-6{line-height:1.5rem;}
.leading-\[12px\]{line-height:12px;}
.leading-\[13px\]{line-height:13px;}
.leading-\[14px\]{line-height:14px;}
.leading-\[16px\]{line-height:16px;}
.leading-\[17px\]{line-height:17px;}
.leading-\[28px\]{line-height:28px;}
.leading-none{line-height:1;}
.leading-normal{line-height:1.5;}
.leading-relaxed{line-height:1.625;}
.leading-snug{line-height:1.375;}
.leading-tight{line-height:1.25;}
.tracking-\[0\.2em\]{letter-spacing:0.2em;}
.tracking-tight{letter-spacing:-0.025em;}
.tracking-wide{letter-spacing:0.025em;}
.tracking-wider{letter-spacing:0.05em;}
.text-\[\#00FF88\]{--tw-text-opacity:1;color:rgb(0 255 136 / var(--tw-text-opacity, 1));}
.text-\[\#1a1640\]{--tw-text-opacity:1;color:rgb(26 22 64 / var(--tw-text-opacity, 1));}
.text-\[\#45ff02\]{--tw-text-opacity:1;color:rgb(69 255 2 / var(--tw-text-opacity, 1));}
.text-\[\#4B7DFF\]{color:rgb(75 125 255 / var(--tw-text-opacity, 1));--tw-text-opacity:1;color:rgb(75 125 255/var(--tw-text-opacity,1));}
.text-\[\#5081FF\]{color:rgb(80 129 255 / var(--tw-text-opacity, 1));--tw-text-opacity:1;color:rgb(80 129 255/var(--tw-text-opacity,1));}
.text-\[\#69B1FF\]{color:rgb(105 177 255 / var(--tw-text-opacity, 1));--tw-text-opacity:1;color:rgb(105 177 255/var(--tw-text-opacity,1));}
.text-\[\#9C27B0\]{--tw-text-opacity:1;color:rgb(156 39 176 / var(--tw-text-opacity, 1));}
.text-\[\#9F9BAB\]{color:rgb(159 155 171 / var(--tw-text-opacity, 1));--tw-text-opacity:1;color:rgb(159 155 171/var(--tw-text-opacity,1));}
.text-\[\#C7D2FE\]{--tw-text-opacity:1;color:rgb(199 210 254 / var(--tw-text-opacity, 1));}
.text-\[\#FF6B6B\]{--tw-text-opacity:1;color:rgb(255 107 107 / var(--tw-text-opacity, 1));}
.text-\[\#FFCA06\]{color:rgb(255 202 6 / var(--tw-text-opacity, 1));--tw-text-opacity:1;color:rgb(255 202 6/var(--tw-text-opacity,1));}
.text-\[\#FFD25F\]{color:rgb(255 210 95 / var(--tw-text-opacity, 1));--tw-text-opacity:1;color:rgb(255 210 95/var(--tw-text-opacity,1));}
.text-\[\#FFD700\]{--tw-text-opacity:1;color:rgb(255 215 0 / var(--tw-text-opacity, 1));}
.text-\[\#FFFFFF99\]{color:#ffffff99;}
.text-\[\#FFFFFFCC\]{color:#ffffffcc;}
.text-\[\#FFFFFF\]{color:rgb(255 255 255 / var(--tw-text-opacity, 1));--tw-text-opacity:1;color:rgb(255 255 255/var(--tw-text-opacity,1));}
.text-\[\#f8f8f8\]{color:rgb(248 248 248 / var(--tw-text-opacity, 1));--tw-text-opacity:1;color:rgb(248 248 248/var(--tw-text-opacity,1));}
.text-\[\#ff062e\]{--tw-text-opacity:1;color:rgb(255 6 46 / var(--tw-text-opacity, 1));}
.text-\[\#ff062e\]\/70{color:rgba(255,6,46,.7);}
.text-\[\#fff9\]{color:#fff9;}
.text-\[\#fff\]{color:rgb(255 255 255 / var(--tw-text-opacity, 1));--tw-text-opacity:1;color:rgb(255 255 255/var(--tw-text-opacity,1));}
.text-amber-400{--tw-text-opacity:1;color:rgb(251 191 36 / var(--tw-text-opacity, 1));}
.text-amber-500{--tw-text-opacity:1;color:rgb(245 158 11 / var(--tw-text-opacity, 1));}
.text-amber-600{--tw-text-opacity:1;color:rgb(217 119 6 / var(--tw-text-opacity, 1));}
.text-black{--tw-text-opacity:1;color:rgb(0 0 0 / var(--tw-text-opacity, 1));}
.text-blue-400{--tw-text-opacity:1;color:rgb(96 165 250 / var(--tw-text-opacity, 1));}
.text-blue-600{--tw-text-opacity:1;color:rgb(37 99 235 / var(--tw-text-opacity, 1));}
.text-cyan-500{--tw-text-opacity:1;color:rgb(6 182 212 / var(--tw-text-opacity, 1));}
.text-emerald-400{--tw-text-opacity:1;color:rgb(52 211 153 / var(--tw-text-opacity, 1));}
.text-emerald-500{--tw-text-opacity:1;color:rgb(16 185 129 / var(--tw-text-opacity, 1));}
.text-gaming-blue{--tw-text-opacity:1;color:rgb(80 129 255 / var(--tw-text-opacity, 1)); color:#5081ff !important;}
.text-gaming-dark{--tw-text-opacity:1;color:rgb(15 23 42 / var(--tw-text-opacity, 1)); color:#0f172a !important;}
.text-gaming-deep-blue{--tw-text-opacity:1;color:rgb(52 99 219 / var(--tw-text-opacity, 1)); color:#3463db !important;}
.text-gaming-gold{--tw-text-opacity:1;color:rgb(255 215 0 / var(--tw-text-opacity, 1)); color:gold !important;}
.text-gaming-green{--tw-text-opacity:1;color:rgb(0 255 136 / var(--tw-text-opacity, 1)); color:#0f8 !important;}
.text-gaming-light-blue{--tw-text-opacity:1;color:rgb(75 125 255 / var(--tw-text-opacity, 1)); color:#4b7dff !important;}
.text-gaming-navy{--tw-text-opacity:1;color:rgb(19 17 46 / var(--tw-text-opacity, 1)); color:#13112e !important;}
.text-gaming-orange{--tw-text-opacity:1;color:rgb(255 140 0 / var(--tw-text-opacity, 1)); color:#ff8c00 !important;}
.text-gaming-purple{--tw-text-opacity:1;color:rgb(39 36 80 / var(--tw-text-opacity, 1)); color:#272450 !important;}
.text-gaming-red{--tw-text-opacity:1;color:rgb(255 107 107 / var(--tw-text-opacity, 1)); color:#ff6b6b !important;}
.text-gaming-slate{--tw-text-opacity:1;color:rgb(30 41 59 / var(--tw-text-opacity, 1)); color:#1e293b !important;}
.text-gaming-violet{--tw-text-opacity:1;color:rgb(156 39 176 / var(--tw-text-opacity, 1)); color:#9c27b0 !important;}
.text-gray-200{--tw-text-opacity:1;color:rgb(229 231 235 / var(--tw-text-opacity, 1));}
.text-gray-300{--tw-text-opacity:1;color:rgb(209 213 219 / var(--tw-text-opacity, 1));}
.text-gray-400{--tw-text-opacity:1;color:rgb(156 163 175 / var(--tw-text-opacity, 1));}
.text-gray-500{--tw-text-opacity:1;color:rgb(107 114 128 / var(--tw-text-opacity, 1));}
.text-gray-600{--tw-text-opacity:1;color:rgb(75 85 99 / var(--tw-text-opacity, 1));}
.text-gray-700{--tw-text-opacity:1;color:rgb(55 65 81 / var(--tw-text-opacity, 1));}
.text-gray-800{--tw-text-opacity:1;color:rgb(31 41 55 / var(--tw-text-opacity, 1));}
.text-gray-900{--tw-text-opacity:1;color:rgb(17 24 39 / var(--tw-text-opacity, 1));}
.text-gray-950{--tw-text-opacity:1;color:rgb(3 7 18 / var(--tw-text-opacity, 1));}
.text-green-400{--tw-text-opacity:1;color:rgb(74 222 128 / var(--tw-text-opacity, 1));}
.text-indigo-600{--tw-text-opacity:1;color:rgb(79 70 229 / var(--tw-text-opacity, 1));}
.text-mandy-500{--tw-text-opacity:1;color:rgb(229 62 62 / var(--tw-text-opacity, 1));}
.text-neutral-300{--tw-text-opacity:1;color:rgb(212 212 212 / var(--tw-text-opacity, 1));}
.text-orange-400{--tw-text-opacity:1;color:rgb(251 146 60 / var(--tw-text-opacity, 1));}
.text-purple-400{--tw-text-opacity:1;color:rgb(192 132 252 / var(--tw-text-opacity, 1));}
.text-red-400{--tw-text-opacity:1;color:rgb(248 113 113 / var(--tw-text-opacity, 1));}
.text-red-500{--tw-text-opacity:1;color:rgb(239 68 68 / var(--tw-text-opacity, 1));}
.text-sky-500{--tw-text-opacity:1;color:rgb(14 165 233 / var(--tw-text-opacity, 1));}
.text-slate-400{--tw-text-opacity:1;color:rgb(148 163 184 / var(--tw-text-opacity, 1));}
.text-slate-500{--tw-text-opacity:1;color:rgb(100 116 139 / var(--tw-text-opacity, 1));}
.text-slate-900{--tw-text-opacity:1;color:rgb(15 23 42 / var(--tw-text-opacity, 1));}
.text-teal-500{--tw-text-opacity:1;color:rgb(20 184 166 / var(--tw-text-opacity, 1));}
.text-transparent{color:transparent;}
.text-white{--tw-text-opacity:1;color:rgb(255 255 255 / var(--tw-text-opacity, 1));}
.text-white\/70{color:hsla(0,0%,100%,.7);}
.text-yellow-300{--tw-text-opacity:1;color:rgb(253 224 71 / var(--tw-text-opacity, 1));}
.text-yellow-400{--tw-text-opacity:1;color:rgb(250 204 21 / var(--tw-text-opacity, 1));}
.text-yellow-500{--tw-text-opacity:1;color:rgb(234 179 8 / var(--tw-text-opacity, 1));}
.underline{text-decoration-line:underline;}
.overline{text-decoration-line:overline;}
.line-through{text-decoration-line:line-through;}
.underline-offset-4{text-underline-offset:4px;}
.antialiased{-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;}
.placeholder-\[\#FFFFFFCC\]\/50::-moz-placeholder{color:hsla(0,0%,100%,.5);}
.placeholder-\[\#FFFFFFCC\]\/50::placeholder{color:hsla(0,0%,100%,.5);}
.opacity-0{opacity:0;}
.opacity-10{opacity:0.1;}
.opacity-100{opacity:1;}
.opacity-20{opacity:0.2;}
.opacity-30{opacity:0.3;opacity:.3;}
.opacity-5{opacity:0.05;}
.opacity-50{opacity:0.5;opacity:.5;}
.opacity-55{opacity:0.55;}
.opacity-60{opacity:0.6;}
.opacity-65{opacity:0.65;opacity:.65;}
.opacity-70{opacity:0.7;}
.opacity-75{opacity:0.75;}
.opacity-80{opacity:0.8;}
.opacity-90{opacity:0.9;}
.opacity-\[0\.7\]{opacity:0.7;}
.mix-blend-luminosity{mix-blend-mode:luminosity;}
.\!shadow-none{box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow) !important;--tw-shadow:0 0 #0000!important;--tw-shadow-colored:0 0 #0000!important;box-shadow:var(--tw-ring-offset-shadow,0 0 #0000),var(--tw-ring-shadow,0 0 #0000),var(--tw-shadow)!important;}
.shadow{--tw-shadow:0 1px 3px 0 rgba(0,0,0,.1), 0 1px 2px -1px rgba(0,0,0,.1);--tw-shadow-colored:0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);--tw-shadow:0 1px 3px 0 rgb(0 0 0/0.1),0 1px 2px -1px rgb(0 0 0/0.1);--tw-shadow-colored:0 1px 3px 0 var(--tw-shadow-color),0 1px 2px -1px var(--tw-shadow-color);}
.shadow,.shadow-2xl{box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);}
.shadow-2xl{--tw-shadow:0 25px 50px -12px rgba(0,0,0,.25);--tw-shadow-colored:0 25px 50px -12px var(--tw-shadow-color);}
.shadow-lg{--tw-shadow:0 10px 15px -3px rgba(0,0,0,.1), 0 4px 6px -4px rgba(0,0,0,.1);--tw-shadow-colored:0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);}
.shadow-lg,.shadow-md{box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);}
.shadow-md{--tw-shadow:0 4px 6px -1px rgba(0,0,0,.1), 0 2px 4px -2px rgba(0,0,0,.1);--tw-shadow-colored:0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);}
.shadow-none{--tw-shadow:0 0 #0000;--tw-shadow-colored:0 0 #0000;}
.shadow-none,.shadow-sm{box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);}
.shadow-sm{--tw-shadow:0 1px 2px 0 rgba(0,0,0,.05);--tw-shadow-colored:0 1px 2px 0 var(--tw-shadow-color);}
.shadow-xl{--tw-shadow:0 20px 25px -5px rgba(0,0,0,.1), 0 8px 10px -6px rgba(0,0,0,.1);--tw-shadow-colored:0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);}
.shadow-\[\#06B6D4\]\/30{--tw-shadow-color:rgba(6,182,212,.3);--tw-shadow:var(--tw-shadow-colored);}
.shadow-\[\#06B6D4\]\/50{--tw-shadow-color:rgba(6,182,212,.5);--tw-shadow:var(--tw-shadow-colored);}
.shadow-\[\#10B981\]\/30{--tw-shadow-color:rgba(16,185,129,.3);--tw-shadow:var(--tw-shadow-colored);}
.shadow-\[\#5081FF\]\/20{--tw-shadow-color:rgba(80,129,255,.2);--tw-shadow:var(--tw-shadow-colored);}
.shadow-\[\#5081FF\]\/30{--tw-shadow-color:rgba(80,129,255,.3);--tw-shadow:var(--tw-shadow-colored);}
.shadow-\[\#5081FF\]\/40{--tw-shadow-color:rgba(80,129,255,.4);--tw-shadow:var(--tw-shadow-colored);}
.shadow-\[\#6366F1\]\/25{--tw-shadow-color:rgba(99,102,241,.25);--tw-shadow:var(--tw-shadow-colored);}
.shadow-\[\#6366F1\]\/30{--tw-shadow-color:rgba(99,102,241,.3);--tw-shadow:var(--tw-shadow-colored);}
.shadow-\[\#6366F1\]\/40{--tw-shadow-color:rgba(99,102,241,.4);--tw-shadow:var(--tw-shadow-colored);}
.shadow-\[\#6366F1\]\/50{--tw-shadow-color:rgba(99,102,241,.5);--tw-shadow:var(--tw-shadow-colored);}
.shadow-\[\#6366F1\]\/60{--tw-shadow-color:rgba(99,102,241,.6);--tw-shadow:var(--tw-shadow-colored);}
.shadow-\[\#8B5CF6\]\/30{--tw-shadow-color:rgba(139,92,246,.3);--tw-shadow:var(--tw-shadow-colored);}
.shadow-\[\#8B5CF6\]\/50{--tw-shadow-color:rgba(139,92,246,.5);--tw-shadow:var(--tw-shadow-colored);}
.shadow-\[\#EF4444\]\/25{--tw-shadow-color:rgba(239,68,68,.25);--tw-shadow:var(--tw-shadow-colored);}
.shadow-\[\#F59E0B\]\/25{--tw-shadow-color:rgba(245,158,11,.25);--tw-shadow:var(--tw-shadow-colored);}
.shadow-blue-500{--tw-shadow-color:#3b82f6;--tw-shadow:var(--tw-shadow-colored);}
.shadow-blue-500\/25{--tw-shadow-color:rgba(59,130,246,.25);--tw-shadow:var(--tw-shadow-colored);}
.shadow-emerald-500{--tw-shadow-color:#10b981;--tw-shadow:var(--tw-shadow-colored);}
.shadow-emerald-500\/25{--tw-shadow-color:rgba(16,185,129,.25);--tw-shadow:var(--tw-shadow-colored);}
.shadow-gaming-blue{--tw-shadow-color:#5081ff;--tw-shadow:var(--tw-shadow-colored);}
.shadow-orange-500{--tw-shadow-color:#f97316;--tw-shadow:var(--tw-shadow-colored);}
.shadow-orange-500\/25{--tw-shadow-color:rgba(249,115,22,.25);--tw-shadow:var(--tw-shadow-colored);}
.shadow-purple-500{--tw-shadow-color:#a855f7;--tw-shadow:var(--tw-shadow-colored);}
.shadow-purple-500\/25{--tw-shadow-color:rgba(168,85,247,.25);--tw-shadow:var(--tw-shadow-colored);}
.shadow-red-500{--tw-shadow-color:#ef4444;--tw-shadow:var(--tw-shadow-colored);}
.shadow-red-500\/25{--tw-shadow-color:rgba(239,68,68,.25);--tw-shadow:var(--tw-shadow-colored);}
.shadow-transparent{--tw-shadow-color:transparent;--tw-shadow:var(--tw-shadow-colored);}
.outline-none{outline:2px solid transparent;outline-offset:2px;}
.outline{outline-style:solid;}
.ring{--tw-ring-offset-shadow:var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);--tw-ring-shadow:var(--tw-ring-inset) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color);}
.ring,.ring-2{box-shadow:var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);}
.ring-2{--tw-ring-offset-shadow:var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);--tw-ring-shadow:var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);}
.ring-4{--tw-ring-offset-shadow:var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);--tw-ring-shadow:var(--tw-ring-inset) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color);box-shadow:var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);}
.ring-gaming-blue{--tw-ring-opacity:1;--tw-ring-color:rgb(80 129 255 / var(--tw-ring-opacity, 1));}
.ring-white{--tw-ring-opacity:1;--tw-ring-color:rgb(255 255 255 / var(--tw-ring-opacity, 1));}
.blur{--tw-blur:blur(8px);}
.blur,.blur-2xl{filter:var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);}
.blur-2xl{--tw-blur:blur(40px);}
.blur-3xl{--tw-blur:blur(64px);}
.blur-3xl,.blur-sm{filter:var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);}
.blur-sm{--tw-blur:blur(4px);}
.blur-xl{--tw-blur:blur(24px);}
.blur-xl,.brightness-110{filter:var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);}
.brightness-110{--tw-brightness:brightness(1.1);}
.drop-shadow{--tw-drop-shadow:drop-shadow(0 1px 2px rgba(0,0,0,.1)) drop-shadow(0 1px 1px rgba(0,0,0,.06));}
.drop-shadow,.drop-shadow-lg{filter:var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);}
.drop-shadow-lg{--tw-drop-shadow:drop-shadow(0 10px 8px rgba(0,0,0,.04)) drop-shadow(0 4px 3px rgba(0,0,0,.1));}
.grayscale{--tw-grayscale:grayscale(100%);filter:var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);}
.\!invert{--tw-invert:invert(100%) !important;filter:var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow) !important;}
.invert{--tw-invert:invert(100%);}
.invert,.saturate-100{filter:var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);}
.saturate-100{--tw-saturate:saturate(1);}
.saturate-50{--tw-saturate:saturate(.5);filter:var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);}
.\!filter{filter:var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow) !important;}
.filter{filter:var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);}
.backdrop-blur-\[2px\]{--tw-backdrop-blur:blur(2px);}
.backdrop-blur-\[2px\],.backdrop-blur-lg{-webkit-backdrop-filter:var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);backdrop-filter:var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);}
.backdrop-blur-lg{--tw-backdrop-blur:blur(16px);}
.backdrop-blur-md{--tw-backdrop-blur:blur(12px);}
.backdrop-blur-md,.backdrop-blur-sm{-webkit-backdrop-filter:var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);backdrop-filter:var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);}
.backdrop-blur-sm{--tw-backdrop-blur:blur(4px);}
.backdrop-filter{-webkit-backdrop-filter:var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);backdrop-filter:var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);}
.transition{transition-duration:150ms;transition-duration:.15s;transition-property:color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;transition-property:color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;transition-property:color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;transition-property:color,background-color,border-color,text-decoration-color,fill,stroke,opacity,box-shadow,transform,filter,-webkit-backdrop-filter;transition-property:color,background-color,border-color,text-decoration-color,fill,stroke,opacity,box-shadow,transform,filter,backdrop-filter;transition-property:color,background-color,border-color,text-decoration-color,fill,stroke,opacity,box-shadow,transform,filter,backdrop-filter,-webkit-backdrop-filter;transition-timing-function:cubic-bezier(0.4, 0, 0.2, 1);transition-timing-function:cubic-bezier(.4,0,.2,1);}
.transition-\[height\]{transition-duration:150ms;transition-property:height;transition-timing-function:cubic-bezier(0.4, 0, 0.2, 1);}
.transition-\[opacity\2c margin\]{transition-property:opacity,margin;}
.transition-\[opacity\2c margin\],.transition-all{transition-duration:150ms;transition-timing-function:cubic-bezier(0.4, 0, 0.2, 1);}
.transition-all{transition-duration:.15s;transition-property:all;transition-timing-function:cubic-bezier(.4,0,.2,1);}
.transition-colors{transition-property:color, background-color, border-color, text-decoration-color, fill, stroke;}
.transition-colors,.transition-opacity{transition-duration:150ms;transition-timing-function:cubic-bezier(0.4, 0, 0.2, 1);}
.transition-opacity{transition-duration:.15s;transition-property:opacity;transition-timing-function:cubic-bezier(.4,0,.2,1);}
.transition-transform{transition-duration:150ms;transition-property:transform;transition-timing-function:cubic-bezier(0.4, 0, 0.2, 1);}
.delay-500{transition-delay:500ms;}
.duration-200{transition-duration:200ms;transition-duration:.2s;}
.duration-300{transition-duration:300ms;transition-duration:.3s;}
.duration-500{transition-duration:500ms;}
.duration-700{transition-duration:700ms;}
.ease-in{transition-timing-function:cubic-bezier(0.4, 0, 1, 1);}
.ease-in-out{transition-timing-function:cubic-bezier(0.4, 0, 0.2, 1);transition-timing-function:cubic-bezier(.4,0,.2,1);}
.ease-out{transition-timing-function:cubic-bezier(0, 0, 0.2, 1);}
.shadow-gaming{ box-shadow:0 4px 12px rgba(80,129,255,.3) !important;
  }
.shadow-gaming-lg{ box-shadow:0 8px 24px rgba(80,129,255,.4) !important;
  }
.shadow-gaming-xl{ box-shadow:0 12px 36px rgba(80,129,255,.5) !important;
  }
.glow-gaming-blue{
    box-shadow:0 0 20px rgba(80,129,255,.5);
  }
.glow-gaming-purple{
    box-shadow:0 0 20px rgba(156,39,176,.5);
  }
.glow-gaming-green{
    box-shadow:0 0 20px rgba(0,255,136,.5);
  }
.text-glow{
    text-shadow:0 0 10px currentColor;
  }
.text-glow-lg{
    text-shadow:0 0 20px currentColor, 0 0 40px currentColor;
  }
.border-glow{
    border-color:currentColor;
    box-shadow:0 0 10px currentColor;
  }
.bg-gaming-gradient{
    background:linear-gradient(135deg, #0f172a, #1e293b 50%, #0f172a);
  }
.bg-gaming-gradient-blue{
    background:linear-gradient(135deg, #5081ff, #3463db 50%, #4b7dff);
  }
.bg-gaming-gradient-purple{
    background:linear-gradient(135deg, #272450, #9c27b0 50%, #272450);
  }
.clip-triangle-tl{
    clip-path:polygon(0 0, 100% 0, 0 100%);
  }
.clip-triangle-tr{
    clip-path:polygon(0 0, 100% 0, 100% 100%);
  }
.clip-triangle-bl{
    clip-path:polygon(0 0, 0 100%, 100% 100%);
  }
.clip-triangle-br{
    clip-path:polygon(100% 0, 100% 100%, 0 100%);
  }
.backdrop-gaming{
    -webkit-backdrop-filter:blur(8px) saturate(180%);
            backdrop-filter:blur(8px) saturate(180%);
  }
.scrollbar-gaming{
    scrollbar-color:#5081ff #1e293b;
    scrollbar-width:thin;
  }
.scrollbar-gaming::-webkit-scrollbar{
    width:8px;
  }
.scrollbar-gaming::-webkit-scrollbar-track{
    background:#1e293b;
    border-radius:4px;
  }
.scrollbar-gaming::-webkit-scrollbar-thumb{
    background:#5081ff;
    border-radius:4px;
  }
.scrollbar-gaming::-webkit-scrollbar-thumb:hover{
    background:#3463db;
  }
.font-gaming{
    font-family:Signika,ui-sans-serif,system-ui,sans-serif;
  }
.font-gaming-display{
    font-family:League Spartan,Signika,ui-sans-serif,system-ui,sans-serif;
  }
._box-number-account_1eg83_36{border-radius:.375rem;color:rgb(255 255 255 / var(--tw-text-opacity, 1));display:inline-block;font-size:.75rem;font-weight:700;line-height:1rem;--tw-text-opacity:1;color:rgb(255 255 255/var(--tw-text-opacity,1));}
._short-account-border_1eg83_17{border-radius:.5rem;display:block;overflow:hidden;position:relative;}
.gaming-image-gallery ._short-account-border_1eg83_17{
    align-items:center;
    display:flex;
    height:auto;
    justify-content:center;
  
    max-height:85vh;
    position:relative;
    visibility:visible;
    z-index:1;
  }
._image-container_1eg83_26{border-radius:.5rem;display:block;height:12rem;overflow:hidden;position:relative;width:100%;}
.gaming-image-gallery ._image-container_1eg83_26{
    align-items:center;
    display:flex;
    height:auto;
    justify-content:center;
  
    max-height:85vh;
    position:relative;
    visibility:visible;
    z-index:1;
  }
._image-container_1eg83_26 img{height:100%;-o-object-fit:cover;object-fit:cover;transition-duration:300ms;transition-duration:.3s;transition-property:transform;transition-timing-function:cubic-bezier(0.4, 0, 0.2, 1);width:100%;}
._image-container_1eg83_26 img:hover{--tw-scale-x:1.05;--tw-scale-y:1.05;transform:translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));}
._button-icon_1eg83_47{--tw-bg-opacity:1;align-items:center;background-color:rgb(80 129 255 / var(--tw-bg-opacity, 1)); background-color:#5081ff;border-radius:.5rem;color:rgb(255 255 255 / var(--tw-text-opacity, 1));display:inline-flex;font-size:.875rem;font-weight:600;justify-content:center;line-height:1.25rem;padding-bottom:.5rem;padding-left:1rem;padding-right:1rem;padding-top:.5rem;transition-duration:300ms;transition-property:color, background-color, border-color, text-decoration-color, fill, stroke;transition-timing-function:cubic-bezier(0.4, 0, 0.2, 1);width:100%;--tw-text-opacity:1;color:rgb(255 255 255/var(--tw-text-opacity,1));transition-duration:.3s;}
._button-icon_1eg83_47:hover{--tw-bg-opacity:1;background-color:rgb(52 99 219 / var(--tw-bg-opacity, 1));}
._button-icon_1eg83_47 svg{margin-right:0.5rem;}
.\[--placement\:bottom-left\]{--placement:bottom-left;}
.\[--placement\:bottom-right\]{--placement:bottom-right;}
.enhanced-gaming-table{
  background:transparent;
  border-collapse:separate;
  border-radius:12px;
  border-spacing:0;
  box-shadow:0 8px 32px rgba(80,129,255,.15);
  font-family:Signika,sans-serif;
  overflow:hidden;
}
.gaming-status-badge{
  align-items:center;
  border:1px solid;
  border-radius:6px;
  display:inline-flex;
  font-family:Signika,sans-serif;
  font-size:11px;
  font-weight:600;
  letter-spacing:0.5px;
  padding:4px 8px;
  text-transform:uppercase;
  transition:all 0.3s ease;
}
.gaming-status-success{
  background:linear-gradient(135deg, rgba(16,185,129,.2), rgba(5,150,105,.2));
  border-color:rgba(16,185,129,.3);
  box-shadow:0 2px 8px rgba(16,185,129,.2);
  color:#10b981;
}
.gaming-status-warning{
  background:linear-gradient(135deg, rgba(245,158,11,.2), rgba(217,119,6,.2));
  border-color:rgba(245,158,11,.3);
  box-shadow:0 2px 8px rgba(245,158,11,.2);
  color:#f59e0b;
}
.gaming-status-danger{
  background:linear-gradient(135deg, rgba(239,68,68,.2), rgba(220,38,38,.2));
  border-color:rgba(239,68,68,.3);
  box-shadow:0 2px 8px rgba(239,68,68,.2);
  color:#ef4444;
}
.gaming-status-info{
  background:linear-gradient(135deg, rgba(59,130,246,.2), rgba(37,99,235,.2));
  border-color:rgba(59,130,246,.3);
  box-shadow:0 2px 8px rgba(59,130,246,.2);
  color:#3b82f6;
}
.enhanced-gaming-table thead tr{
  background:linear-gradient(135deg, rgba(15,23,42,.95), rgba(30,41,59,.95) 50%, rgba(15,23,42,.95));
  border-bottom:2px solid rgba(80,129,255,.4);
  position:relative;
}
.enhanced-gaming-table th{
  background:transparent;
  border:none;
  color:#fff;
  font-size:12px;
  font-weight:700;
  letter-spacing:1px;
  padding:16px 12px;
  position:relative;
  text-align:left;
  text-transform:uppercase;
  white-space:nowrap;
}
.enhanced-gaming-table th:after{
  background:linear-gradient(90deg, transparent, rgba(80,129,255,.5), transparent);
  bottom:0;
  content:"";
  height:1px;
  left:50%;
  position:absolute;
  transform:translateX(-50%);
  width:60%;
}
.enhanced-gaming-table tbody tr{
  background:linear-gradient(135deg, rgba(15,23,42,.4), rgba(30,41,59,.4));
  border-bottom:1px solid rgba(80,129,255,.1);
  position:relative;
  transition:all 0.3s ease;
}
.enhanced-gaming-table tbody tr:nth-child(2n){
  background:linear-gradient(135deg, rgba(30,41,59,.3), rgba(15,23,42,.3));
}
.enhanced-gaming-table tbody tr:hover{
  background:linear-gradient(135deg, rgba(80,129,255,.15), rgba(52,99,219,.15));
  border-color:rgba(80,129,255,.3);
  box-shadow:0 4px 16px rgba(80,129,255,.2);
  transform:translateY(-1px);
}
.enhanced-gaming-table td{
  border:none;
  color:#ffffffcc;
  font-size:13px;
  font-weight:500;
  padding:14px 12px;
  transition:all 0.3s ease;
  vertical-align:middle;
}
.enhanced-gaming-table tbody tr:hover td{
  color:#fff;
}
.enhanced-gaming-table tbody tr:after{
  border-color:transparent rgba(80,129,255,.2) transparent transparent;
  border-style:solid;
  border-width:0 8px 8px 0;
  content:"";
  height:0;
  opacity:0;
  position:absolute;
  right:0;
  top:0;
  transition:opacity 0.3s ease;
  width:0;
}
.enhanced-gaming-table tbody tr:hover:after{
  opacity:1;
}
.datatable-pagination{
  background:linear-gradient(135deg, rgba(15,23,42,.6), rgba(30,41,59,.6));
  border:1px solid rgba(80,129,255,.2);
  border-radius:12px;
  margin-top:24px;
  margin-top:20px;
  padding:16px;
}
.datatable-pagination .datatable-pagination-list{
  align-items:center;
  display:flex;
  flex-wrap:wrap;
  gap:8px;
  justify-content:center;
}
.datatable-pagination .datatable-pagination-list .datatable-pagination-list-item .datatable-pagination-list-item-link{
  align-items:center;
  background:linear-gradient(135deg, rgba(30,41,59,.8), rgba(15,23,42,.8));
  background:rgba(30,41,59,.5);
  border:2px solid rgba(80,129,255,.3);
  border:1px solid rgba(80,129,255,.3);
  border-radius:8px;
  color:#ffffffcc;
  display:flex;
  font-family:Signika,sans-serif;
  font-size:14px;
  font-weight:600;
  height:40px;
  justify-content:center;
  min-width:40px;
  overflow:hidden;
  padding:8px 12px;
  position:relative;
  text-decoration:none;
  transition:all 0.3s ease;
}
.datatable-pagination .datatable-pagination-list .datatable-pagination-list-item .datatable-pagination-list-item-link:before{
  background:linear-gradient(90deg, transparent, rgba(80,129,255,.2), transparent);
  content:"";
  height:100%;
  left:-100%;
  position:absolute;
  top:0;
  transition:left 0.5s ease;
  width:100%;
}
.datatable-pagination .datatable-pagination-list .datatable-pagination-list-item .datatable-pagination-list-item-link:hover:before{
  left:100%;
}
.datatable-pagination .datatable-pagination-list .datatable-pagination-list-item .datatable-pagination-list-item-link:hover,.datatable-pagination .datatable-pagination-list .datatable-pagination-list-item.datatable-pagination-active .datatable-pagination-list-item-link{
  background:linear-gradient(135deg, #5081ff, #3463db);
  border-color:#5081ff;
  box-shadow:0 6px 20px rgba(80,129,255,.4);
  box-shadow:0 4px 12px rgba(80,129,255,.3);
  color:#fff;
  transform:translateY(-2px);
}
.datatable-pagination .datatable-pagination-list .datatable-pagination-list-item.datatable-pagination-active .datatable-pagination-list-item-link{
  box-shadow:0 6px 20px rgba(80,129,255,.6);
}
.datatable-top{
  align-items:center;
  background:linear-gradient(135deg, rgba(15,23,42,.6), rgba(30,41,59,.6));
  border:1px solid rgba(80,129,255,.2);
  border-radius:12px;
  display:flex;
  flex-wrap:wrap;
  gap:16px;
  justify-content:space-between;
  margin-bottom:20px;
  padding:16px;
}
.datatable-search{
  position:relative;
}
.datatable-search input{
  background:linear-gradient(135deg, rgba(30,41,59,.8), rgba(15,23,42,.8));
  background:rgba(30,41,59,.5);
  border:2px solid rgba(80,129,255,.3);
  border-radius:10px;
  border-radius:8px;
  color:#fff;
  font-family:Signika,sans-serif;
  font-size:14px;
  font-weight:500;
  min-width:280px;
  padding:12px 16px 12px 44px;
  padding:8px 12px;
  transition:all 0.3s ease;
}
.datatable-search input::-moz-placeholder{
  color:hsla(0,0%,100%,.5);
}
.datatable-search input::placeholder{
  color:hsla(0,0%,100%,.5);
}
.datatable-search input:focus{
  background:linear-gradient(135deg, rgba(30,41,59,.9), rgba(15,23,42,.9));
  border-color:#5081ff;
  box-shadow:0 0 0 4px rgba(80,129,255,.2);
  box-shadow:0 0 0 3px rgba(80,129,255,.2);
  outline:none;
}
.datatable-search:before{
  background-image:url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' stroke='%23FFFFFFCC' viewBox='0 0 24 24'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m21 21-6-6m2-5a7 7 0 1 1-14 0 7 7 0 0 1 14 0'/%3E%3C/svg%3E");
  background-repeat:no-repeat;
  background-size:contain;
  content:"";
  height:16px;
  left:16px;
  pointer-events:none;
  position:absolute;
  top:50%;
  transform:translateY(-50%);
  width:16px;
}
.datatable-selector{
  position:relative;
}
.datatable-selector select{
  -webkit-appearance:none;
     -moz-appearance:none;
          appearance:none;
  background:linear-gradient(135deg, rgba(30,41,59,.8), rgba(15,23,42,.8));
  background:rgba(30,41,59,.5);
  border:2px solid rgba(80,129,255,.3);
  border-radius:10px;
  border-radius:8px;
  color:#fff;
  cursor:pointer;
  font-family:Signika,sans-serif;
  font-size:14px;
  font-weight:500;
  padding:12px 40px 12px 16px;
  padding:6px 10px;
  transition:all 0.3s ease;
}
.datatable-selector select:focus{
  border-color:#5081ff;
  box-shadow:0 0 0 4px rgba(80,129,255,.2);
  box-shadow:0 0 0 3px rgba(80,129,255,.2);
  outline:none;
}
.datatable-selector:after{
  background-image:url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' stroke='%23FFFFFFCC' viewBox='0 0 24 24'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m19 9-7 7-7-7'/%3E%3C/svg%3E");
  background-repeat:no-repeat;
  background-size:contain;
  content:"";
  height:12px;
  pointer-events:none;
  position:absolute;
  right:16px;
  top:50%;
  transform:translateY(-50%);
  width:12px;
}
.datatable-info{
  background:linear-gradient(135deg, rgba(15,23,42,.6), rgba(30,41,59,.6));
  border:1px solid rgba(80,129,255,.2);
  border-radius:8px;
  color:#ffffffcc;
  font-family:Signika,sans-serif;
  font-size:14px;
  font-weight:500;
  padding:12px 16px;
}
.gaming-loading-item{
  border-radius:8px;
  overflow:hidden;
  position:relative;
}
.gaming-loading-item:before{
  animation:shimmer 2s infinite;
  background:linear-gradient(90deg, transparent, rgba(80,129,255,.3), transparent);
  background:linear-gradient(90deg, transparent, rgba(80,129,255,.2), transparent);
  content:"";
  height:100%;
  left:-100%;
  position:absolute;
  top:0;
  width:100%;
}
.gaming-table tr:hover td{
  background:linear-gradient(135deg, rgba(80,129,255,.1), rgba(52,99,219,.1));
  color:#fff;
}
.datatable-pagination .datatable-pagination-list .datatable-pagination-list-item{
  border-radius:8px;
  overflow:hidden;
}
.gaming-input:focus,.gaming-select:focus{
  box-shadow:0 4px 12px rgba(99,102,241,.3);
  transform:translateY(-1px);
}
.gaming-submit-btn:active{
  transform:scale(0.98) translateY(0);
}
.gaming-bank-card:hover{
  animation:glow 2s ease-in-out infinite;
}
.gaming-bank-card.active{
  animation:pulse-glow 3s ease-in-out infinite;
}
.gaming-recharge-panel:before{
  animation:rotate 8s linear infinite;
  background:linear-gradient(45deg, #6366f1, #8b5cf6, #06b6d4, #10b981, #f59e0b, #ef4444, #6366f1);
  border-radius:18px;
  bottom:-2px;
  content:"";
  left:-2px;
  opacity:0.1;
  position:absolute;
  right:-2px;
  top:-2px;
  z-index:-1;
}
.gaming-bank-card{
  border-radius:12px;
  overflow:hidden;
  position:relative;
  transform-origin:center;
  transition:all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  will-change:transform, box-shadow, border-color;
}
.gaming-bank-card:before{
  animation:rotate 4s linear infinite;
  background:linear-gradient(45deg, #5081ff, #3463db, #4b7dff, #5081ff);
  border-radius:14px;
  bottom:-2px;
  content:"";
  left:-2px;
  opacity:0;
  position:absolute;
  right:-2px;
  top:-2px;
  transition:opacity 0.3s ease;
  z-index:-1;
}
.gaming-bank-card:hover:before{
  opacity:0.3;
}
.gaming-bank-card.active:before{
  opacity:0.6;
}
@font-face{
    font-display:swap;
    font-family:Signika;
    src:url(/assets/webfonts/Signika-SemiBold.ttf);
}
a,button,div,h1,h2,h3,h4,h5,h6,input,p,select,span,textarea{
    font-family:Signika,sans-serif;
}
.__className_b661ba{
    font-family:League Spartan,Arial,sans-serif;
    font-style:normal
}
.__variable_b661ba{
    --font-league-spartan:"League Spartan", Arial, sans-serif}
.bg-\[#272450\]{ background-color:#272450 !important; }
.bg-\[#5081FF\]{ background-color:#5081ff !important; }
.bg-\[#3463DB\]{ background-color:#3463db !important; }
.bg-\[#4B7DFF\]{ background-color:#4b7dff !important; }
.bg-\[#00FF88\]{ background-color:#0f8 !important; }
.bg-\[#FFD700\]{ background-color:gold !important; }
.bg-\[#FF6B6B\]{ background-color:#ff6b6b !important; }
.bg-\[#FF8C00\]{ background-color:#ff8c00 !important; }
.bg-\[#9C27B0\]{ background-color:#9c27b0 !important; }
.bg-\[#0F172A\]{ background-color:#0f172a !important; }
.bg-\[#1E293B\]{ background-color:#1e293b !important; }
.bg-\[#13112E\]{ background-color:#13112e !important; }
.border-\[#272450\]{ border-color:#272450 !important; }
.border-\[#5081FF\]{ border-color:#5081ff !important; }
.border-\[#3463DB\]{ border-color:#3463db !important; }
.border-\[#4B7DFF\]{ border-color:#4b7dff !important; }
.border-\[#00FF88\]{ border-color:#0f8 !important; }
.border-\[#FFD700\]{ border-color:gold !important; }
.border-\[#FF6B6B\]{ border-color:#ff6b6b !important; }
.border-\[#FF8C00\]{ border-color:#ff8c00 !important; }
.border-\[#9C27B0\]{ border-color:#9c27b0 !important; }
.border-\[#0F172A\]{ border-color:#0f172a !important; }
.border-\[#1E293B\]{ border-color:#1e293b !important; }
.border-\[#13112E\]{ border-color:#13112e !important; }
.text-\[#FFFFFF\]{ color:#fff !important; }
.text-\[#FFFFFFCC\]{ color:#ffffffcc !important; }
.text-\[#FFFFFF99\]{ color:#ffffff99 !important; }
.text-\[#FFFFFF66\]{ color:#ffffff66 !important; }
.text-\[#272450\]{ color:#272450 !important; }
.text-\[#5081FF\]{ color:#5081ff !important; }
.text-\[#3463DB\]{ color:#3463db !important; }
.text-\[#4B7DFF\]{ color:#4b7dff !important; }
.text-\[#00FF88\]{ color:#0f8 !important; }
.text-\[#FFD700\]{ color:gold !important; }
.text-\[#FF6B6B\]{ color:#ff6b6b !important; }
.text-\[#FF8C00\]{ color:#ff8c00 !important; }
.text-\[#9C27B0\]{ color:#9c27b0 !important; }
.text-\[#0F172A\]{ color:#0f172a !important; }
.text-\[#1E293B\]{ color:#1e293b !important; }
.text-\[#13112E\]{ color:#13112e !important; }
*,:after,:before{--tw-border-spacing-x:0;--tw-border-spacing-y:0;--tw-translate-x:0;--tw-translate-y:0;--tw-rotate:0;--tw-skew-x:0;--tw-skew-y:0;--tw-scale-x:1;--tw-scale-y:1;--tw-pan-x:;--tw-pan-y:;--tw-pinch-zoom:;--tw-scroll-snap-strictness:proximity;--tw-gradient-from-position:;--tw-gradient-via-position:;--tw-gradient-to-position:;--tw-ordinal:;--tw-slashed-zero:;--tw-numeric-figure:;--tw-numeric-spacing:;--tw-numeric-fraction:;--tw-ring-inset:;--tw-ring-offset-width:0px;--tw-ring-offset-color:#fff;--tw-ring-color:rgb(59 130 246/0.5);--tw-ring-offset-shadow:0 0 #0000;--tw-ring-shadow:0 0 #0000;--tw-shadow:0 0 #0000;--tw-shadow-colored:0 0 #0000;--tw-blur:;--tw-brightness:;--tw-contrast:;--tw-grayscale:;--tw-hue-rotate:;--tw-invert:;--tw-saturate:;--tw-sepia:;--tw-drop-shadow:;--tw-backdrop-blur:;--tw-backdrop-brightness:;--tw-backdrop-contrast:;--tw-backdrop-grayscale:;--tw-backdrop-hue-rotate:;--tw-backdrop-invert:;--tw-backdrop-opacity:;--tw-backdrop-saturate:;--tw-backdrop-sepia:;--tw-contain-size:;--tw-contain-layout:;--tw-contain-paint:;--tw-contain-style:;border:0 solid #e5e7eb;box-sizing:border-box}
/*
! tailwindcss v3.4.17 | MIT License | https://tailwindcss.com
*/
:after,:before{--tw-content:""}
fieldset,legend{padding:0}
input::-moz-placeholder,textarea::-moz-placeholder{color:#9ca3af;opacity:1}
.-bottom-\[5px\]{bottom:-5px}
.-top-\[70px\]{top:-70px}
.bottom-\[24px\]{bottom:24px}
.bottom-\[8px\]{bottom:8px}
.end-\[100\%\]{inset-inline-end:100%}
.left-0\.5{left:.125rem}
.left-\[10px\]{left:10px}
.left-\[12px\]{left:12px}
.left-\[16px\]{left:16px}
.left-\[24px\]{left:24px}
.left-\[8px\]{left:8px}
.left-\[calc\(\(100\%-1320px\)\/2-240px\)\]{left:calc(50% - 900px)}
.right-\[12px\]{right:12px}
.right-\[16px\]{right:16px}
.right-\[24px\]{right:24px}
.right-\[6px\]{right:6px}
.right-\[8px\]{right:8px}
.right-\[calc\(\(100\%-1320px\)\/2-240px\)\]{right:calc(50% - 900px)}
.top-\[100\%\]{top:100%}
.top-\[10px\]{top:10px}
.top-\[12px\]{top:12px}
.top-\[14px\]{top:14px}
.top-\[16px\]{top:16px}
.top-\[6px\]{top:6px}
.top-\[8px\]{top:8px}
.m-\[60px\]{margin:60px}
.\!my-\[24px\]{margin-bottom:24px!important;margin-top:24px!important}
.-mx-\[12px\]{margin-left:-12px;margin-right:-12px}
.-mx-\[16px\]{margin-left:-16px;margin-right:-16px}
.-mx-dynamic-1{margin-left:-1px;margin-right:-1px}
.-mx-dynamic-10{margin-left:-10px;margin-right:-10px}
.-mx-dynamic-100{margin-left:-100px;margin-right:-100px}
.-mx-dynamic-11{margin-left:-11px;margin-right:-11px}
.-mx-dynamic-12{margin-left:-12px;margin-right:-12px}
.-mx-dynamic-13{margin-left:-13px;margin-right:-13px}
.-mx-dynamic-14{margin-left:-14px;margin-right:-14px}
.-mx-dynamic-15{margin-left:-15px;margin-right:-15px}
.-mx-dynamic-16{margin-left:-16px;margin-right:-16px}
.-mx-dynamic-17{margin-left:-17px;margin-right:-17px}
.-mx-dynamic-18{margin-left:-18px;margin-right:-18px}
.-mx-dynamic-19{margin-left:-19px;margin-right:-19px}
.-mx-dynamic-2{margin-left:-2px;margin-right:-2px}
.-mx-dynamic-20{margin-left:-20px;margin-right:-20px}
.-mx-dynamic-21{margin-left:-21px;margin-right:-21px}
.-mx-dynamic-22{margin-left:-22px;margin-right:-22px}
.-mx-dynamic-23{margin-left:-23px;margin-right:-23px}
.-mx-dynamic-24{margin-left:-24px;margin-right:-24px}
.-mx-dynamic-25{margin-left:-25px;margin-right:-25px}
.-mx-dynamic-26{margin-left:-26px;margin-right:-26px}
.-mx-dynamic-27{margin-left:-27px;margin-right:-27px}
.-mx-dynamic-28{margin-left:-28px;margin-right:-28px}
.-mx-dynamic-29{margin-left:-29px;margin-right:-29px}
.-mx-dynamic-3{margin-left:-3px;margin-right:-3px}
.-mx-dynamic-30{margin-left:-30px;margin-right:-30px}
.-mx-dynamic-31{margin-left:-31px;margin-right:-31px}
.-mx-dynamic-32{margin-left:-32px;margin-right:-32px}
.-mx-dynamic-33{margin-left:-33px;margin-right:-33px}
.-mx-dynamic-34{margin-left:-34px;margin-right:-34px}
.-mx-dynamic-35{margin-left:-35px;margin-right:-35px}
.-mx-dynamic-36{margin-left:-36px;margin-right:-36px}
.-mx-dynamic-37{margin-left:-37px;margin-right:-37px}
.-mx-dynamic-38{margin-left:-38px;margin-right:-38px}
.-mx-dynamic-39{margin-left:-39px;margin-right:-39px}
.-mx-dynamic-4{margin-left:-4px;margin-right:-4px}
.-mx-dynamic-40{margin-left:-40px;margin-right:-40px}
.-mx-dynamic-41{margin-left:-41px;margin-right:-41px}
.-mx-dynamic-42{margin-left:-42px;margin-right:-42px}
.-mx-dynamic-43{margin-left:-43px;margin-right:-43px}
.-mx-dynamic-44{margin-left:-44px;margin-right:-44px}
.-mx-dynamic-45{margin-left:-45px;margin-right:-45px}
.-mx-dynamic-46{margin-left:-46px;margin-right:-46px}
.-mx-dynamic-47{margin-left:-47px;margin-right:-47px}
.-mx-dynamic-48{margin-left:-48px;margin-right:-48px}
.-mx-dynamic-49{margin-left:-49px;margin-right:-49px}
.-mx-dynamic-5{margin-left:-5px;margin-right:-5px}
.-mx-dynamic-50{margin-left:-50px;margin-right:-50px}
.-mx-dynamic-51{margin-left:-51px;margin-right:-51px}
.-mx-dynamic-52{margin-left:-52px;margin-right:-52px}
.-mx-dynamic-53{margin-left:-53px;margin-right:-53px}
.-mx-dynamic-54{margin-left:-54px;margin-right:-54px}
.-mx-dynamic-55{margin-left:-55px;margin-right:-55px}
.-mx-dynamic-56{margin-left:-56px;margin-right:-56px}
.-mx-dynamic-57{margin-left:-57px;margin-right:-57px}
.-mx-dynamic-58{margin-left:-58px;margin-right:-58px}
.-mx-dynamic-59{margin-left:-59px;margin-right:-59px}
.-mx-dynamic-6{margin-left:-6px;margin-right:-6px}
.-mx-dynamic-60{margin-left:-60px;margin-right:-60px}
.-mx-dynamic-61{margin-left:-61px;margin-right:-61px}
.-mx-dynamic-62{margin-left:-62px;margin-right:-62px}
.-mx-dynamic-63{margin-left:-63px;margin-right:-63px}
.-mx-dynamic-64{margin-left:-64px;margin-right:-64px}
.-mx-dynamic-65{margin-left:-65px;margin-right:-65px}
.-mx-dynamic-66{margin-left:-66px;margin-right:-66px}
.-mx-dynamic-67{margin-left:-67px;margin-right:-67px}
.-mx-dynamic-68{margin-left:-68px;margin-right:-68px}
.-mx-dynamic-69{margin-left:-69px;margin-right:-69px}
.-mx-dynamic-7{margin-left:-7px;margin-right:-7px}
.-mx-dynamic-70{margin-left:-70px;margin-right:-70px}
.-mx-dynamic-71{margin-left:-71px;margin-right:-71px}
.-mx-dynamic-72{margin-left:-72px;margin-right:-72px}
.-mx-dynamic-73{margin-left:-73px;margin-right:-73px}
.-mx-dynamic-74{margin-left:-74px;margin-right:-74px}
.-mx-dynamic-75{margin-left:-75px;margin-right:-75px}
.-mx-dynamic-76{margin-left:-76px;margin-right:-76px}
.-mx-dynamic-77{margin-left:-77px;margin-right:-77px}
.-mx-dynamic-78{margin-left:-78px;margin-right:-78px}
.-mx-dynamic-79{margin-left:-79px;margin-right:-79px}
.-mx-dynamic-8{margin-left:-8px;margin-right:-8px}
.-mx-dynamic-80{margin-left:-80px;margin-right:-80px}
.-mx-dynamic-81{margin-left:-81px;margin-right:-81px}
.-mx-dynamic-82{margin-left:-82px;margin-right:-82px}
.-mx-dynamic-83{margin-left:-83px;margin-right:-83px}
.-mx-dynamic-84{margin-left:-84px;margin-right:-84px}
.-mx-dynamic-85{margin-left:-85px;margin-right:-85px}
.-mx-dynamic-86{margin-left:-86px;margin-right:-86px}
.-mx-dynamic-87{margin-left:-87px;margin-right:-87px}
.-mx-dynamic-88{margin-left:-88px;margin-right:-88px}
.-mx-dynamic-89{margin-left:-89px;margin-right:-89px}
.-mx-dynamic-9{margin-left:-9px;margin-right:-9px}
.-mx-dynamic-90{margin-left:-90px;margin-right:-90px}
.-mx-dynamic-91{margin-left:-91px;margin-right:-91px}
.-mx-dynamic-92{margin-left:-92px;margin-right:-92px}
.-mx-dynamic-93{margin-left:-93px;margin-right:-93px}
.-mx-dynamic-94{margin-left:-94px;margin-right:-94px}
.-mx-dynamic-95{margin-left:-95px;margin-right:-95px}
.-mx-dynamic-96{margin-left:-96px;margin-right:-96px}
.-mx-dynamic-97{margin-left:-97px;margin-right:-97px}
.-mx-dynamic-98{margin-left:-98px;margin-right:-98px}
.-mx-dynamic-99{margin-left:-99px;margin-right:-99px}
.-my-\[1px\]{margin-bottom:-1px;margin-top:-1px}
.-my-\[36px\]{margin-bottom:-36px;margin-top:-36px}
.mx-\[12px\]{margin-left:12px;margin-right:12px}
.mx-\[5px\]{margin-left:5px;margin-right:5px}
.my-\[-1px\]{margin-bottom:-1px;margin-top:-1px}
.my-\[-4px\]{margin-bottom:-4px;margin-top:-4px}
.my-\[12px\]{margin-bottom:12px;margin-top:12px}
.my-\[16px\]{margin-bottom:16px;margin-top:16px}
.my-\[24px\]{margin-bottom:24px;margin-top:24px}
.my-\[90px\]{margin-bottom:90px;margin-top:90px}
.\!mb-\[12px\]{margin-bottom:12px!important}
.\!mb-\[16px\]{margin-bottom:16px!important}
.\!mb-\[24px\]{margin-bottom:24px!important}
.-mb-\[0\.2em\]{margin-bottom:-.2em}
.-mb-\[0\.35em\]{margin-bottom:-.35em}
.-mb-\[0\.3em\]{margin-bottom:-.3em}
.-mb-\[12px\]{margin-bottom:-12px}
.-mb-\[20px\]{margin-bottom:-20px}
.-ml-\[0\.05em\]{margin-left:-.05em}
.-mr-\[0\.05em\]{margin-right:-.05em}
.-mr-\[10px\]{margin-right:-10px}
.-mt-\[78px\]{margin-top:-78px}
.mb-\[12px\]{margin-bottom:12px}
.mb-\[16px\]{margin-bottom:16px}
.mb-\[24px\]{margin-bottom:24px}
.mb-\[36px\]{margin-bottom:36px}
.mb-\[4px\]{margin-bottom:4px}
.mb-\[8px\]{margin-bottom:8px}
.ml-\[-10px\]{margin-left:-10px}
.ml-\[12px\]{margin-left:12px}
.ml-\[16px\]{margin-left:16px}
.ml-\[20px\]{margin-left:20px}
.ml-\[24px\]{margin-left:24px}
.ml-\[2px\]{margin-left:2px}
.ml-\[32px\]{margin-left:32px}
.ml-\[4px\]{margin-left:4px}
.ml-\[53px\]{margin-left:53px}
.ml-\[8px\]{margin-left:8px}
.mr-0\.5{margin-right:.125rem}
.mr-\[-10px\]{margin-right:-10px}
.mr-\[16px\]{margin-right:16px}
.mr-\[2px\]{margin-right:2px}
.mr-\[4px\]{margin-right:4px}
.ms-1-24{margin-inline-start:4.166666666666666%}
.ms-10-24{margin-inline-start:41.66666666666667%}
.ms-11-24{margin-inline-start:45.83333333333333%}
.ms-12-24{margin-inline-start:50%}
.ms-13-24{margin-inline-start:54.166666666666664%}
.ms-14-24{margin-inline-start:58.333333333333336%}
.ms-15-24{margin-inline-start:62.5%}
.ms-16-24{margin-inline-start:66.66666666666666%}
.ms-17-24{margin-inline-start:70.83333333333334%}
.ms-18-24{margin-inline-start:75%}
.ms-19-24{margin-inline-start:79.16666666666666%}
.ms-2-24{margin-inline-start:8.333333333333332%}
.ms-20-24{margin-inline-start:83.33333333333334%}
.ms-21-24{margin-inline-start:87.5%}
.ms-22-24{margin-inline-start:91.66666666666666%}
.ms-23-24{margin-inline-start:95.83333333333334%}
.ms-24-24{margin-inline-start:100%}
.ms-3-24{margin-inline-start:12.5%}
.ms-4-24{margin-inline-start:16.666666666666664%}
.ms-5-24{margin-inline-start:20.833333333333336%}
.ms-6-24{margin-inline-start:25%}
.ms-7-24{margin-inline-start:29.166666666666668%}
.ms-8-24{margin-inline-start:33.33333333333333%}
.ms-9-24{margin-inline-start:37.5%}
.mt-\[0\.175em\]{margin-top:.175em}
.mt-\[0\.2em\]{margin-top:.2em}
.mt-\[0\.335em\]{margin-top:.335em}
.mt-\[0\.3em\]{margin-top:.3em}
.mt-\[11px\]{margin-top:11px}
.mt-\[12px\]{margin-top:12px}
.mt-\[17px\]{margin-top:17px}
.mt-\[20px\]{margin-top:20px}
.mt-\[2px\]{margin-top:2px}
.mt-\[32px\]{margin-top:32px}
.mt-\[34px\]{margin-top:34px}
.mt-\[36px\]{margin-top:36px}
.mt-\[48px\]{margin-top:48px}
.mt-\[60px\]{margin-top:60px}
.line-clamp-1,.line-clamp-2{display:-webkit-box;overflow:hidden;-webkit-box-orient:vertical}
.aspect-\[106\/66\]{aspect-ratio:106/66}
.aspect-\[1250\/2894\]{aspect-ratio:1250/2894}
.aspect-\[126\/78\]{aspect-ratio:126/78}
.aspect-\[1320\/275\]{aspect-ratio:1320/275}
.aspect-\[149\/307\]{aspect-ratio:149/307}
.aspect-\[16\/16\]{aspect-ratio:16/16}
.aspect-\[168\/88\]{aspect-ratio:168/88}
.aspect-\[24\/24\]{aspect-ratio:24/24}
.aspect-\[318\/132\]{aspect-ratio:318/132}
.aspect-\[388\/259\]{aspect-ratio:388/259}
.aspect-\[48\/48\]{aspect-ratio:48/48}
.aspect-\[86\/132\]{aspect-ratio:86/132}
.\!size-\[20px\]{height:20px!important;width:20px!important}
.size-\[17px\]{height:17px;width:17px}
.size-\[40px\]{height:40px;width:40px}
.size-\[48px\]{height:48px;width:48px}
.size-\[64px\]{height:64px;width:64px}
.\!h-\[12px\]{height:12px!important}
.\!h-\[13px\]{height:13px!important}
.\!h-\[14px\]{height:14px!important}
.\!h-\[16px\]{height:16px!important}
.\!h-\[17px\]{height:17px!important}
.\!h-\[24px\]{height:24px!important}
.\!h-\[40px\]{height:40px!important}
.\!h-\[4px\]{height:4px!important}
.h-\[0\.9em\]{height:.9em}
.h-\[100px\]{height:100px}
.h-\[11px\]{height:11px}
.h-\[131px\]{height:131px}
.h-\[132px\]{height:132px}
.h-\[13px\]{height:13px}
.h-\[14px\]{height:14px}
.h-\[15px\]{height:15px}
.h-\[164px\]{height:164px}
.h-\[17px\]{height:17px}
.h-\[180px\]{height:180px}
.h-\[20px\]{height:20px}
.h-\[242px\]{height:242px}
.h-\[266px\]{height:266px}
.h-\[28px\]{height:28px}
.h-\[300px\]{height:300px}
.h-\[33px\]{height:33px}
.h-\[36px\]{height:36px}
.h-\[378px\]{height:378px}
.h-\[39px\]{height:39px}
.h-\[42px\]{height:42px}
.h-\[44px\]{height:44px}
.h-\[48px\]{height:48px}
.h-\[50px\]{height:50px}
.h-\[60px\]{height:60px}
.h-\[65px\]{height:65px}
.h-\[85px\]{height:85px}
.h-\[95px\]{height:95px}
.h-\[96px\]{height:96px}
.h-\[calc\(100\%-32px\)\]{height:calc(100% - 32px)}
.max-h-\[26px\]{max-height:26px}
.max-h-\[314px\]{max-height:314px}
.max-h-\[33px\]{max-height:33px}
.max-h-\[36px\]{max-height:36px}
.max-h-\[40px\]{max-height:40px}
.max-h-\[70vh\]{max-height:70vh}
.min-h-\[131px\]{min-height:131px}
.min-h-\[14px\]{min-height:14px}
.min-h-\[16px\]{min-height:16px}
.min-h-\[40px\]{min-height:40px}
.min-h-\[9px\]{min-height:9px}
.\!w-\[24px\]{width:24px!important}
.w-1-24{width:4.166666666666666%}
.w-10-24{width:41.66666666666667%}
.w-11-24{width:45.83333333333333%}
.w-12-24{width:50%}
.w-13-24{width:54.166666666666664%}
.w-14-24{width:58.333333333333336%}
.w-15-24{width:62.5%}
.w-16-24{width:66.66666666666666%}
.w-17-24{width:70.83333333333334%}
.w-18-24{width:75%}
.w-19-24{width:79.16666666666666%}
.w-2-24{width:8.333333333333332%}
.w-20-24{width:83.33333333333334%}
.w-21-24{width:87.5%}
.w-22-24{width:91.66666666666666%}
.w-23-24{width:95.83333333333334%}
.w-24-24{width:100%}
.w-3-24{width:12.5%}
.w-4-24{width:16.666666666666664%}
.w-5-24{width:20.833333333333336%}
.w-6-24{width:25%}
.w-7-24{width:29.166666666666668%}
.w-8-24{width:33.33333333333333%}
.w-9-24{width:37.5%}
.w-\[100px\]{width:100px}
.w-\[106px\]{width:106px}
.w-\[122px\]{width:122px}
.w-\[130px\]{width:130px}
.w-\[1320px\]{width:1320px}
.w-\[142px\]{width:142px}
.w-\[150px\]{width:150px}
.w-\[162\.8\%\]{width:162.8%}
.w-\[180px\]{width:180px}
.w-\[20px\]{width:20px}
.w-\[228px\]{width:228px}
.w-\[22px\]{width:22px}
.w-\[250px\]{width:250px}
.w-\[276px\]{width:276px}
.w-\[28\.6px\]{width:28.6px}
.w-\[300px\]{width:300px}
.w-\[312px\]{width:312px}
.w-\[34px\]{width:34px}
.w-\[375px\]{width:375px}
.w-\[388px\]{width:388px}
.w-\[38px\]{width:38px}
.w-\[423px\]{width:423px}
.w-\[424px\]{width:424px}
.w-\[436px\]{width:436px}
.w-\[440px\]{width:440px}
.w-\[44px\]{width:44px}
.w-\[46px\]{width:46px}
.w-\[480px\]{width:480px}
.w-\[486px\]{width:486px}
.w-\[48px\]{width:48px}
.w-\[500px\]{width:500px}
.w-\[50px\]{width:50px}
.w-\[536px\]{width:536px}
.w-\[600px\]{width:600px}
.w-\[60px\]{width:60px}
.w-\[64px\]{width:64px}
.w-\[72px\]{width:72px}
.w-\[800px\]{width:800px}
.w-\[82px\]{width:82px}
.w-\[86px\]{width:86px}
.w-\[89px\]{width:89px}
.w-\[95px\]{width:95px}
.w-\[984px\]{width:984px}
.w-\[calc\(100\%-28px\)\]{width:calc(100% - 28px)}
.w-\[calc\(100\%-40px\)\]{width:calc(100% - 40px)}
.w-\[calc\(100\%-72px\)\]{width:calc(100% - 72px)}
.w-col-1{width:100%}
.w-col-10{width:10%}
.w-col-11{width:9.090909090909092%}
.w-col-12{width:8.333333333333334%}
.w-col-13{width:7.6923076923076925%}
.w-col-14{width:7.142857142857143%}
.w-col-15{width:6.666666666666667%}
.w-col-16{width:6.25%}
.w-col-17{width:5.882352941176471%}
.w-col-18{width:5.555555555555555%}
.w-col-19{width:5.2631578947368425%}
.w-col-2{width:50%}
.w-col-20{width:5%}
.w-col-21{width:4.761904761904762%}
.w-col-22{width:4.545454545454546%}
.w-col-23{width:4.3478260869565215%}
.w-col-24{width:4.166666666666667%}
.w-col-3{width:33.333333333333336%}
.w-col-4{width:25%}
.w-col-5{width:20%}
.w-col-6{width:16.666666666666668%}
.w-col-7{width:14.285714285714286%}
.w-col-8{width:12.5%}
.w-col-9{width:11.11111111111111%}
.min-w-\[14px\]{min-width:14px}
.min-w-\[150px\]{min-width:150px}
.min-w-\[16px\]{min-width:16px}
.min-w-\[200px\]{min-width:200px}
.min-w-\[228px\]{min-width:228px}
.min-w-\[250px\]{min-width:250px}
.min-w-\[40px\]{min-width:40px}
.min-w-\[80px\]{min-width:80px}
.min-w-\[84px\]{min-width:84px}
.min-w-\[9px\]{min-width:9px}
.max-w-\[110px\]{max-width:110px}
.max-w-\[16px\]{max-width:16px}
.max-w-\[347px\]{max-width:347px}
.max-w-\[352px\]{max-width:352px}
.max-w-\[468px\]{max-width:468px}
.max-w-\[536px\]{max-width:536px}
.max-w-\[84px\]{max-width:84px}
.max-w-\[944px\]{max-width:944px}
.flex-shrink-0,.shrink-0{flex-shrink:0}
.origin-\[0\]{transform-origin:0}
.-translate-x-1\/2,.-translate-x-\[100\%\]{transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}
.-translate-x-\[100\%\]{--tw-translate-x:-100%}
.-translate-x-\[50\%\],.-translate-y-1\/2,.-translate-y-2,.-translate-y-\[100\%\]{transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}
.-translate-y-\[100\%\]{--tw-translate-y:-100%}
.-translate-y-\[50\%\],.translate-x-0{transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}
.translate-x-\[100\%\]{--tw-translate-x:100%}
.translate-x-\[100\%\],.translate-x-\[50\%\]{transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}
.translate-x-\[50\%\]{--tw-translate-x:50%}
.translate-y-0,.translate-y-\[-50\%\]{transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}
.translate-y-\[-50\%\]{--tw-translate-y:-50%}
.translate-y-\[100\%\]{--tw-translate-y:100%}
.translate-y-\[100\%\],.translate-y-\[266px\]{transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}
.translate-y-\[266px\]{--tw-translate-y:266px}
.rotate-0,.rotate-180,.scale-0,.scale-100,.scale-90,.scale-95{transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}
@keyframes bounce-y{0%,to{transform:translateY(-5%)}50%{transform:translateY(5%)}}
.animate-bounce-y{animation:bounce-y 1s infinite}
.scroll-mt-\[100px\]{scroll-margin-top:100px}
.gap-\[20px\]{gap:20px}
.gap-\[2px\]{gap:2px}
.gap-\[32px\]{gap:32px}
.gap-\[50px\]{gap:50px}
.gap-\[60px\]{gap:60px}
.gap-\[6px\]{gap:6px}
.gap-x-\[4px\]{-moz-column-gap:4px;column-gap:4px}
.gap-y-\[4px\]{row-gap:4px}
.gap-y-dynamic-1{row-gap:1px}
.gap-y-dynamic-10{row-gap:10px}
.gap-y-dynamic-100{row-gap:100px}
.gap-y-dynamic-11{row-gap:11px}
.gap-y-dynamic-12{row-gap:12px}
.gap-y-dynamic-13{row-gap:13px}
.gap-y-dynamic-14{row-gap:14px}
.gap-y-dynamic-15{row-gap:15px}
.gap-y-dynamic-16{row-gap:16px}
.gap-y-dynamic-17{row-gap:17px}
.gap-y-dynamic-18{row-gap:18px}
.gap-y-dynamic-19{row-gap:19px}
.gap-y-dynamic-2{row-gap:2px}
.gap-y-dynamic-20{row-gap:20px}
.gap-y-dynamic-21{row-gap:21px}
.gap-y-dynamic-22{row-gap:22px}
.gap-y-dynamic-23{row-gap:23px}
.gap-y-dynamic-24{row-gap:24px}
.gap-y-dynamic-25{row-gap:25px}
.gap-y-dynamic-26{row-gap:26px}
.gap-y-dynamic-27{row-gap:27px}
.gap-y-dynamic-28{row-gap:28px}
.gap-y-dynamic-29{row-gap:29px}
.gap-y-dynamic-3{row-gap:3px}
.gap-y-dynamic-30{row-gap:30px}
.gap-y-dynamic-31{row-gap:31px}
.gap-y-dynamic-32{row-gap:32px}
.gap-y-dynamic-33{row-gap:33px}
.gap-y-dynamic-34{row-gap:34px}
.gap-y-dynamic-35{row-gap:35px}
.gap-y-dynamic-36{row-gap:36px}
.gap-y-dynamic-37{row-gap:37px}
.gap-y-dynamic-38{row-gap:38px}
.gap-y-dynamic-39{row-gap:39px}
.gap-y-dynamic-4{row-gap:4px}
.gap-y-dynamic-40{row-gap:40px}
.gap-y-dynamic-41{row-gap:41px}
.gap-y-dynamic-42{row-gap:42px}
.gap-y-dynamic-43{row-gap:43px}
.gap-y-dynamic-44{row-gap:44px}
.gap-y-dynamic-45{row-gap:45px}
.gap-y-dynamic-46{row-gap:46px}
.gap-y-dynamic-47{row-gap:47px}
.gap-y-dynamic-48{row-gap:48px}
.gap-y-dynamic-49{row-gap:49px}
.gap-y-dynamic-5{row-gap:5px}
.gap-y-dynamic-50{row-gap:50px}
.gap-y-dynamic-51{row-gap:51px}
.gap-y-dynamic-52{row-gap:52px}
.gap-y-dynamic-53{row-gap:53px}
.gap-y-dynamic-54{row-gap:54px}
.gap-y-dynamic-55{row-gap:55px}
.gap-y-dynamic-56{row-gap:56px}
.gap-y-dynamic-57{row-gap:57px}
.gap-y-dynamic-58{row-gap:58px}
.gap-y-dynamic-59{row-gap:59px}
.gap-y-dynamic-6{row-gap:6px}
.gap-y-dynamic-60{row-gap:60px}
.gap-y-dynamic-61{row-gap:61px}
.gap-y-dynamic-62{row-gap:62px}
.gap-y-dynamic-63{row-gap:63px}
.gap-y-dynamic-64{row-gap:64px}
.gap-y-dynamic-65{row-gap:65px}
.gap-y-dynamic-66{row-gap:66px}
.gap-y-dynamic-67{row-gap:67px}
.gap-y-dynamic-68{row-gap:68px}
.gap-y-dynamic-69{row-gap:69px}
.gap-y-dynamic-7{row-gap:7px}
.gap-y-dynamic-70{row-gap:70px}
.gap-y-dynamic-71{row-gap:71px}
.gap-y-dynamic-72{row-gap:72px}
.gap-y-dynamic-73{row-gap:73px}
.gap-y-dynamic-74{row-gap:74px}
.gap-y-dynamic-75{row-gap:75px}
.gap-y-dynamic-76{row-gap:76px}
.gap-y-dynamic-77{row-gap:77px}
.gap-y-dynamic-78{row-gap:78px}
.gap-y-dynamic-79{row-gap:79px}
.gap-y-dynamic-8{row-gap:8px}
.gap-y-dynamic-80{row-gap:80px}
.gap-y-dynamic-81{row-gap:81px}
.gap-y-dynamic-82{row-gap:82px}
.gap-y-dynamic-83{row-gap:83px}
.gap-y-dynamic-84{row-gap:84px}
.gap-y-dynamic-85{row-gap:85px}
.gap-y-dynamic-86{row-gap:86px}
.gap-y-dynamic-87{row-gap:87px}
.gap-y-dynamic-88{row-gap:88px}
.gap-y-dynamic-89{row-gap:89px}
.gap-y-dynamic-9{row-gap:9px}
.gap-y-dynamic-90{row-gap:90px}
.gap-y-dynamic-91{row-gap:91px}
.gap-y-dynamic-92{row-gap:92px}
.gap-y-dynamic-93{row-gap:93px}
.gap-y-dynamic-94{row-gap:94px}
.gap-y-dynamic-95{row-gap:95px}
.gap-y-dynamic-96{row-gap:96px}
.gap-y-dynamic-97{row-gap:97px}
.gap-y-dynamic-98{row-gap:98px}
.gap-y-dynamic-99{row-gap:99px}
.space-x-\[12px\]>:not([hidden])~:not([hidden]){--tw-space-x-reverse:0;margin-left:calc(12px*(1 - var(--tw-space-x-reverse)));margin-right:calc(12px*var(--tw-space-x-reverse))}
.space-x-\[25px\]>:not([hidden])~:not([hidden]){--tw-space-x-reverse:0;margin-left:calc(25px*(1 - var(--tw-space-x-reverse)));margin-right:calc(25px*var(--tw-space-x-reverse))}
.space-x-\[4px\]>:not([hidden])~:not([hidden]){--tw-space-x-reverse:0;margin-left:calc(4px*(1 - var(--tw-space-x-reverse)));margin-right:calc(4px*var(--tw-space-x-reverse))}
.space-y-\[12px\]>:not([hidden])~:not([hidden]){--tw-space-y-reverse:0;margin-bottom:calc(12px*var(--tw-space-y-reverse));margin-top:calc(12px*(1 - var(--tw-space-y-reverse)))}
.space-y-\[16px\]>:not([hidden])~:not([hidden]){--tw-space-y-reverse:0;margin-bottom:calc(16px*var(--tw-space-y-reverse));margin-top:calc(16px*(1 - var(--tw-space-y-reverse)))}
.space-y-\[20px\]>:not([hidden])~:not([hidden]){--tw-space-y-reverse:0;margin-bottom:calc(20px*var(--tw-space-y-reverse));margin-top:calc(20px*(1 - var(--tw-space-y-reverse)))}
.space-y-\[24px\]>:not([hidden])~:not([hidden]){--tw-space-y-reverse:0;margin-bottom:calc(24px*var(--tw-space-y-reverse));margin-top:calc(24px*(1 - var(--tw-space-y-reverse)))}
.space-y-\[4px\]>:not([hidden])~:not([hidden]){--tw-space-y-reverse:0;margin-bottom:calc(4px*var(--tw-space-y-reverse));margin-top:calc(4px*(1 - var(--tw-space-y-reverse)))}
.divide-y-\[1px\]>:not([hidden])~:not([hidden]),.divide-y>:not([hidden])~:not([hidden]){--tw-divide-y-reverse:0;border-bottom-width:calc(1px*var(--tw-divide-y-reverse));border-top-width:calc(1px*(1 - var(--tw-divide-y-reverse)))}
.divide-\[\#FFFFFF1F\]>:not([hidden])~:not([hidden]){border-color:#ffffff1f}
.divide-\[\#ffffff1f\]>:not([hidden])~:not([hidden]){border-color:#ffffff1f}
.truncate,.whitespace-nowrap{white-space:nowrap}
.\!rounded-\[16px\]{border-radius:16px!important}
.\!rounded-\[24px\]{border-radius:24px!important}
.rounded-\[18px\]{border-radius:18px}
.rounded-\[20px\]{border-radius:20px}
.rounded-\[4px\]{border-radius:4px}
.rounded-\[5px\]{border-radius:5px}
.rounded-b-\[16px\]{border-bottom-left-radius:16px;border-bottom-right-radius:16px}
.rounded-b-\[8px\]{border-bottom-left-radius:8px;border-bottom-right-radius:8px}
.rounded-l-\[16px\]{border-bottom-left-radius:16px;border-top-left-radius:16px}
.rounded-r-\[16px\]{border-bottom-right-radius:16px;border-top-right-radius:16px}
.rounded-t-\[16px\]{border-top-left-radius:16px;border-top-right-radius:16px}
.rounded-t-\[24px\]{border-top-left-radius:24px;border-top-right-radius:24px}
.rounded-t-\[8px\]{border-top-left-radius:8px;border-top-right-radius:8px}
.border-\[1px\]{border-width:1px}
.border-\[2px\]{border-width:2px}
.border-\[4px\]{border-width:4px}
.border-b-\[1px\]{border-bottom-width:1px}
.\!border-\[\#4B7DFF\]{--tw-border-opacity:1!important;border-color:rgb(75 125 255/var(--tw-border-opacity,1))!important}
.\!border-\[\#5081FF33\]{border-color:#5081ff33!important}
.\!border-\[\#FF5252\]{--tw-border-opacity:1!important;border-color:rgb(255 82 82/var(--tw-border-opacity,1))!important}
.\!border-\[\#FFFFFF1F\]{border-color:#ffffff1f!important}
.border-\[\#12CBAB\]{--tw-border-opacity:1;border-color:rgb(18 203 171/var(--tw-border-opacity,1))}
.border-\[\#272450\]{--tw-border-opacity:1;border-color:rgb(39 36 80/var(--tw-border-opacity,1))}
.border-\[\#2A2D4F\]{--tw-border-opacity:1;border-color:rgb(42 45 79/var(--tw-border-opacity,1))}
.border-\[\#3C8630\]{--tw-border-opacity:1;border-color:rgb(60 134 48/var(--tw-border-opacity,1))}
.border-\[\#4B7DFF\]{--tw-border-opacity:1;border-color:rgb(75 125 255/var(--tw-border-opacity,1))}
.border-\[\#4C4E52\]{--tw-border-opacity:1;border-color:rgb(76 78 82/var(--tw-border-opacity,1))}
.border-\[\#5081FF8F\]{border-color:#5081ff8f}
.border-\[\#69B1FF3D\]{border-color:#69b1ff3d}
.border-\[\#69B1FF\]{--tw-border-opacity:1;border-color:rgb(105 177 255/var(--tw-border-opacity,1))}
.border-\[\#C15755\]{--tw-border-opacity:1;border-color:rgb(193 87 85/var(--tw-border-opacity,1))}
.border-\[\#DC7439\]{--tw-border-opacity:1;border-color:rgb(220 116 57/var(--tw-border-opacity,1))}
.border-\[\#FE5252\]{--tw-border-opacity:1;border-color:rgb(254 82 82/var(--tw-border-opacity,1))}
.border-\[\#FF5252\]{--tw-border-opacity:1;border-color:rgb(255 82 82/var(--tw-border-opacity,1))}
.border-\[\#FFD25F\]{--tw-border-opacity:1;border-color:rgb(255 210 95/var(--tw-border-opacity,1))}
.border-\[\#FFFFFF33\]{border-color:#ffffff33}
.border-\[\#fff9\]{border-color:#fff9}
.border-\[\#ffffff1f\]{border-color:#ffffff1f}
.border-\[\#ffffff99\]{border-color:#ffffff99}
.border-y-\[\#FFFFFF1F\]{border-bottom-color:#ffffff1f;border-top-color:#ffffff1f}
.border-b-\[\#12CBAB\]{--tw-border-opacity:1;border-bottom-color:rgb(18 203 171/var(--tw-border-opacity,1))}
.border-b-\[\#5081FF33\]{border-bottom-color:#5081ff33}
.border-b-\[\#FF5252\]{--tw-border-opacity:1;border-bottom-color:rgb(255 82 82/var(--tw-border-opacity,1))}
.border-b-\[\#FFD25F\]{--tw-border-opacity:1;border-bottom-color:rgb(255 210 95/var(--tw-border-opacity,1))}
.border-l-\[\#FFFFFF1F\]{border-left-color:#ffffff1f}
.border-t-\[\#FFFFFF1F\]{border-top-color:#ffffff1f}
.border-t-\[\#FFFFFF99\]{border-top-color:#ffffff99}
.\!bg-\[\#0000005C\]{background-color:#0000005c!important}
.\!bg-\[\#0E0A2F\]{--tw-bg-opacity:1!important;background-color:rgb(14 10 47/var(--tw-bg-opacity,1))!important}
.\!bg-\[\#3A3E64\]{--tw-bg-opacity:1!important;background-color:rgb(58 62 100/var(--tw-bg-opacity,1))!important}
.\!bg-\[\#FF52525C\]{background-color:#ff52525c!important}
.\!bg-\[\#FFFFFF0F\]{background-color:#ffffff0f!important}
.\!bg-\[\#FFFFFF1F\]{background-color:#ffffff1f!important}
.bg-\[\#00000011\]{background-color:#00000011}
.bg-\[\#0000005A\]{background-color:#0000005a}
.bg-\[\#0000005C\]{background-color:#0000005c}
.bg-\[\#0000005D\]{background-color:#0000005d}
.bg-\[\#112242\]{--tw-bg-opacity:1;background-color:rgb(17 34 66/var(--tw-bg-opacity,1))}
.bg-\[\#12CBAB42\]{background-color:#12cbab42}
.bg-\[\#13112E\],.bg-\[\#13112e\]{--tw-bg-opacity:1;background-color:rgb(19 17 46/var(--tw-bg-opacity,1))}
.bg-\[\#1677ff\]{--tw-bg-opacity:1;background-color:rgb(22 119 255/var(--tw-bg-opacity,1))}
.bg-\[\#1677ffbf\]{background-color:#1677ffbf}
.bg-\[\#171A21\]{--tw-bg-opacity:1;background-color:rgb(23 26 33/var(--tw-bg-opacity,1))}
.bg-\[\#181A21\]{--tw-bg-opacity:1;background-color:rgb(24 26 33/var(--tw-bg-opacity,1))}
.bg-\[\#2B2E33\]{--tw-bg-opacity:1;background-color:rgb(43 46 51/var(--tw-bg-opacity,1))}
.bg-\[\#2c2e41\]{--tw-bg-opacity:1;background-color:rgb(44 46 65/var(--tw-bg-opacity,1))}
.bg-\[\#302f5a\]{--tw-bg-opacity:1;background-color:rgb(48 47 90/var(--tw-bg-opacity,1))}
.bg-\[\#32353C\]{--tw-bg-opacity:1;background-color:rgb(50 53 60/var(--tw-bg-opacity,1))}
.bg-\[\#364d79\]{--tw-bg-opacity:1;background-color:rgb(54 77 121/var(--tw-bg-opacity,1))}
.bg-\[\#3A3E64\]{--tw-bg-opacity:1;background-color:rgb(58 62 100/var(--tw-bg-opacity,1))}
.bg-\[\#3C4746\]{--tw-bg-opacity:1;background-color:rgb(60 71 70/var(--tw-bg-opacity,1))}
.bg-\[\#3C8630\]{--tw-bg-opacity:1;background-color:rgb(60 134 48/var(--tw-bg-opacity,1))}
.bg-\[\#473C3C\]{--tw-bg-opacity:1;background-color:rgb(71 60 60/var(--tw-bg-opacity,1))}
.bg-\[\#47443C\]{--tw-bg-opacity:1;background-color:rgb(71 68 60/var(--tw-bg-opacity,1))}
.bg-\[\#5081FF33\]{background-color:#5081ff33}
.bg-\[\#555\]{--tw-bg-opacity:1;background-color:rgb(85 85 85/var(--tw-bg-opacity,1))}
.bg-\[\#5765F2\]{--tw-bg-opacity:1;background-color:rgb(87 101 242/var(--tw-bg-opacity,1))}
.bg-\[\#69B1FF\]{--tw-bg-opacity:1;background-color:rgb(105 177 255/var(--tw-bg-opacity,1))}
.bg-\[\#69b1ff42\]{background-color:#69b1ff42}
.bg-\[\#876800\]{--tw-bg-opacity:1;background-color:rgb(135 104 0/var(--tw-bg-opacity,1))}
.bg-\[\#DC7439\]{--tw-bg-opacity:1;background-color:rgb(220 116 57/var(--tw-bg-opacity,1))}
.bg-\[\#FE5252\]{--tw-bg-opacity:1;background-color:rgb(254 82 82/var(--tw-bg-opacity,1))}
.bg-\[\#FF52525C\]{background-color:#ff52525c}
.bg-\[\#FF52525c\]{background-color:#ff52525c}
.bg-\[\#FFD25F42\]{background-color:#ffd25f42}
.bg-\[\#FFFFFF0F\]{background-color:#ffffff0f}
.bg-\[\#FFFFFF1E\]{background-color:#ffffff1e}
.bg-\[\#FFFFFF4D\]{background-color:#ffffff4d}
.bg-\[\#ff4d4f\]{--tw-bg-opacity:1;background-color:rgb(255 77 79/var(--tw-bg-opacity,1))}
.bg-\[\#ff525242\]{background-color:#ff525242}
.bg-\[\#fff\]{--tw-bg-opacity:1;background-color:rgb(255 255 255/var(--tw-bg-opacity,1))}
.bg-\[\#ffffff33\]{background-color:#ffffff33}
.bg-\[\#ffffff3d\]{background-color:#ffffff3d}
.bg-\[\#fffffff5\]{background-color:#fffffff5}
.bg-\[blue\]{--tw-bg-opacity:1;background-color:rgb(0 0 255/var(--tw-bg-opacity,1))}
.bg-white\/50{
    background-color:hsla(0,0%,100%,.5)}
.from-\[\#06BFFF\]{--tw-gradient-from:#06bfff var(--tw-gradient-from-position);--tw-gradient-to:rgb(6 191 255/0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from),var(--tw-gradient-to)}
.from-\[\#0C3E6A\]{--tw-gradient-from:#0c3e6a var(--tw-gradient-from-position);--tw-gradient-to:rgb(12 62 106/0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from),var(--tw-gradient-to)}
.from-\[\#3C8CE5\]{--tw-gradient-from:#3c8ce5 var(--tw-gradient-from-position);--tw-gradient-to:rgb(60 140 229/0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from),var(--tw-gradient-to)}
.from-\[\#4C5FFD\]{--tw-gradient-from:#4c5ffd var(--tw-gradient-from-position);--tw-gradient-to:rgb(76 95 253/0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from),var(--tw-gradient-to)}
.from-\[\#A975FF\]{--tw-gradient-from:#a975ff var(--tw-gradient-from-position);--tw-gradient-to:rgb(169 117 255/0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from),var(--tw-gradient-to)}
.from-\[\#FFFFFF00\]{--tw-gradient-from:#ffffff00 var(--tw-gradient-from-position);--tw-gradient-to:hsla(0,0%,100%,0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from),var(--tw-gradient-to)}
.to-\[\#096394\]{--tw-gradient-to:#096394 var(--tw-gradient-to-position)}
.to-\[\#1841D3\]{--tw-gradient-to:#1841d3 var(--tw-gradient-to-position)}
.to-\[\#2D73FF\]{--tw-gradient-to:#2d73ff var(--tw-gradient-to-position)}
.to-\[\#423BA4\]{--tw-gradient-to:#423ba4 var(--tw-gradient-to-position)}
.to-\[\#489CFF\]{--tw-gradient-to:#489cff var(--tw-gradient-to-position)}
.to-\[\#FFFFFF3D\]{--tw-gradient-to:#ffffff3d var(--tw-gradient-to-position)}
.\!p-\[12px\]{padding:12px!important}
.p-2\.5{padding:.625rem}
.p-\[0\]{padding:0}
.p-\[12px\]{padding:12px}
.p-\[2px\]{padding:2px}
.p-\[32px\]{padding:32px}
.p-\[60px\]{padding:60px}
.p-\[8px\]{padding:8px}
.\!px-\[16px\]{padding-left:16px!important;padding-right:16px!important}
.\!py-\[16px\]{padding-bottom:16px!important;padding-top:16px!important}
.px-\[10px\]{padding-left:10px;padding-right:10px}
.px-\[18px\]{padding-left:18px;padding-right:18px}
.px-\[20px\]{padding-left:20px;padding-right:20px}
.px-\[23px\]{padding-left:23px;padding-right:23px}
.px-\[2px\]{padding-left:2px;padding-right:2px}
.px-\[32px\]{padding-left:32px;padding-right:32px}
.px-\[36px\]{padding-left:36px;padding-right:36px}
.px-\[4px\]{padding-left:4px;padding-right:4px}
.px-\[6px\]{padding-left:6px;padding-right:6px}
.px-\[8px\]{padding-left:8px;padding-right:8px}
.px-dynamic-1{padding-left:1px;padding-right:1px}
.px-dynamic-10{padding-left:10px;padding-right:10px}
.px-dynamic-100{padding-left:100px;padding-right:100px}
.px-dynamic-11{padding-left:11px;padding-right:11px}
.px-dynamic-12{padding-left:12px;padding-right:12px}
.px-dynamic-13{padding-left:13px;padding-right:13px}
.px-dynamic-14{padding-left:14px;padding-right:14px}
.px-dynamic-15{padding-left:15px;padding-right:15px}
.px-dynamic-16{padding-left:16px;padding-right:16px}
.px-dynamic-17{padding-left:17px;padding-right:17px}
.px-dynamic-18{padding-left:18px;padding-right:18px}
.px-dynamic-19{padding-left:19px;padding-right:19px}
.px-dynamic-2{padding-left:2px;padding-right:2px}
.px-dynamic-20{padding-left:20px;padding-right:20px}
.px-dynamic-21{padding-left:21px;padding-right:21px}
.px-dynamic-22{padding-left:22px;padding-right:22px}
.px-dynamic-23{padding-left:23px;padding-right:23px}
.px-dynamic-24{padding-left:24px;padding-right:24px}
.px-dynamic-25{padding-left:25px;padding-right:25px}
.px-dynamic-26{padding-left:26px;padding-right:26px}
.px-dynamic-27{padding-left:27px;padding-right:27px}
.px-dynamic-28{padding-left:28px;padding-right:28px}
.px-dynamic-29{padding-left:29px;padding-right:29px}
.px-dynamic-3{padding-left:3px;padding-right:3px}
.px-dynamic-30{padding-left:30px;padding-right:30px}
.px-dynamic-31{padding-left:31px;padding-right:31px}
.px-dynamic-32{padding-left:32px;padding-right:32px}
.px-dynamic-33{padding-left:33px;padding-right:33px}
.px-dynamic-34{padding-left:34px;padding-right:34px}
.px-dynamic-35{padding-left:35px;padding-right:35px}
.px-dynamic-36{padding-left:36px;padding-right:36px}
.px-dynamic-37{padding-left:37px;padding-right:37px}
.px-dynamic-38{padding-left:38px;padding-right:38px}
.px-dynamic-39{padding-left:39px;padding-right:39px}
.px-dynamic-4{padding-left:4px;padding-right:4px}
.px-dynamic-40{padding-left:40px;padding-right:40px}
.px-dynamic-41{padding-left:41px;padding-right:41px}
.px-dynamic-42{padding-left:42px;padding-right:42px}
.px-dynamic-43{padding-left:43px;padding-right:43px}
.px-dynamic-44{padding-left:44px;padding-right:44px}
.px-dynamic-45{padding-left:45px;padding-right:45px}
.px-dynamic-46{padding-left:46px;padding-right:46px}
.px-dynamic-47{padding-left:47px;padding-right:47px}
.px-dynamic-48{padding-left:48px;padding-right:48px}
.px-dynamic-49{padding-left:49px;padding-right:49px}
.px-dynamic-5{padding-left:5px;padding-right:5px}
.px-dynamic-50{padding-left:50px;padding-right:50px}
.px-dynamic-51{padding-left:51px;padding-right:51px}
.px-dynamic-52{padding-left:52px;padding-right:52px}
.px-dynamic-53{padding-left:53px;padding-right:53px}
.px-dynamic-54{padding-left:54px;padding-right:54px}
.px-dynamic-55{padding-left:55px;padding-right:55px}
.px-dynamic-56{padding-left:56px;padding-right:56px}
.px-dynamic-57{padding-left:57px;padding-right:57px}
.px-dynamic-58{padding-left:58px;padding-right:58px}
.px-dynamic-59{padding-left:59px;padding-right:59px}
.px-dynamic-6{padding-left:6px;padding-right:6px}
.px-dynamic-60{padding-left:60px;padding-right:60px}
.px-dynamic-61{padding-left:61px;padding-right:61px}
.px-dynamic-62{padding-left:62px;padding-right:62px}
.px-dynamic-63{padding-left:63px;padding-right:63px}
.px-dynamic-64{padding-left:64px;padding-right:64px}
.px-dynamic-65{padding-left:65px;padding-right:65px}
.px-dynamic-66{padding-left:66px;padding-right:66px}
.px-dynamic-67{padding-left:67px;padding-right:67px}
.px-dynamic-68{padding-left:68px;padding-right:68px}
.px-dynamic-69{padding-left:69px;padding-right:69px}
.px-dynamic-7{padding-left:7px;padding-right:7px}
.px-dynamic-70{padding-left:70px;padding-right:70px}
.px-dynamic-71{padding-left:71px;padding-right:71px}
.px-dynamic-72{padding-left:72px;padding-right:72px}
.px-dynamic-73{padding-left:73px;padding-right:73px}
.px-dynamic-74{padding-left:74px;padding-right:74px}
.px-dynamic-75{padding-left:75px;padding-right:75px}
.px-dynamic-76{padding-left:76px;padding-right:76px}
.px-dynamic-77{padding-left:77px;padding-right:77px}
.px-dynamic-78{padding-left:78px;padding-right:78px}
.px-dynamic-79{padding-left:79px;padding-right:79px}
.px-dynamic-8{padding-left:8px;padding-right:8px}
.px-dynamic-80{padding-left:80px;padding-right:80px}
.px-dynamic-81{padding-left:81px;padding-right:81px}
.px-dynamic-82{padding-left:82px;padding-right:82px}
.px-dynamic-83{padding-left:83px;padding-right:83px}
.px-dynamic-84{padding-left:84px;padding-right:84px}
.px-dynamic-85{padding-left:85px;padding-right:85px}
.px-dynamic-86{padding-left:86px;padding-right:86px}
.px-dynamic-87{padding-left:87px;padding-right:87px}
.px-dynamic-88{padding-left:88px;padding-right:88px}
.px-dynamic-89{padding-left:89px;padding-right:89px}
.px-dynamic-9{padding-left:9px;padding-right:9px}
.px-dynamic-90{padding-left:90px;padding-right:90px}
.px-dynamic-91{padding-left:91px;padding-right:91px}
.px-dynamic-92{padding-left:92px;padding-right:92px}
.px-dynamic-93{padding-left:93px;padding-right:93px}
.px-dynamic-94{padding-left:94px;padding-right:94px}
.px-dynamic-95{padding-left:95px;padding-right:95px}
.px-dynamic-96{padding-left:96px;padding-right:96px}
.px-dynamic-97{padding-left:97px;padding-right:97px}
.px-dynamic-98{padding-left:98px;padding-right:98px}
.px-dynamic-99{padding-left:99px;padding-right:99px}
.py-\[10px\]{padding-bottom:10px;padding-top:10px}
.py-\[15px\]{padding-bottom:15px;padding-top:15px}
.py-\[1px\]{padding-bottom:1px;padding-top:1px}
.py-\[20px\]{padding-bottom:20px;padding-top:20px}
.py-\[24px\]{padding-bottom:24px;padding-top:24px}
.py-\[48px\]{padding-bottom:48px;padding-top:48px}
.py-\[4px\]{padding-bottom:4px;padding-top:4px}
.py-\[60px\]{padding-bottom:60px;padding-top:60px}
.py-\[8px\]{padding-bottom:8px;padding-top:8px}
.pb-\[0\.3em\]{padding-bottom:.3em}
.pb-\[12px\]{padding-bottom:12px}
.pb-\[16px\]{padding-bottom:16px}
.pb-\[20px\]{padding-bottom:20px}
.pb-\[3px\]{padding-bottom:3px}
.pb-\[48px\]{padding-bottom:48px}
.pb-\[4px\]{padding-bottom:4px}
.pb-\[5px\]{padding-bottom:5px}
.pl-\[16px\]{padding-left:16px}
.pl-\[24px\]{padding-left:24px}
.pr-\[10px\]{padding-right:10px}
.pr-\[24px\]{padding-right:24px}
.pt-\[12px\]{padding-top:12px}
.pt-\[13px\]{padding-top:13px}
.pt-\[16px\]{padding-top:16px}
.pt-\[18px\]{padding-top:18px}
.pt-\[21px\]{padding-top:21px}
.pt-\[22px\]{padding-top:22px}
.pt-\[24px\]{padding-top:24px}
.pt-\[26px\]{padding-top:26px}
.pt-\[30px\]{padding-top:30px}
.pt-\[5px\]{padding-top:5px}
.\!text-\[14px\]{font-size:14px!important}
.\!text-\[16px\]{font-size:16px!important}
.\!text-\[20px\]{font-size:20px!important}
.text-\[13px\]{font-size:13px}
.text-\[18px\]{font-size:18px}
.text-\[22px\]{font-size:22px}
.text-\[26px\]{font-size:26px}
.text-\[30px\]{font-size:30px}
.text-\[32px\]{font-size:32px}
.text-\[36px\]{font-size:36px}
.text-\[40px\]{font-size:40px}
.text-\[4px\]{font-size:4px}
.text-\[50px\]{font-size:50px}
.text-\[60px\]{font-size:60px}
.text-\[64px\]{font-size:64px}
.text-\[90px\]{font-size:90px}
.\!font-\[300\]{font-weight:300!important}
.\!font-\[400\]{font-weight:400!important}
.font-\[500\]{font-weight:500}
.font-\[600\]{font-weight:600}
.\!leading-\[17px\]{line-height:17px!important}
.leading-\[1\.2\]{line-height:1.2}
.leading-\[120\%\]{line-height:120%}
.leading-\[140\%\]{line-height:140%}
.leading-\[15px\]{line-height:15px}
.leading-\[18px\]{line-height:18px}
.leading-\[19px\]{line-height:19px}
.leading-\[20px\]{line-height:20px}
.leading-\[22px\]{line-height:22px}
.leading-\[24px\]{line-height:24px}
.leading-\[25px\]{line-height:25px}
.leading-\[34px\]{line-height:34px}
.leading-\[59px\]{line-height:59px}
.\!text-\[\#69B1FF\]{--tw-text-opacity:1!important;color:rgb(105 177 255/var(--tw-text-opacity,1))!important}
.\!text-\[\#FFFFFFCC\]{color:#ffffffcc!important}
.text-\[\#0E0A2F\]{--tw-text-opacity:1;color:rgb(14 10 47/var(--tw-text-opacity,1))}
.text-\[\#12CBAB\]{--tw-text-opacity:1;color:rgb(18 203 171/var(--tw-text-opacity,1))}
.text-\[\#18191A\]{--tw-text-opacity:1;color:rgb(24 25 26/var(--tw-text-opacity,1))}
.text-\[\#1999FF\]{--tw-text-opacity:1;color:rgb(25 153 255/var(--tw-text-opacity,1))}
.text-\[\#3A3E64\]{--tw-text-opacity:1;color:rgb(58 62 100/var(--tw-text-opacity,1))}
.text-\[\#3C8630\]{--tw-text-opacity:1;color:rgb(60 134 48/var(--tw-text-opacity,1))}
.text-\[\#AFAFAF\]{--tw-text-opacity:1;color:rgb(175 175 175/var(--tw-text-opacity,1))}
.text-\[\#C15755\]{--tw-text-opacity:1;color:rgb(193 87 85/var(--tw-text-opacity,1))}
.text-\[\#DC7439\]{--tw-text-opacity:1;color:rgb(220 116 57/var(--tw-text-opacity,1))}
.text-\[\#F8F8F8\]{--tw-text-opacity:1;color:rgb(248 248 248/var(--tw-text-opacity,1))}
.text-\[\#FE5252\]{--tw-text-opacity:1;color:rgb(254 82 82/var(--tw-text-opacity,1))}
.text-\[\#FF5252\]{--tw-text-opacity:1;color:rgb(255 82 82/var(--tw-text-opacity,1))}
.text-\[\#FFFFFF33\]{color:#ffffff33}
.text-\[\#FFFFFF66\]{color:#ffffff66}
.text-\[\#fe5252\]{--tw-text-opacity:1;color:rgb(254 82 82/var(--tw-text-opacity,1))}
.text-\[\#ffffff1f\]{color:#ffffff1f}
.text-\[\#ffffff3f\]{color:#ffffff3f}
.text-\[rgba\(159\2c 155\2c 171\2c \.8\)\]{color:hsla(255,9%,64%,.8)}
.text-\[white\],.text-white{--tw-text-opacity:1;color:rgb(255 255 255/var(--tw-text-opacity,1))}
.opacity-\[0\.36\]{opacity:.36}
.opacity-\[0\.6\]{opacity:.6}
.shadow,.shadow-\[0_4px_32px_0_rgba\(0\2c 0\2c 0\2c 0\.24\)\]{box-shadow:var(--tw-ring-offset-shadow,0 0 #0000),var(--tw-ring-shadow,0 0 #0000),var(--tw-shadow)}
.shadow-\[0_4px_32px_0_rgba\(0\2c 0\2c 0\2c 0\.24\)\]{--tw-shadow:0 4px 32px 0 rgba(0,0,0,.24);--tw-shadow-colored:0 4px 32px 0 var(--tw-shadow-color)}
.shadow-\[32px_0_60px_0_rgba\(80\2c 129\2c 255\2c 0\.08\)\]{--tw-shadow:32px 0 60px 0 rgba(80,129,255,.08);--tw-shadow-colored:32px 0 60px 0 var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow,0 0 #0000),var(--tw-ring-shadow,0 0 #0000),var(--tw-shadow)}
.blur,.filter{filter:var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow)}
.backdrop-blur-\[10px\]{--tw-backdrop-blur:blur(10px)}
.backdrop-blur-\[10px\],.backdrop-blur-\[2px\]{-webkit-backdrop-filter:var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);backdrop-filter:var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia)}
input[type=password]::-ms-clear,input[type=password]::-ms-reveal{display:none}
.no-scroll::-webkit-scrollbar{display:none;scrollbar-width:none}
.scrollbar-customize::-webkit-scrollbar{height:10px;width:4px}
.scrollbar-customize::-webkit-scrollbar-track{background:transparent;border-radius:10px}
.scrollbar-customize::-webkit-scrollbar-thumb{background:#ffffff1f;border-radius:10px}
.scrollbar-customize::-webkit-scrollbar-thumb:hover{background:transparent}
.no-spin::-webkit-inner-spin-button,.no-spin::-webkit-outer-spin-button{-webkit-appearance:none;margin:0}
input[type=number].no-spin{-moz-appearance:textfield}
input[type=text]{font-size:inherit}
.EmojiPickerReact.epr-dark-theme{--epr-bg-color:#272450;--epr-category-label-bg-color:#272450}
.before\:absolute:before{content:var(--tw-content);position:absolute}
.before\:bottom-0:before{bottom:0;content:var(--tw-content)}
.before\:left-0:before{content:var(--tw-content);left:0}
.before\:left-1\/2:before{content:var(--tw-content);left:50%}
.before\:right-0:before{content:var(--tw-content);right:0}
.before\:top-0:before{content:var(--tw-content);top:0}
.before\:z-10:before{content:var(--tw-content);z-index:10}
.before\:block:before{content:var(--tw-content);display:block}
.before\:hidden:before{content:var(--tw-content);display:none}
.before\:h-\[10px\]:before{content:var(--tw-content);height:10px}
.before\:h-\[16px\]:before{content:var(--tw-content);height:16px}
.before\:h-full:before{content:var(--tw-content);height:100%}
.before\:w-0:before{content:var(--tw-content);width:0}
.before\:w-\[10px\]:before{content:var(--tw-content);width:10px}
.before\:w-\[4px\]:before{content:var(--tw-content);width:4px}
.before\:w-full:before{content:var(--tw-content);width:100%}
.before\:-translate-x-1\/2:before{--tw-translate-x:-50%}
.before\:-translate-x-1\/2:before,.before\:transform:before{content:var(--tw-content);transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}
@keyframes slide-wave{0%{content:var(--tw-content);transform:translateX(400%)}to{content:var(--tw-content);transform:translateX(-100%)}}
.before\:animate-slide-wave:before{animation:slide-wave .8s linear infinite;content:var(--tw-content)}
.before\:rounded-full:before{border-radius:9999px;content:var(--tw-content)}
.before\:border-\[4px\]:before{border-width:4px;content:var(--tw-content)}
.before\:border-\[\#F0F5FF\]:before{content:var(--tw-content);--tw-border-opacity:1;border-color:rgb(240 245 255/var(--tw-border-opacity,1))}
.before\:bg-\[\#4B7DFF\]:before{content:var(--tw-content);--tw-bg-opacity:1;background-color:rgb(75 125 255/var(--tw-bg-opacity,1))}
.before\:bg-\[\#fff\]:before{content:var(--tw-content);--tw-bg-opacity:1;background-color:rgb(255 255 255/var(--tw-bg-opacity,1))}
.before\:bg-gradient-to-r:before{background-image:linear-gradient(to right,var(--tw-gradient-stops));content:var(--tw-content)}
.before\:from-transparent:before{content:var(--tw-content);--tw-gradient-from:transparent var(--tw-gradient-from-position);--tw-gradient-to:transparent var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from),var(--tw-gradient-to)}
.before\:via-\[\#fff\]:before{content:var(--tw-content);--tw-gradient-to:hsla(0,0%,100%,0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from),#fff var(--tw-gradient-via-position),var(--tw-gradient-to)}
.before\:p-\[3px\]:before{content:var(--tw-content);padding:3px}
.before\:transition-all:before{content:var(--tw-content);transition-duration:.15s;transition-property:all;transition-timing-function:cubic-bezier(.4,0,.2,1)}
.before\:duration-\[4500ms\]:before{content:var(--tw-content);transition-duration:4.5s}
.before\:content-\[\"\"\]:before,.before\:content-\[\'\'\]:before{--tw-content:"";content:var(--tw-content)}
.first\:mt-0:first-child{margin-top:0}
.last\:border-none:last-child{border-style:none}
.last\:pr-0:last-child{padding-right:0}
.checked\:border-\[\#4B7DFF\]:checked{--tw-border-opacity:1;border-color:rgb(75 125 255/var(--tw-border-opacity,1))}
.checked\:bg-\[\#4B7DFF\]:checked{--tw-bg-opacity:1;background-color:rgb(75 125 255/var(--tw-bg-opacity,1))}
.checked\:before\:block:checked:before{content:var(--tw-content);display:block}
.checked\:before\:border-\[\#D6E4FF\]:checked:before{content:var(--tw-content);--tw-border-opacity:1;border-color:rgb(214 228 255/var(--tw-border-opacity,1))}
.focus-within\:border:focus-within{border-width:1px}
.focus-within\:border-\[\#69B1FF\]:focus-within{--tw-border-opacity:1;border-color:rgb(105 177 255/var(--tw-border-opacity,1))}
.focus-within\:border-\[\#FF5252\]:focus-within{--tw-border-opacity:1;border-color:rgb(255 82 82/var(--tw-border-opacity,1))}
.hover\:scale-110:hover{transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));--tw-scale-x:1.1;--tw-scale-y:1.1;transform:translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}
.hover\:rounded-full:hover{border-radius:9999px}
.hover\:border:hover{border-width:1px}
.hover\:border-\[\#2A2D4F\]:hover{--tw-border-opacity:1;border-color:rgb(42 45 79/var(--tw-border-opacity,1))}
.hover\:border-\[\#3463DB\]:hover{border-color:rgb(52 99 219/var(--tw-border-opacity,1));--tw-border-opacity:1;border-color:rgb(52 99 219 / var(--tw-border-opacity, 1))}
.hover\:border-\[\#4B7DFF\]:hover{border-color:rgb(75 125 255/var(--tw-border-opacity,1));--tw-border-opacity:1;border-color:rgb(75 125 255 / var(--tw-border-opacity, 1))}
.hover\:border-\[\#69B1FF\]:hover{--tw-border-opacity:1;border-color:rgb(105 177 255/var(--tw-border-opacity,1))}
.hover\:\!bg-\[\#3463DB\]:hover{--tw-bg-opacity:1!important;background-color:rgb(52 99 219/var(--tw-bg-opacity,1))!important}
.hover\:\!bg-\[\#5765F2\]:hover{--tw-bg-opacity:1!important;background-color:rgb(87 101 242/var(--tw-bg-opacity,1))!important}
.hover\:bg-\[\#2A2D4F\]:hover{background-color:rgb(42 45 79/var(--tw-bg-opacity,1));--tw-bg-opacity:1;background-color:rgb(42 45 79 / var(--tw-bg-opacity, 1))}
.hover\:bg-\[\#3463DB\]:hover,.hover\:bg-\[\#3463db\]:hover{--tw-bg-opacity:1;background-color:rgb(52 99 219/var(--tw-bg-opacity,1))}
.hover\:bg-\[\#4B7DFF\]:hover{background-color:rgb(75 125 255/var(--tw-bg-opacity,1));--tw-bg-opacity:1;background-color:rgb(75 125 255 / var(--tw-bg-opacity, 1))}
.hover\:bg-\[\#5081FF33\]:hover{background-color:#5081ff33}
.hover\:bg-\[\#5081FF\]:hover{--tw-bg-opacity:1;background-color:rgb(80 129 255/var(--tw-bg-opacity,1))}
.hover\:bg-\[\#5081ff33\]:hover{background-color:#5081ff33}
.hover\:bg-\[\#FFFFFF1F\]:hover{background-color:#ffffff1f}
.hover\:bg-gradient-to-r:hover{background-image:linear-gradient(to right,var(--tw-gradient-stops))}
.hover\:from-\[\#FFFFFF00\]:hover{--tw-gradient-from:#ffffff00 var(--tw-gradient-from-position);--tw-gradient-to:hsla(0,0%,100%,0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from),var(--tw-gradient-to)}
.hover\:to-\[\#FFFFFF3D\]:hover{--tw-gradient-to:#ffffff3d var(--tw-gradient-to-position)}
.hover\:text-\[\#2A2D4F\]:hover{--tw-text-opacity:1;color:rgb(42 45 79/var(--tw-text-opacity,1))}
.hover\:text-\[\#3463DB\]:hover{--tw-text-opacity:1;color:rgb(52 99 219/var(--tw-text-opacity,1))}
.hover\:text-\[\#4B7DFF\]:hover{color:rgb(75 125 255/var(--tw-text-opacity,1));--tw-text-opacity:1;color:rgb(75 125 255 / var(--tw-text-opacity, 1))}
.hover\:text-\[\#69B1FF\]:hover{--tw-text-opacity:1;color:rgb(105 177 255/var(--tw-text-opacity,1))}
.hover\:text-\[\#FFFFFFCC\]:hover{color:#ffffffcc}
.hover\:text-\[\#fff\]:hover,.hover\:text-white:hover{--tw-text-opacity:1;color:rgb(255 255 255/var(--tw-text-opacity,1))}
.hover\:underline:hover{text-decoration-line:underline}
.hover\:opacity-\[0\.7\]:hover{opacity:.7}
.hover\:brightness-125:hover{--tw-brightness:brightness(1.25);filter:var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow)}
.checked\:hover\:bg-\[\#4B7DFF\]:hover:checked{--tw-bg-opacity:1;background-color:rgb(75 125 255/var(--tw-bg-opacity,1))}
.focus\:outline-none:focus{outline:2px solid transparent;outline-offset:2px}
.focus\:ring-0:focus{--tw-ring-offset-shadow:var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);--tw-ring-shadow:var(--tw-ring-inset) 0 0 0 calc(var(--tw-ring-offset-width)) var(--tw-ring-color);box-shadow:var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow,0 0 #0000)}
.focus-visible\:border-\[\#4B7DFF\]:focus-visible{--tw-border-opacity:1;border-color:rgb(75 125 255/var(--tw-border-opacity,1))}
.focus-visible\:bg-\[\#112242\]:focus-visible{--tw-bg-opacity:1;background-color:rgb(17 34 66/var(--tw-bg-opacity,1))}
.focus-visible\:bg-\[\#162041\]:focus-visible{--tw-bg-opacity:1;background-color:rgb(22 32 65/var(--tw-bg-opacity,1))}
.focus-visible\:outline-0:focus-visible{outline-width:0}
.enabled\:checked\:border-\[\#4B7DFF\]:checked:enabled{--tw-border-opacity:1;border-color:rgb(75 125 255/var(--tw-border-opacity,1))}
.enabled\:checked\:bg-\[\#4B7DFF\]:checked:enabled{--tw-bg-opacity:1;background-color:rgb(75 125 255/var(--tw-bg-opacity,1))}
.disabled\:cursor-not-allowed:disabled{cursor:not-allowed}
.disabled\:border-\[rgba\(159\2c 155\2c 171\2c \.8\)\]:disabled{border-color:hsla(255,9%,64%,.8)}
.disabled\:bg-\[rgba\(159\2c 155\2c 171\2c \.2\)\]:disabled{background-color:hsla(255,9%,64%,.2)}
.disabled\:text-\[\#ffffff3f\]:disabled{color:#ffffff3f}
.disabled\:opacity-50:disabled{opacity:.5;opacity:0.5}
.group:last-child .group-last\:pb-0{padding-bottom:0}
.group:hover .group-hover\:block{display:block}
.group:hover .group-hover\:scale-110{transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));--tw-scale-x:1.1;--tw-scale-y:1.1;transform:translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}
@keyframes circle-wave{0%{opacity:0;transform:scale(0)}50%{opacity:1}to{opacity:0;transform:scale(1.2)}}
.group:hover .group-hover\:animate-circle-wave{animation:circle-wave 1s ease-out 1 forwards}
.group:hover .group-hover\:text-\[\#69B1FF\]{--tw-text-opacity:1;color:rgb(105 177 255/var(--tw-text-opacity,1))}
.group:hover .group-hover\:opacity-\[1\]{opacity:1}
.group:hover .enabled\:group-hover\:border-\[\#4B7DFF\]:enabled{--tw-border-opacity:1;border-color:rgb(75 125 255/var(--tw-border-opacity,1))}
.group:hover .enabled\:group-hover\:bg-\[\#5081FF33\]:enabled{background-color:#5081ff33}
.group:hover .enabled\:checked\:group-hover\:bg-\[\#4B7DFF\]:checked:enabled{--tw-bg-opacity:1;background-color:rgb(75 125 255/var(--tw-bg-opacity,1))}
.peer:checked~.peer-checked\:translate-x-full{transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));--tw-translate-x:100%;transform:translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}
.peer:checked~.peer-checked\:bg-blue-500{background-color:rgb(59 130 246/var(--tw-bg-opacity,1));--tw-bg-opacity:1;background-color:rgb(59 130 246 / var(--tw-bg-opacity, 1))}
.peer:checked~.peer-checked\:opacity-100{opacity:1}
.peer:-moz-placeholder~.peer-placeholder-shown\:translate-y-0{--tw-translate-y:0px;transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}
.peer:placeholder-shown~.peer-placeholder-shown\:translate-y-0{--tw-translate-y:0px;transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}
.peer:-moz-placeholder~.peer-placeholder-shown\:scale-100{--tw-scale-x:1;--tw-scale-y:1;transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}
.peer:placeholder-shown~.peer-placeholder-shown\:scale-100{--tw-scale-x:1;--tw-scale-y:1;transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}
.peer:focus~.peer-focus\:-translate-y-2{--tw-translate-y:-0.5rem}
.peer:focus~.peer-focus\:-translate-y-2,.peer:focus~.peer-focus\:scale-90{transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}
.peer:focus~.peer-focus\:scale-90{--tw-scale-x:.9;--tw-scale-y:.9}
.peer:focus~.peer-focus\:text-\[\#FFFFFF99\]{color:#ffffff99}
.\[\&_a\:hover\]\:underline a:hover{text-decoration-line:underline}
.\[\&_a\]\:text-\[\#69B1FF\] a{--tw-text-opacity:1;color:rgb(105 177 255/var(--tw-text-opacity,1))}
.bg-dark{
    background-color:rgb(14 23 38 / var(--tw-bg-opacity));
}
.bg-dark,.bg-light{
    --tw-bg-opacity:1;
}
.bg-light{
    background-color:rgb(250 250 250 / var(--tw-bg-opacity));
}
.bg-primary{
    --tw-bg-opacity:1;
    background-color:rgb(67 97 238 / var(--tw-bg-opacity));
}
.bg-secondary{
    --tw-bg-opacity:1;
    background-color:rgb(128 93 202 / var(--tw-bg-opacity));
}
.bg-success{
    --tw-bg-opacity:1;
    background-color:rgb(0 171 85 / var(--tw-bg-opacity));
}
.bg-info{
    --tw-bg-opacity:1;
    background-color:rgb(33 150 243 / var(--tw-bg-opacity));
}
.bg-warning{
    --tw-bg-opacity:1;
    background-color:rgb(226 160 63 / var(--tw-bg-opacity));
}
.bg-danger{
    --tw-bg-opacity:1;
    background-color:rgb(231 81 90 / var(--tw-bg-opacity));
}
.dark .bg-dark{
    --tw-bg-opacity:1;
    background-color:rgb(6 8 24 / var(--tw-bg-opacity));
}
.dark .bg-white{
    --tw-bg-opacity:1;
    background-color:rgb(14 23 38 / var(--tw-bg-opacity));
}
.dark .bg-gray-50,.dark .bg-light{
    --tw-bg-opacity:1;
    background-color:rgb(26 41 65 / var(--tw-bg-opacity));
}
.dark .bg-gray-100{
    --tw-bg-opacity:1;
    background-color:rgb(25 30 58 / var(--tw-bg-opacity));
}
.dark .bg-gray-200{
    --tw-bg-opacity:1;
    background-color:rgb(59 63 92 / var(--tw-bg-opacity));
}
.dark .bg-gray-300{
    --tw-bg-opacity:1;
    background-color:rgb(136 142 168 / var(--tw-bg-opacity));
}
.bg-black\/10{
    background-color:rgba(0,0,0,.1);
}
.bg-black\/20{
    background-color:rgba(0,0,0,.2);
}
.bg-black\/30{
    background-color:rgba(0,0,0,.3);
}
.bg-black\/50{
    background-color:rgba(0,0,0,.5);
}
.bg-white\/20{
    background-color:hsla(0,0%,100%,.2);
}
.bg-white\/30{
    background-color:hsla(0,0%,100%,.3);
}
.bg-gradient-primary{
    background:linear-gradient(135deg, #4361ee, #805dca);
}
.bg-gradient-success{
    background:linear-gradient(135deg, #00ab55, #2196f3);
}
.bg-gradient-warning{
    background:linear-gradient(135deg, #e2a03f, #e7515a);
}
.bg-gradient-dark{
    background:linear-gradient(135deg, #0e1726, #1a2941);
}
.gaming-ripple{
  animation:gaming-ripple-animation 0.6s linear;
  background:hsla(0,0%,100%,.3);
  border-radius:50%;
  pointer-events:none;
  position:absolute;
  transform:scale(0);
}
.gaming-pagination-bullet{
  background:rgba(80,129,255,.4);
  border:3px solid rgba(80,129,255,.6);
  border-radius:50%;
  box-shadow:0 4px 15px rgba(0,0,0,.2),0 0 10px rgba(80,129,255,.3);
  cursor:pointer;
  display:inline-block;
  height:16px;
  margin:0 10px;
  transition:all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  visibility:visible;
  width:16px;
  z-index:10;
}
.gaming-pagination-bullet:hover{
  background:rgba(80,129,255,.7);
  border-color:rgba(80,129,255,.8);
  box-shadow:0 6px 20px rgba(0,0,0,.3),0 0 15px rgba(80,129,255,.5);
  transform:scale(1.2);
}
.gaming-pagination-bullet.swiper-pagination-bullet-active{
  background:linear-gradient(135deg, #5081ff, #3463db);
  border-color:#5081ff;
  box-shadow:0 8px 25px rgba(0,0,0,.3),0 0 20px rgba(80,129,255,.7);
  transform:scale(1.3);
}
.tippy-box[data-theme~=gaming]{
  -webkit-backdrop-filter:blur(8px);
          backdrop-filter:blur(8px);
  background:linear-gradient(135deg, 
    rgba(30,41,59,.95), 
    rgba(15,23,42,.98));
  border:1px solid rgba(80,129,255,.3);
  border-radius:0.5rem;
  box-shadow:0 10px 25px rgba(0,0,0,.3);
}
.tippy-box[data-theme~=gaming] .tippy-content{
  color:#fff;
  font-weight:500;
  padding:0.5rem 0.75rem;
}
.gaming-account-container{
    background:linear-gradient(135deg,
      rgba(15,23,42,.95),
      rgba(30,41,59,.9) 50%,
      rgba(15,23,42,.95));
    background:linear-gradient(135deg, #0f172a, #1e293b 50%, #0f172a);
    min-height:100vh;
    overflow:hidden;
    overflow-x:hidden;
    position:relative;
  }
.gaming-account-container:before{
    background:radial-gradient(circle at 20% 80%, rgba(80,129,255,.1) 0%, transparent 50%), radial-gradient(circle at 80% 20%, rgba(52,99,219,.1) 0%, transparent 50%), radial-gradient(circle at 40% 40%, rgba(139,92,246,.05) 0%, transparent 50%);
    background:radial-gradient(circle at 20% 50%, rgba(59,130,246,.1) 0%, transparent 50%), radial-gradient(circle at 80% 20%, rgba(147,51,234,.1) 0%, transparent 50%);
    bottom:0;
    content:"";
    left:0;
    pointer-events:none;
    position:absolute;
    right:0;
    top:0;
    z-index:1;
  }
.gaming-account-card{
    backdrop-filter:blur(12px);
    -webkit-backdrop-filter:blur(12px);
    -webkit-backdrop-filter:blur(10px);
            backdrop-filter:blur(10px);
    background:linear-gradient(145deg,
      rgba(30,41,59,.8),
      rgba(15,23,42,.9) 50%,
      rgba(30,41,59,.8));
    background:rgba(30,41,59,.95);
    border:2px solid rgba(80,129,255,.3);
    border:1px solid rgba(59,130,246,.2);
    border-radius:1rem;
    box-shadow:0 20px 25px -5px rgba(0,0,0,.3),0 10px 10px -5px rgba(0,0,0,.2),0 0 0 1px rgba(80,129,255,.1),inset 0 1px 0 hsla(0,0%,100%,.1);
    box-shadow:0 25px 50px -12px rgba(0,0,0,.5);
    position:relative;
    transition:all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    z-index:2;
  }
.gaming-account-card:hover{
    border-color:rgba(80,129,255,.5);
    border-color:rgba(59,130,246,.4);
    box-shadow:0 25px 50px rgba(80,129,255,.2),0 0 30px rgba(80,129,255,.1),inset 0 1px 0 hsla(0,0%,100%,.15);
    box-shadow:0 25px 50px -12px rgba(59,130,246,.2);
    transform:translateY(-2px);
  }
.gaming-image-gallery{
    background:linear-gradient(145deg,
      rgba(20,25,40,.95),
      rgba(10,15,30,.98));
    border:3px solid rgba(80,129,255,.6);
    border-radius:1rem;
    box-shadow:0 8px 32px rgba(0,0,0,.3),0 0 0 1px rgba(80,129,255,.2),inset 0 1px 0 hsla(0,0%,100%,.1);
    display:block !important;
    margin:0 auto;
   
    max-height:85vh;
    max-width:100%;
    overflow:visible;
    padding:0;
    position:relative;
    visibility:visible !important;
    width:100%;
  }
.gaming-image-gallery .relative{
    align-items:center !important;
    display:flex !important;
    justify-content:center !important;
    z-index:1 !important;
  }
.gaming-image-gallery .relative,.gaming-image-gallery .swiper{
    height:auto !important;
  
    max-height:85vh !important;
    position:relative !important;
    visibility:visible !important;
  }
.gaming-image-gallery .swiper{
    border-radius:0.75rem;
    display:block !important;
    overflow:hidden;
    z-index:2 !important;
  }
.gaming-image-gallery .swiper-slide{
    align-items:center !important;
    display:flex !important;
    height:auto !important;
    justify-content:center !important;
    
    max-height:85vh !important;
    position:relative !important;
    visibility:visible !important;
    z-index:3 !important;
  }
.gaming-image-gallery:before{
    background:linear-gradient(45deg,
      rgba(80,129,255,.6),
      rgba(52,99,219,.4),
      rgba(139,92,246,.5));
    border-radius:1.125rem;
    bottom:-3px;
    content:"";
    filter:blur(8px);
    left:-3px;
    opacity:0.3;
    position:absolute;
    right:-3px;
    top:-3px;
    transition:opacity 0.3s ease;
    z-index:-1;
  }
.gaming-image-gallery:hover:before{
    filter:blur(12px);
    opacity:0.8;
  }
.gaming-btn-primary:before{
    background:linear-gradient(90deg, transparent, hsla(0,0%,100%,.2), transparent);
    content:"";
    height:100%;
    left:-100%;
    position:absolute;
    top:0;
    transition:left 0.5s;
    width:100%;
  }
.gaming-btn-primary:hover:before{
    left:100%;
  }
.gaming-info-card{
    -webkit-backdrop-filter:blur(8px);
            backdrop-filter:blur(8px);
    background:linear-gradient(135deg,
      rgba(80,129,255,.1),
      rgba(52,99,219,.05));
    background:rgba(55,65,81,.6);
    border:1px solid rgba(80,129,255,.2);
    border:1px solid rgba(75,85,99,.3);
    border-radius:0.75rem;
    border-radius:0.5rem;
    padding:1rem;
    padding:0.75rem;
    transition:all 0.3s ease;
    transition:all 0.2s ease;
  }
.gaming-info-card:hover{
    background:linear-gradient(135deg,
      rgba(80,129,255,.15),
      rgba(52,99,219,.1));
    background:rgba(55,65,81,.8);
    border-color:rgba(80,129,255,.4);
    border-color:rgba(59,130,246,.3);
  }
.gaming-title{
    background:linear-gradient(135deg, #fff, #c7d2fe);
    -webkit-text-fill-color:transparent;
    animation:gradientShift 3s ease-in-out infinite;
    background:linear-gradient(135deg, #3b82f6, #8b5cf6, #3b82f6);
    -webkit-background-clip:text;
    background-clip:text;
    background-size:200% 200%;
    color:transparent;
    font-weight:700;
    letter-spacing:0.05em;
    text-transform:uppercase;
  }
.gaming-accent-text{
    color:#5081ff;
    color:#3b82f6;
    font-weight:600;
    text-shadow:0 0 10px rgba(59,130,246,.3);
  }
.gaming-status-available{
    background:linear-gradient(135deg, #10b981, #059669);
  }
.gaming-status-available,.gaming-status-sold{
    border-radius:0.5rem;
    color:#fff;
    font-weight:600;
    padding:0.5rem 1rem;
    text-align:center;
  }
.gaming-status-sold{
    background:linear-gradient(135deg, #ef4444, #dc2626);
    border-radius:0.75rem;
    box-shadow:0 10px 25px -5px rgba(239,68,68,.3);
    padding:0.75rem 1rem;
  }
.gaming-attribute-icon{
    align-items:center;
    background:linear-gradient(145deg,
      rgba(30,41,59,.8),
      rgba(15,23,42,.9));
    border:2px solid rgba(80,129,255,.3);
    border-radius:0.5rem;
    border-radius:0.75rem;
    box-shadow:0 8px 25px rgba(0,0,0,.2),0 0 20px rgba(80,129,255,.1);
    display:flex;
    height:3rem;
    height:2.5rem;
    justify-content:center;
    overflow:hidden;
    position:relative;
    transition:all 0.3s ease;
    width:3rem;
    width:2.5rem;
  }
.gaming-attribute-icon:before{
    background-image:url(/assets/image/d7bb309392a99e4179abbf83cba73c3d.png);
    background-position:50%;
    background-size:cover;
    content:"";
    inset:0;
    opacity:0.3;
    position:absolute;
  }
.gaming-attribute-icon:hover{
    border-color:rgba(80,129,255,.6);
    box-shadow:0 8px 25px rgba(80,129,255,.3);
    box-shadow:0 12px 35px rgba(0,0,0,.3),0 0 30px rgba(80,129,255,.2);
    transform:scale(1.05);
    transform:translateY(-2px) scale(1.05);
  }
.gaming-attribute-icon img{
    height:100%;
    -o-object-fit:cover;
       object-fit:cover;
    position:relative;
    width:100%;
    z-index:2;
  }
.gaming-attributes-container{
    backdrop-filter:blur(16px);
    -webkit-backdrop-filter:blur(16px);
    background:linear-gradient(145deg,
      rgba(30,41,59,.95),
      rgba(15,23,42,.98) 50%,
      rgba(30,41,59,.95));
    border:2px solid rgba(80,129,255,.4);
    border-radius:1.25rem;
    box-shadow:0 25px 50px rgba(0,0,0,.4),0 0 0 1px rgba(80,129,255,.1),inset 0 1px 0 hsla(0,0%,100%,.1);
    overflow:hidden;
    position:relative;
  }
.gaming-attributes-container:before{
    background:radial-gradient(circle at 20% 20%, rgba(80,129,255,.1) 0%, transparent 50%), radial-gradient(circle at 80% 80%, rgba(139,92,246,.1) 0%, transparent 50%), radial-gradient(circle at 40% 60%, rgba(52,99,219,.05) 0%, transparent 50%);
    bottom:0;
    content:"";
    left:0;
    pointer-events:none;
    position:absolute;
    right:0;
    top:0;
    z-index:0;
  }
.gaming-attributes-header{
    background:linear-gradient(135deg,
      rgba(80,129,255,.05),
      rgba(52,99,219,.03));
    border-bottom:1px solid rgba(80,129,255,.2);
    padding:2rem 2rem 1rem 2rem;
    position:relative;
    z-index:1;
  }
.gaming-attributes-title{
    background:linear-gradient(135deg, #fff, #c7d2fe 50%, #5081ff);
    -webkit-background-clip:text;
    -webkit-text-fill-color:transparent;
    background-clip:text;
    font-size:1.75rem;
    font-weight:700;
    letter-spacing:0.05em;
    margin-bottom:0.5rem;
    text-transform:uppercase;
  }
.gaming-attributes-subtitle{
    color:rgba(156,163,175,.9);
    font-size:0.95rem;
    font-weight:400;
    line-height:1.5;
  }
.gaming-attributes-icon{
    background:linear-gradient(135deg, #5081ff, #8b5cf6);
    border-radius:0.75rem;
    box-shadow:0 8px 25px rgba(80,129,255,.3),0 0 20px rgba(80,129,255,.2);
    padding:0.75rem;
    position:relative;
  }
.gaming-attributes-icon:before{
    background:linear-gradient(45deg, #5081ff, #8b5cf6, #3463db);
    border-radius:0.875rem;
    content:"";
    filter:blur(4px);
    inset:-2px;
    opacity:0.6;
    position:absolute;
    z-index:-1;
  }
.gaming-sidebar-thumbnails{
    backdrop-filter:blur(12px);
    -webkit-backdrop-filter:blur(12px);
    background:linear-gradient(135deg,
      rgba(20,25,40,.95),
      rgba(10,15,30,.98));
    border:2px solid rgba(80,129,255,.4);
    border-radius:0.75rem;
    box-shadow:0 8px 25px rgba(0,0,0,.3),0 0 0 1px rgba(80,129,255,.2),inset 0 1px 0 hsla(0,0%,100%,.1);
    padding:1rem;
    transition:all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }
.gaming-sidebar-thumbnails:hover{
    border-color:rgba(80,129,255,.6);
    box-shadow:0 12px 35px rgba(0,0,0,.4),0 0 0 1px rgba(80,129,255,.3),0 0 20px rgba(80,129,255,.15),inset 0 1px 0 hsla(0,0%,100%,.15);
  }
.gaming-sidebar-thumbnail-grid{
    display:grid;
    gap:0.5rem;
    grid-template-columns:repeat(auto-fill, minmax(60px, 1fr));
    margin-top:0.75rem;
    max-height:300px;
    overflow-y:auto;
    scrollbar-color:rgba(80,129,255,.5) rgba(30,41,59,.3);
    scrollbar-width:thin;
  }
.gaming-sidebar-thumbnail{
    aspect-ratio:1;
    background:linear-gradient(135deg,
      rgba(30,41,59,.8),
      rgba(15,23,42,.9));
    border:2px solid rgba(80,129,255,.3);
    border-radius:0.5rem;
    cursor:pointer;
    overflow:hidden;
    position:relative;
    transition:all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }
.gaming-sidebar-thumbnail:hover{
    border-color:rgba(80,129,255,.6);
    box-shadow:0 8px 20px rgba(80,129,255,.2);
    transform:translateY(-2px);
  }
.gaming-sidebar-thumbnail.active{
    border-color:rgba(80,129,255,.8);
    box-shadow:0 0 15px rgba(80,129,255,.4);
  }
.gaming-sidebar-thumbnail img{
    height:100%;
    -o-object-fit:cover;
       object-fit:cover;
    transition:all 0.3s ease;
    width:100%;
  }
.gaming-main-gallery{
    -webkit-backdrop-filter:blur(10px);
            backdrop-filter:blur(10px);
    background:linear-gradient(135deg,
      rgba(20,25,40,.95),
      rgba(10,15,30,.98));
    background:rgba(17,24,39,.8);
    border:2px solid rgba(80,129,255,.4);
    border:1px solid rgba(59,130,246,.2);
    border-radius:1rem;
    box-shadow:0 10px 30px rgba(0,0,0,.3),0 0 0 1px rgba(80,129,255,.2),inset 0 1px 0 hsla(0,0%,100%,.1);
    overflow:hidden;
  }
.gaming-grid-header{
    background:linear-gradient(135deg,
      rgba(30,41,59,.8),
      rgba(15,23,42,.9));
    background:linear-gradient(135deg, rgba(59,130,246,.1), rgba(147,51,234,.1));
    border-bottom:1px solid rgba(80,129,255,.2);
    border-bottom:1px solid rgba(59,130,246,.2);
    padding:1.5rem;
    padding:1rem 1.5rem;
  }
.gaming-grid-stats{
    background:linear-gradient(135deg,
      rgba(80,129,255,.1),
      rgba(52,99,219,.1));
    border:1px solid rgba(80,129,255,.3);
    border-radius:0.5rem;
    font-size:0.875rem;
    padding:0.5rem 1rem;
  }
.gaming-grid-container{
    position:relative;
   
  }
.gaming-featured-display{
    background:rgba(0,0,0,.3);
    padding:1.5rem;
    position:relative;
    
  }
.gaming-grid-display{
    padding:1.5rem;
   
  }
.gaming-grid-items{
    display:grid;
    gap:1rem;
    grid-template-columns:repeat(auto-fill, minmax(150px, 1fr));
    max-height:600px;
    overflow-y:auto;
    scrollbar-color:rgba(80,129,255,.5) rgba(30,41,59,.3);
    scrollbar-width:thin;
  }
.gaming-grid-item{
    aspect-ratio:1;
    background:linear-gradient(135deg,
      rgba(30,41,59,.8),
      rgba(15,23,42,.9));
    border:2px solid rgba(80,129,255,.3);
    border-radius:0.75rem;
    cursor:pointer;
    overflow:hidden;
    position:relative;
    transition:all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }
.gaming-grid-item:hover{
    border-color:rgba(80,129,255,.6);
    box-shadow:0 12px 25px rgba(80,129,255,.2);
    transform:translateY(-4px);
  }
.gaming-grid-item.active{
    border-color:rgba(80,129,255,.8);
    box-shadow:0 0 20px rgba(80,129,255,.4);
  }
.gaming-grid-item-container{
    height:100%;
    overflow:hidden;
    position:relative;
    width:100%;
  }
.gaming-grid-item-image{
    height:100%;
    -o-object-fit:cover;
       object-fit:cover;
    transition:all 0.3s ease;
    width:100%;
  }
.gaming-grid-item:hover .gaming-grid-item-image{
    transform:scale(1.05);
  }
.gaming-grid-item-overlay{
    align-items:center;
    background:linear-gradient(135deg,
      rgba(80,129,255,.1),
      rgba(52,99,219,.2));
    display:flex;
    inset:0;
    justify-content:center;
    opacity:0;
    position:absolute;
    transition:all 0.3s ease;
  }
.gaming-grid-item:hover .gaming-grid-item-overlay{
    opacity:1;
  }
.gaming-grid-item-hover-icon{
    -webkit-backdrop-filter:blur(8px);
            backdrop-filter:blur(8px);
    background:hsla(0,0%,100%,.1);
    border:1px solid hsla(0,0%,100%,.2);
    border-radius:50%;
    padding:0.75rem;
    transform:scale(0.8);
    transition:all 0.3s ease;
  }
.gaming-grid-item:hover .gaming-grid-item-hover-icon{
    transform:scale(1);
  }
.gaming-grid-item-placeholder{
    align-items:center;
    background:linear-gradient(135deg,
      rgba(55,65,81,.5),
      rgba(31,41,55,.7));
    color:rgba(156,163,175,.5);
    display:flex;
    height:100%;
    justify-content:center;
    width:100%;
  }
.gaming-grid-item-placeholder svg{
    height:3rem;
    width:3rem;
  }
.gaming-thumbnail-display{
    padding:1.5rem;

  }
.gaming-thumbnail-header{
    align-items:center;
    border-bottom:1px solid rgba(80,129,255,.2);
    display:flex;
    justify-content:space-between;
    margin-bottom:1.5rem;
    margin-bottom:0.5rem;
    padding-bottom:1rem;
    padding-bottom:0.5rem;
  }
.gaming-thumbnail-title{
    align-items:center;
    color:hsla(0,0%,100%,.9);
    color:#fff;
    display:flex;
    font-size:1.125rem;
    font-size:0.875rem;
    font-weight:600;
    gap:0.5rem;
  }
.gaming-thumbnail-count{
    background:linear-gradient(135deg,
      rgba(80,129,255,.1),
      rgba(52,99,219,.1));
    background:linear-gradient(135deg, #5081ff, #3463db);
    border:1px solid rgba(80,129,255,.3);
    border-radius:0.5rem;
    border-radius:0.375rem;
    color:rgba(80,129,255,.9);
    color:#fff;
    font-size:0.875rem;
    font-size:0.75rem;
    font-weight:600;
    padding:0.5rem 1rem;
    padding:0.25rem 0.5rem;
  }
.gaming-main-thumbnail-grid{
    display:grid;
    gap:1rem;
    grid-template-columns:repeat(auto-fill, minmax(120px, 1fr));
    max-height:600px;
    overflow-y:auto;
    scrollbar-color:rgba(80,129,255,.5) rgba(30,41,59,.3);
    scrollbar-width:thin;
  }
.gaming-main-thumbnail{
    aspect-ratio:1;
    background:linear-gradient(135deg,
      rgba(30,41,59,.8),
      rgba(15,23,42,.9));
    border:2px solid rgba(80,129,255,.3);
    border-radius:0.75rem;
    cursor:pointer;
    overflow:hidden;
    position:relative;
    transition:all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }
.gaming-main-thumbnail:hover{
    border-color:rgba(80,129,255,.6);
    box-shadow:0 12px 25px rgba(80,129,255,.2);
    transform:translateY(-4px);
  }
.gaming-main-thumbnail.active{
    border-color:rgba(80,129,255,.8);
    box-shadow:0 0 20px rgba(80,129,255,.4);
  }
.gaming-main-thumbnail-container{
    height:100%;
    overflow:hidden;
    position:relative;
    width:100%;
  }
.gaming-main-thumbnail-image{
    height:100%;
    -o-object-fit:cover;
       object-fit:cover;
    transition:all 0.3s ease;
    width:100%;
  }
.gaming-main-thumbnail:hover .gaming-main-thumbnail-image{
    transform:scale(1.05);
  }
.gaming-main-thumbnail-overlay{
    align-items:center;
    background:linear-gradient(135deg,
      rgba(80,129,255,.1),
      rgba(52,99,219,.2));
    display:flex;
    inset:0;
    justify-content:center;
    opacity:0;
    position:absolute;
    transition:all 0.3s ease;
  }
.gaming-main-thumbnail:hover .gaming-main-thumbnail-overlay{
    opacity:1;
  }
.gaming-main-thumbnail-hover-icon{
    -webkit-backdrop-filter:blur(8px);
            backdrop-filter:blur(8px);
    background:hsla(0,0%,100%,.1);
    border:1px solid hsla(0,0%,100%,.2);
    border-radius:50%;
    padding:0.75rem;
    transform:scale(0.8);
    transition:all 0.3s ease;
  }
.gaming-main-thumbnail:hover .gaming-main-thumbnail-hover-icon{
    transform:scale(1);
  }
.gaming-view-controls{
    display:flex;
    gap:0.5rem;
    position:absolute;
    right:1rem;
    top:1rem;
    z-index:20;
  }
.gaming-view-btn{
    align-items:center;
    -webkit-backdrop-filter:blur(8px);
            backdrop-filter:blur(8px);
    background:linear-gradient(135deg,
      rgba(30,41,59,.9),
      rgba(15,23,42,.95));
    border:1px solid rgba(80,129,255,.3);
    border-radius:0.5rem;
    color:rgba(156,163,175,.8);
    cursor:pointer;
    display:flex;
    height:2.5rem;
    justify-content:center;
    transition:all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    width:2.5rem;
  }
.gaming-view-btn:hover{
    border-color:rgba(80,129,255,.6);
    box-shadow:0 4px 12px rgba(80,129,255,.2);
    color:rgba(80,129,255,.9);
    transform:translateY(-1px);
  }
.gaming-view-btn.active{
    background:linear-gradient(135deg,
      rgba(80,129,255,.2),
      rgba(52,99,219,.3));
    border-color:rgba(80,129,255,.6);
    box-shadow:0 0 15px rgba(80,129,255,.3);
    color:#5081ff;
  }
.gaming-thumbnail-gallery{
    display:none !important;
  }
.gaming-thumbnail-gallery:hover{
    border-color:rgba(80,129,255,.6);
    box-shadow:0 15px 40px rgba(0,0,0,.5),0 0 0 1px rgba(80,129,255,.3),0 0 20px rgba(80,129,255,.2),inset 0 1px 0 hsla(0,0%,100%,.15);
  }
.gaming-thumbnail-container{
    align-items:flex-start;
    box-sizing:border-box;
    display:flex !important;
    flex-wrap:wrap;
    gap:0.5rem;
    max-height:120px;
    max-width:100%;
    min-height:60px;
    overflow-x:auto;
    overflow-y:hidden;
    padding:0.5rem 0;
    scrollbar-color:rgba(80,129,255,.5) rgba(30,41,59,.3);
    scrollbar-width:thin;
    width:100%;
    -webkit-overflow-scrolling:touch;
    justify-content:flex-start;
    scroll-behavior:smooth;
  }
.gaming-thumbnail-container::-webkit-scrollbar{
    height:6px;
  }
.gaming-thumbnail-container::-webkit-scrollbar-track{
    background:rgba(30,41,59,.3);
    border-radius:3px;
  }
.gaming-thumbnail-container::-webkit-scrollbar-thumb{
    background:linear-gradient(90deg, #5081ff, #3463db);
    border-radius:3px;
  }
.gaming-thumbnail-container::-webkit-scrollbar-thumb:hover{
    background:linear-gradient(90deg, #6366f1, #4f46e5);
  }
.gaming-thumbnail{
    background:linear-gradient(145deg,
      rgba(30,41,59,.9),
      rgba(15,23,42,.95));
    border:2px solid rgba(80,129,255,.3);
    border-radius:0.375rem;
    box-sizing:border-box;
    cursor:pointer;
    display:block;
    flex-shrink:0;
    margin:0 !important;
    min-height:45px;
    min-width:60px;
    overflow:hidden;
    padding:0 !important;
    position:relative;
    transition:all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    width:60px;
  }
.gaming-thumbnail:hover{
    border-color:rgba(80,129,255,.6);
    box-shadow:0 8px 25px rgba(80,129,255,.3),0 0 20px rgba(80,129,255,.2);
    transform:translateY(-2px) scale(1.05);
  }
.gaming-thumbnail.active{
    animation:thumbnailPulse 2s infinite;
    border-color:rgba(80,129,255,.8);
    box-shadow:0 0 0 2px rgba(80,129,255,.4),0 8px 25px rgba(80,129,255,.4),0 0 30px rgba(80,129,255,.3);
    transform:scale(1.1);
  }
.gaming-thumbnail:before{
    background:linear-gradient(45deg,
      rgba(80,129,255,.6),
      rgba(52,99,219,.4),
      rgba(139,92,246,.5));
    border-radius:0.5rem;
    bottom:-2px;
    content:"";
    left:-2px;
    opacity:0;
    position:absolute;
    right:-2px;
    top:-2px;
    transition:opacity 0.3s ease;
    z-index:-1;
  }
.gaming-thumbnail.active:before{
    opacity:1;
  }
.gaming-thumbnail img{
    border-radius:0.375rem;
    display:block;
    height:100% !important;
    image-rendering:-webkit-optimize-contrast;
    image-rendering:crisp-edges;
    left:0;
    margin:0 !important;
    max-height:100% !important;
    max-width:100% !important;
    -o-object-fit:cover !important;
       object-fit:cover !important;
    -o-object-position:center !important;
       object-position:center !important;
    padding:0 !important;
    position:absolute;
    top:0;
    transition:all 0.3s ease;
    transition:transform 0.3s ease,filter 0.3s ease !important;
    transition:opacity 0.3s ease,transform 0.3s ease;
    width:100% !important;
    z-index:1;
  }
.gaming-thumbnail:hover img{
    filter:brightness(1.1);
    transform:scale(1.1);
  }
.gaming-thumbnail-placeholder{
    align-items:center;
    background:linear-gradient(145deg,
      rgba(75,85,99,.8),
      rgba(55,65,81,.9));
    border-radius:0.375rem;
    box-sizing:border-box;
    color:rgba(156,163,175,.8);
    display:flex;
    height:100%;
    justify-content:center;
    left:0;
    margin:0;
    padding:0;
    position:absolute;
    top:0;
    width:100%;
    z-index:0;
  }
.gaming-thumbnail-placeholder svg{
    height:24px;
    opacity:0.6;
    width:24px;
  }
.gaming-attribute-section{
    background:linear-gradient(135deg,
      rgba(80,129,255,.03),
      rgba(52,99,219,.02));
    border:1px solid rgba(80,129,255,.15);
    border-radius:1rem;
    margin-bottom:2rem;
    padding:1.5rem 2rem;
    position:relative;
    transition:all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    z-index:1;
  }
.gaming-attribute-section:hover{
    background:linear-gradient(135deg,
      rgba(80,129,255,.05),
      rgba(52,99,219,.03));
    border-color:rgba(80,129,255,.25);
    box-shadow:0 8px 25px rgba(80,129,255,.1);
    transform:translateY(-2px);
  }
.gaming-attribute-header{
    align-items:center;
    border-bottom:1px solid rgba(80,129,255,.1);
    display:flex;
    justify-content:space-between;
    margin-bottom:1.5rem;
    padding-bottom:1rem;
  }
.gaming-attribute-title-group{
    align-items:center;
    display:flex;
    gap:0.75rem;
  }
.gaming-attribute-icon.character{
    background:linear-gradient(135deg, #10b981, #059669);
  }
.gaming-attribute-icon.weapon{
    background:linear-gradient(135deg, #f59e0b, #d97706);
  }
.gaming-attribute-icon.skin{
    background:linear-gradient(135deg, #8b5cf6, #7c3aed);
  }
.gaming-attribute-title{
    color:#fff;
    font-size:1.25rem;
    font-weight:600;
    letter-spacing:0.025em;
  }
.gaming-attribute-count{
    -webkit-backdrop-filter:blur(8px);
            backdrop-filter:blur(8px);
    border:1px solid hsla(0,0%,100%,.1);
    border-radius:2rem;
    font-size:0.875rem;
    font-weight:600;
    padding:0.5rem 1rem;
    transition:all 0.3s ease;
  }
.gaming-attribute-count.character{
    background:linear-gradient(135deg, rgba(16,185,129,.2), rgba(5,150,105,.1));
    border-color:rgba(16,185,129,.3);
    color:#34d399;
  }
.gaming-attribute-count.weapon{
    background:linear-gradient(135deg, rgba(245,158,11,.2), rgba(217,119,6,.1));
    border-color:rgba(245,158,11,.3);
    color:#fbbf24;
  }
.gaming-attribute-count.skin{
    background:linear-gradient(135deg, rgba(139,92,246,.2), rgba(124,58,237,.1));
    border-color:rgba(139,92,246,.3);
    color:#a78bfa;
  }
.gaming-attribute-count:hover{
    box-shadow:0 4px 15px rgba(80,129,255,.2);
    transform:scale(1.05);
  }
.gaming-attribute-grid{
    display:grid;
    gap:0.75rem;
    grid-template-columns:repeat(auto-fill, minmax(60px, 1fr));
  }
.gaming-attribute-card{
    aspect-ratio:1;
    background:linear-gradient(145deg,
      rgba(31,41,55,.9),
      rgba(17,24,39,.95));
    border:2px solid hsla(220,9%,46%,.3);
    border:2px solid rgba(59,130,246,.3);
    border-radius:0.875rem;
    border-radius:0.5rem;
    box-shadow:0 4px 15px rgba(0,0,0,.2),inset 0 1px 0 hsla(0,0%,100%,.05);
    cursor:pointer;
    height:50px;
    overflow:hidden;
    position:relative;
    transition:all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    transition:all 0.3s ease;
    width:50px;
  }
.gaming-attribute-card:before{
    background:linear-gradient(45deg,
      transparent,
      rgba(80,129,255,.1) 50%,
      transparent);
    content:"";
    inset:0;
    opacity:0;
    position:absolute;
    transition:opacity 0.3s ease;
    z-index:1;
  }
.gaming-attribute-card:hover:before{
    opacity:1;
  }
.gaming-attribute-card:hover{
    border-color:rgba(80,129,255,.6);
    border-color:rgba(59,130,246,.6);
    box-shadow:0 20px 40px rgba(0,0,0,.3),0 0 30px rgba(80,129,255,.2),inset 0 1px 0 hsla(0,0%,100%,.1);
    box-shadow:0 10px 25px -5px rgba(59,130,246,.3);
    transform:translateY(-4px) scale(1.05);
    transform:scale(1.05);
  }
.gaming-attribute-card.character:hover{
    border-color:rgba(16,185,129,.6);
    box-shadow:0 20px 40px rgba(0,0,0,.3),0 0 30px rgba(16,185,129,.2),inset 0 1px 0 hsla(0,0%,100%,.1);
  }
.gaming-attribute-card.weapon:hover{
    border-color:rgba(245,158,11,.6);
    box-shadow:0 20px 40px rgba(0,0,0,.3),0 0 30px rgba(245,158,11,.2),inset 0 1px 0 hsla(0,0%,100%,.1);
  }
.gaming-attribute-card.skin:hover{
    border-color:rgba(139,92,246,.6);
    box-shadow:0 20px 40px rgba(0,0,0,.3),0 0 30px rgba(139,92,246,.2),inset 0 1px 0 hsla(0,0%,100%,.1);
  }
.gaming-attribute-background{
    background-position:50%;
    background-size:cover;
    inset:0;
    opacity:0.15;
    position:absolute;
    transition:all 0.3s ease;
    z-index:0;
  }
.gaming-attribute-card:hover .gaming-attribute-background{
    opacity:0.25;
    transform:scale(1.1);
  }
.gaming-attribute-image{
    filter:brightness(1.1) contrast(1.1) saturate(1.05);
    height:100%;
    -o-object-fit:cover;
       object-fit:cover;
    position:relative;
    transition:all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    transition:transform 0.3s ease;
    width:100%;
    z-index:2;
  }
.gaming-attribute-card:hover .gaming-attribute-image{
    filter:brightness(1.2) contrast(1.2) saturate(1.15);
    transform:scale(1.1);
  }
.gaming-attribute-overlay{
    background:linear-gradient(0deg,
      rgba(0,0,0,.4) 0%,
      transparent 50%,
      transparent);
    inset:0;
    opacity:0;
    position:absolute;
    transition:all 0.3s ease;
    z-index:3;
  }
.gaming-attribute-card:hover .gaming-attribute-overlay{
    opacity:1;
  }
.gaming-attribute-overlay.character{
    background:linear-gradient(0deg,
      rgba(16,185,129,.3) 0%,
      transparent 60%);
  }
.gaming-attribute-overlay.weapon{
    background:linear-gradient(0deg,
      rgba(245,158,11,.3) 0%,
      transparent 60%);
  }
.gaming-attribute-overlay.skin{
    background:linear-gradient(0deg,
      rgba(139,92,246,.3) 0%,
      transparent 60%);
  }
.gaming-attribute-glow{
    border-radius:0.875rem;
    box-shadow:inset 0 0 20px rgba(80,129,255,0);
    inset:0;
    position:absolute;
    transition:all 0.3s ease;
    z-index:4;
  }
.gaming-attribute-card:hover .gaming-attribute-glow{
    box-shadow:inset 0 0 20px rgba(80,129,255,.2);
  }
.gaming-attribute-card.character:hover .gaming-attribute-glow{
    box-shadow:inset 0 0 20px rgba(16,185,129,.2);
  }
.gaming-attribute-card.weapon:hover .gaming-attribute-glow{
    box-shadow:inset 0 0 20px rgba(245,158,11,.2);
  }
.gaming-attribute-card.skin:hover .gaming-attribute-glow{
    box-shadow:inset 0 0 20px rgba(139,92,246,.2);
  }
.gaming-thumbnail:focus{
    border-color:rgba(80,129,255,.8);
    outline:2px solid rgba(80,129,255,.8);
    outline-offset:2px;
  }
.gaming-thumbnail img[loading=lazy]{
    opacity:0;
  }
.gaming-thumbnail img.loaded,.gaming-thumbnail img:not([loading=lazy]){
    opacity:1;
  }
@keyframes thumbnailPulse{
    0%{ box-shadow:0 0 0 0 rgba(80,129,255,.4); }
    70%{ box-shadow:0 0 0 10px rgba(80,129,255,0); }
    to{ box-shadow:0 0 0 0 rgba(80,129,255,0); }
  }
.gaming-empty-state{
    background:linear-gradient(135deg,
      rgba(31,41,55,.6),
      rgba(17,24,39,.8));
    border:2px dashed hsla(220,9%,46%,.3);
    border-radius:1rem;
    overflow:hidden;
    padding:3rem 2rem;
    position:relative;
    text-align:center;
    transition:all 0.3s ease;
  }
.gaming-empty-state:before{
    background:radial-gradient(circle at 50% 50%, rgba(80,129,255,.05) 0%, transparent 70%);
    content:"";
    inset:0;
    opacity:0;
    position:absolute;
    transition:opacity 0.3s ease;
  }
.gaming-empty-state:hover{
    background:linear-gradient(135deg,
      rgba(31,41,55,.8),
      rgba(17,24,39,.9));
    border-color:rgba(80,129,255,.4);
  }
.gaming-empty-state:hover:before{
    opacity:1;
  }
.gaming-empty-state-icon{
    align-items:center;
    background:linear-gradient(135deg,
      hsla(220,9%,46%,.2),
      rgba(75,85,99,.3));
    border:1px solid hsla(220,9%,46%,.3);
    border-radius:1rem;
    display:flex;
    height:4rem;
    justify-content:center;
    margin:0 auto 1.5rem auto;
    transition:all 0.3s ease;
    width:4rem;
  }
.gaming-empty-state:hover .gaming-empty-state-icon{
    background:linear-gradient(135deg,
      rgba(80,129,255,.2),
      rgba(52,99,219,.3));
    border-color:rgba(80,129,255,.4);
    transform:scale(1.05);
  }
.gaming-empty-state-text{
    color:rgba(156,163,175,.8);
    font-size:0.95rem;
    font-weight:500;
    position:relative;
    z-index:1;
  }
.gaming-empty-state:hover .gaming-empty-state-text{
    color:#9ca3af;
  }
@keyframes glowPulse{
  0%,to{
    opacity:0.3;
    transform:scale(1);
  }
  50%{
    opacity:0.8;
    transform:scale(1.05);
  }
}
#modalGlow{
  animation:glowPulse 3s ease-in-out infinite;
}
@keyframes fadeInImage{
  0%{ opacity:0; }
  to{ opacity:1; }
}
.debug-mode .swiper-slide{
  border:2px solid red;
}
.debug-mode .swiper-slide img{
  border:2px solid green;
}
.gaming-btn-primary:active{
    transform:translateY(0);
  }
@keyframes gradientShift{
    0%,to{ background-position:0% 50%; }
    50%{ background-position:100% 50%; }
  }
.mySwiper{
    border-radius:0.5rem;
    height:100%;
    width:100%;
  }
.swiper-slide{
    align-items:center;
    background:transparent;
    display:flex;
    justify-content:center;
  }
.swiper-slide img{
    border-radius:0.75rem;
    height:100%;
    -o-object-fit:cover;
       object-fit:cover;
    transition:transform 0.3s ease,filter 0.3s ease;
    width:100%;
  }
.swiper-slide:hover img{
    filter:brightness(1.1);
    transform:scale(1.02);
  }
.gaming-account-card,.gaming-btn-primary,.gaming-main-gallery{
    will-change:transform;
  }
.gaming-attribute-image,.swiper-slide img{
    image-rendering:-webkit-optimize-contrast;
    image-rendering:crisp-edges;
  }
img[loading=lazy]{
    opacity:0;
    transition:opacity 0.3s ease;
  }
img[loading=eager],img[loading=lazy].loaded{
    opacity:1;
  }
.swiper-container{
    contain:layout style paint;
  }
.gaming-attribute-card,.gaming-btn-primary,.gaming-title{
    transform:translateZ(0);
  }
@keyframes zoom{
            0%{
                opacity:0;
                transform:scale(.5);
            }
            50%{
                opacity:1;
            }
            to{
                opacity:0;
                transform:scale(1);
            }
        }
@keyframes lucidgentelegram{
            0%,to{
                transform:rotate(-25deg);
            }
            50%{
                transform:rotate(25deg);
            }
        }
.th-social a{background-color:#0f1c23;border-radius:0;color:#1778f2;display:inline-block;font-size:20px;height:46px;line-height:46px;margin-right:5px;position:relative;text-align:center;width:46px}
.hero-carousel,.hero-carousel .swiper-slide,.hero-carousel .swiper-wrapper{
    height:100%;
    width:100%;
}
.hero-carousel .swiper-slide{
    align-items:stretch;
    display:flex;
}
.hero-carousel .swiper-slide a{
    display:block;
    height:100%;
    position:relative;
    width:100%;
}
.hero-carousel .swiper-slide img{
    bottom:0 !important;
    height:100% !important;
    left:0 !important;
    -o-object-fit:cover !important;
       object-fit:cover !important;
    position:absolute !important;
    right:0 !important;
    top:0 !important;
    width:100% !important;
}
.hero-carousel-pagination .pagination-dot{
    background:hsla(0,0%,100%,.48);
    border-radius:10px;
    cursor:pointer;
    height:4px;
    overflow:hidden;
    position:relative;
    transition:all 0.3s ease;
    width:18px;
}
.hero-carousel-pagination .pagination-dot.active{
    width:36px;
}
.hero-carousel-pagination .pagination-dot:before{
    background:#fff;
    content:"";
    height:100%;
    left:0;
    position:absolute;
    top:0;
    transition:width 4.5s linear;
    width:0;
}
.hero-carousel-pagination .pagination-dot.active:before{
    width:100%;
}
.hero-carousel-pagination .pagination-dot:not(.active):before{
    transition:none;
    width:0;
}
.header-element{
    align-items:stretch;
    display:flex;
}
.mobile-nav-container{
    bottom:0;
    height:64px;
    left:0;
    position:fixed;
    width:100%;
    z-index:999998;
}
.nav-section-left{
    border-top:1px solid #5081ff33;
    border-top-right-radius:20px;
    float:left;
}
.nav-section-left,.nav-section-right{
    background:#13112e;
    box-shadow:0 -4px 8px 0 rgba(80,129,255,.15);
    display:flex;
    height:64px;
    width:calc(50% - 40px);
}
.nav-section-right{
    border-top:1px solid #5081ff33;
    border-top-left-radius:20px;
    float:right;
}
.nav-item{
    align-items:center;
    color:#9f9bab;
    cursor:pointer;
    display:flex;
    flex-direction:column;
    justify-content:center;
    padding-top:8px;
    text-align:center;
    text-decoration:none;
    transition:all 0.3s ease;
    width:50%;
}
.nav-item:hover{
    color:#ffffffcc;
    transform:translateY(-1px);
}
.nav-icon{
    display:block;
    font-size:26px;
    height:26px;
    margin:0 auto 6px;
    transition:transform 0.3s ease;
    width:26px;
}
.nav-item:hover .nav-icon{
    transform:scale(1.1);
}
.nav-label{
    display:block;
    font-size:12px;
    font-weight:500;
    height:16px;
    letter-spacing:0.3px;
    line-height:16px;
    margin-top:2px;
}
.floating-home-button{
    background:transparent;
    border-radius:50%;
    box-sizing:border-box;
    height:80px;
    left:50%;
    padding:5px;
    position:absolute;
    text-decoration:none;
    top:-26px;
    transform:translateX(-50%);
    transition:transform 0.3s ease;
    width:80px;
    z-index:999999;
}
.floating-home-button:hover{
    transform:translateX(-50%) scale(1.05);
}
.floating-home-button:before{
    background:#13112e;
    box-shadow:0 33px 0 10px #13112e;
    content:"";
    left:0;
    position:absolute;
    top:0;
    z-index:-1;
}
.floating-home-button:before,.home-button-inner{
    border-radius:50%;
    height:100%;
    width:100%;
}
.home-button-inner{
    align-items:center;
    background:linear-gradient(135deg, #4b7dff, #5081ff);
    box-shadow:0 8px 16px rgba(75,125,255,.3),inset 0 -4px 8px rgba(0,0,0,.1);
    box-sizing:border-box;
    color:#fff;
    cursor:pointer;
    display:flex;
    font-size:24px;
    justify-content:center;
    text-align:center;
    transition:all 0.3s ease;
}
.floating-home-button:hover .home-button-inner{
    background:linear-gradient(135deg, #5081ff, #4b7dff);
    box-shadow:0 12px 24px rgba(75,125,255,.4),inset 0 -4px 8px rgba(0,0,0,.15);
}
.floating-home-button:after{
    border-radius:50%;
    box-shadow:inset 0 -10px 5px 0 rgba(5,26,40,.08);
    content:"";
    display:block;
    height:100%;
    left:0;
    pointer-events:none;
    position:absolute;
    top:0;
    width:100%;
    z-index:1;
}
.mobile-nav-container:after{
    clear:both;
    content:"";
    display:table;
}
.clean-account-card{
    background:linear-gradient(145deg, #272450, #1e293b 50%, #272450);
    border:2px solid transparent;
    border-radius:0.75rem;
    box-shadow:0 4px 6px -1px rgba(0,0,0,.1),0 2px 4px -1px rgba(0,0,0,.06);
    display:flex;
    flex-direction:column;
    min-height:360px;
    overflow:hidden;
    padding:1rem;
    position:relative;
    transform-origin:center;
    transition:all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    will-change:transform, box-shadow, border-color;
  }
.clean-account-card:hover{
    animation:gaming-card-glow 2s infinite;
    border-color:#5081ff !important;
    box-shadow:0 20px 40px rgba(80,129,255,.4),0 0 30px rgba(80,129,255,.3),0 0 60px rgba(75,125,255,.2);
    transform:scale(1.05) translateY(-8px);
  }
.clean-account-card--selected{
    border-color:#5081ff;
    box-shadow:0 0 0 1px rgba(80,129,255,.3);
  }
.clean-card-header{
    display:flex;
    justify-content:flex-end;
    margin-bottom:0.75rem;
  }
.status-badge{
    align-items:center;
    background:rgba(16,185,129,.1);
    border:1px solid rgba(16,185,129,.3);
    border-radius:0.375rem;
    color:#10b981;
    display:flex;
    font-size:0.6875rem;
    font-weight:500;
    gap:0.375rem;
    padding:0.125rem 0.5rem;
  }
.status-indicator{
    animation:pulse 2s infinite;
    background:#10b981;
    border-radius:50%;
    height:4px;
    width:4px;
  }
.clean-image-section{
    margin-bottom:1rem;
    position:relative;
  }
.image-container{
    background:rgba(30,41,59,.5);
    border-radius:0.5rem;
    height:0;
    overflow:hidden;
    padding-bottom:58.82%;
    position:relative;
    width:100%;
  }
.account-image{
    height:100%;
    left:0;
    -o-object-fit:cover;
       object-fit:cover;
    -o-object-position:center;
       object-position:center;
    position:absolute;
    top:0;
    transition:transform 0.3s ease;
    width:100%;
  }
.clean-account-card:hover .account-image{
    transform:scale(1.03);
  }
.image-overlay{
    align-items:center;
    background:rgba(0,0,0,.4);
    color:#fff;
    display:flex;
    font-size:1.25rem;
    justify-content:center;
    transition:opacity 0.3s ease;
  }
.image-overlay,.image-skeleton{
    inset:0;
    position:absolute;
  }
.image-skeleton{
    animation:shimmer 1.5s infinite;
    background:linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%);
    background-size:200% 100%;
  }
.account-code-badge{
    background:linear-gradient(135deg, #5081ff, #3463db);
    border-radius:0.375rem;
    bottom:-6px;
    box-shadow:0 2px 4px -1px rgba(0,0,0,.1);
    color:#fff;
    font-size:0.6875rem;
    font-weight:600;
    left:50%;
    padding:0.125rem 0.75rem;
    position:absolute;
    transform:translateX(-50%);
    z-index:10;
  }
.clean-account-title{
    align-items:center;
    display:flex;
    justify-content:center;
    margin-bottom:0.75rem;
    min-height:2.5rem;
    padding:0 0.5rem;
    text-align:center;
  }
.clean-account-title h3{
    color:#fff;
    font-family:Signika,sans-serif;
    font-size:1rem;
    font-weight:600;
    line-height:1.3;
    margin:0;
    text-align:center;
  }
.account-title-truncated{
    -webkit-backdrop-filter:blur(4px);
            backdrop-filter:blur(4px);
    background:hsla(0,0%,100%,.02);
    border-radius:0.375rem;
    cursor:help;
    display:block;
    max-width:100%;
    overflow:hidden;
    padding:0.25rem 0;
    text-overflow:ellipsis;
    transition:all 0.3s ease;
    white-space:nowrap;
    width:100%;
  }
.account-title-truncated:hover{
    background:rgba(80,129,255,.1);
    box-shadow:0 0 10px rgba(80,129,255,.2);
    color:#5081ff;
    transform:scale(1.02);
  }
.clean-stats-grid{
    margin-bottom:1rem;
  }
.stat-item{
    align-items:center;
    border-bottom:1px solid hsla(0,0%,100%,.1);
    display:flex;
    justify-content:space-between;
    padding:0.5rem 0;
  }
.stat-item:last-child{
    border-bottom:none;
  }
.stat-label{
    color:#9ca3af;
    font-weight:400;
  }
.stat-label,.stat-value{
    font-size:0.875rem;
    line-height:1.5;
  }
.stat-value{
    color:#fff;
    font-weight:500;
    text-align:right;
  }
.clean-feature-section{
    margin-bottom:0.75rem;
  }
.feature-header{
    margin-bottom:0.5rem;
  }
.feature-header h4{
    color:#5081ff;
    font-size:0.8125rem;
    font-weight:600;
    line-height:1.2;
    margin:0 0 0.375rem 0;
    text-align:center;
  }
.feature-dropdown{
    position:relative;
  }
.feature-toggle-btn{
    align-items:center;
    background:rgba(80,129,255,.1);
    border:1px solid rgba(80,129,255,.3);
    border-radius:0.375rem;
    color:#fff;
    cursor:pointer;
    display:flex;
    font-size:0.6875rem;
    gap:0.375rem;
    justify-content:center;
    padding:0.375rem 0.5rem;
    transition:all 0.3s ease;
    width:100%;
  }
.feature-toggle-btn:hover{
    background:rgba(80,129,255,.2);
    border-color:rgba(80,129,255,.5);
  }
.feature-dropdown-content{
    background:#1e293b;
    border:1px solid rgba(80,129,255,.3);
    border-radius:0.5rem;
    box-shadow:0 10px 15px -3px rgba(0,0,0,.1);
    left:0;
    margin-top:0.25rem;
    position:absolute;
    right:0;
    top:100%;
    z-index:50;
  }
.dropdown-header{
    align-items:center;
    border-bottom:1px solid hsla(0,0%,100%,.1);
    display:flex;
    justify-content:space-between;
    padding:0.75rem;
  }
.count-label{
    color:#9ca3af;
    font-size:0.75rem;
  }
.close-btn{
    background:none;
    border:none;
    border-radius:0.25rem;
    color:#9ca3af;
    cursor:pointer;
    padding:0.25rem;
    transition:color 0.3s ease;
  }
.close-btn:hover{
    color:#fff;
  }
.feature-grid{
    display:flex;
    flex-wrap:wrap;
    gap:0.75rem;
    justify-content:center;
    max-height:160px;
    overflow-y:auto;
    padding:1rem;
  }
.feature-item{
    border:1px solid hsla(0,0%,100%,.2);
    border-radius:0.5rem;
    cursor:pointer;
    height:40px;
    overflow:hidden;
    transition:transform 0.3s ease;
    width:40px;
  }
.feature-item:hover{
    transform:scale(1.1);
  }
.feature-image{
    height:100%;
    -o-object-fit:cover;
       object-fit:cover;
    width:100%;
  }
.clean-marquee-container{
    margin-top:0.75rem;
  }
.marquee-wrapper{
    background:hsla(0,0%,100%,.05);
    border-radius:0.5rem;
    overflow:hidden;
    padding:0.5rem;
    position:relative;
    width:100%;
  }
.marquee-content{
    align-items:center;
    display:flex;
    gap:0.5rem;
  }
.marquee-item{
    border:1px solid hsla(0,0%,100%,.2);
    border-radius:0.375rem;
    flex-shrink:0;
    height:32px;
    overflow:hidden;
    width:32px;
  }
.marquee-image{
    height:100%;
    -o-object-fit:cover;
       object-fit:cover;
    width:100%;
  }
.clean-purchase-section{
    margin-top:auto;
    padding-top:0.75rem;
  }
.clean-purchase-btn{
    align-items:center;
    background:linear-gradient(135deg, #5081ff, #3463db);
    border:none;
    border-radius:0.5rem;
    box-shadow:0 2px 4px -1px rgba(80,129,255,.3);
    color:#fff;
    display:flex;
    font-weight:600;
    gap:0.5rem;
    justify-content:center;
    padding:0.625rem 0.75rem;
    text-decoration:none;
    transition:all 0.3s ease;
    width:100%;
  }
.clean-purchase-btn:hover{
    background:linear-gradient(135deg, #3463db, #1e40af);
    box-shadow:0 4px 6px -1px rgba(80,129,255,.4);
    transform:translateY(-1px);
  }
.purchase-icon{
    font-size:0.875rem;
  }
.purchase-price{
    gap:0.125rem;
  }
.price-with-discount,.purchase-price{
    align-items:center;
    display:flex;
    flex-direction:column;
  }
.price-with-discount{
    gap:0.0625rem;
  }
.original-price{
    color:hsla(0,0%,100%,.6);
    font-size:0.6875rem;
    line-height:1;
    text-decoration:line-through;
  }
.discounted-price,.regular-price{
    color:#fff;
    font-size:0.8125rem;
    font-weight:700;
    line-height:1.2;
  }
.marquee-track{
    animation:marquee linear infinite;
    display:flex;
    white-space:nowrap;
  }
.marquee-track:hover{
    animation-play-state:paused;
  }
.gaming-pagination-container{
    align-items:center;
    display:flex;
    flex-direction:column;
    gap:1rem;
    gap:0.75rem;
  }
.gaming-pagination-list{
    align-items:center;
    display:flex;
    gap:0.5rem;
    gap:0.375rem;
    list-style:none;
    margin:0;
    min-height:2.5rem;
    min-height:2rem;
    padding:0;
  }
.gaming-pagination-btn{
    align-items:center;
    border:none;
    border-radius:0.5rem;
    border-radius:0.375rem;
    box-sizing:border-box;
    cursor:pointer;
    display:inline-flex;
    font-family:Montserrat,sans-serif;
    font-size:0.875rem;
    font-size:0.75rem;
    font-weight:600;
    gap:0.5rem;
    gap:0.25rem;
    height:2.5rem;
    height:1.75rem;
    justify-content:center;
    min-width:2.5rem;
    min-width:1.75rem;
    overflow:hidden;
    padding:0.5rem 0.75rem;
    padding:0.25rem 0.375rem;
    position:relative;
    transition:all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    -webkit-user-select:none;
    -moz-user-select:none;
    user-select:none;
    vertical-align:middle;
    -webkit-tap-highlight-color:transparent;
  }
.gaming-pagination-btn:before{
    background:linear-gradient(90deg, transparent, hsla(0,0%,100%,.2), transparent);
    content:"";
    height:100%;
    left:-100%;
    position:absolute;
    top:0;
    transition:left 0.5s ease;
    width:100%;
  }
.gaming-pagination-btn:hover:before{
    left:100%;
  }
.gaming-pagination-btn--number{
    background:linear-gradient(145deg, rgba(39,36,80,.8), rgba(30,41,59,.6));
    border:1px solid rgba(80,129,255,.3);
    box-shadow:0 2px 8px rgba(0,0,0,.2);
    box-shadow:0 1px 4px rgba(0,0,0,.2);
    color:#ffffff99;
  }
.gaming-pagination-btn--number:hover{
    background:linear-gradient(145deg, rgba(80,129,255,.2), rgba(75,125,255,.3));
    border-color:rgba(80,129,255,.6);
    box-shadow:0 8px 16px rgba(80,129,255,.3),0 0 20px rgba(80,129,255,.2);
    box-shadow:0 4px 8px rgba(80,129,255,.3),0 0 12px rgba(80,129,255,.2);
    color:#fff;
  }
.gaming-pagination-btn--active{
    animation:gaming-pagination-pulse 2s infinite;
    background:linear-gradient(135deg, #4b7dff, #5081ff 50%, #3463db);
    border:1px solid rgba(80,129,255,.8);
    box-shadow:0 4px 12px rgba(80,129,255,.4),0 0 20px rgba(80,129,255,.3),inset 0 1px 0 hsla(0,0%,100%,.2);
    box-shadow:0 2px 8px rgba(80,129,255,.4),0 0 12px rgba(80,129,255,.3),inset 0 1px 0 hsla(0,0%,100%,.2);
    color:#fff;
  }
.gaming-pagination-btn--active:hover{
    box-shadow:0 6px 16px rgba(80,129,255,.5),0 0 30px rgba(80,129,255,.4),inset 0 1px 0 hsla(0,0%,100%,.3);
    box-shadow:0 3px 10px rgba(80,129,255,.5),0 0 16px rgba(80,129,255,.4),inset 0 1px 0 hsla(0,0%,100%,.3);
    transform:translateY(-1px) scale(1.02);
  }
.gaming-pagination-btn--nav{
    background:linear-gradient(145deg, rgba(75,125,255,.1), rgba(80,129,255,.2));
    border:1px solid rgba(80,129,255,.4);
    color:#5081ff;
    min-width:3rem;
    min-width:2rem;
  }
.gaming-pagination-btn--nav:hover{
    background:linear-gradient(145deg, rgba(75,125,255,.3), rgba(80,129,255,.4));
    border-color:rgba(80,129,255,.8);
    box-shadow:0 8px 16px rgba(80,129,255,.3),0 0 20px rgba(80,129,255,.2);
   
    box-shadow:0 4px 8px rgba(80,129,255,.3),0 0 12px rgba(80,129,255,.2);
    color:#fff;
  }
.gaming-pagination-btn--disabled{
    border:1px solid rgba(80,129,255,.1);
    color:#ffffff33;
    cursor:not-allowed;
    opacity:0.5;
  }
.gaming-pagination-btn--disabled,.gaming-pagination-btn--disabled:hover{
    background:linear-gradient(145deg, rgba(39,36,80,.3), rgba(30,41,59,.2));
  }
.gaming-pagination-btn--disabled:hover{
    box-shadow:none;
    transform:none;
  }
.gaming-pagination-btn--disabled:before{
    display:none;
  }
.gaming-pagination-ellipsis{
    align-items:center;
    color:#ffffff66;
    display:flex;
    font-size:1rem;
    font-size:0.875rem;
    height:2.5rem;
    height:1.75rem;
    justify-content:center;
    min-width:2.5rem;
    min-width:1.75rem;
  }
.gaming-pagination-icon{
    flex-shrink:0;
    height:14px;
    transition:transform 0.3s ease;
    width:14px;
  }
.gaming-pagination-btn:hover .gaming-pagination-icon{
    transform:scale(1.1);
  }
.gaming-pagination-text{
    font-size:0.875rem;
    font-size:0.75rem;
    font-weight:600;
  }
.gaming-pagination-info{
    margin-top:0.5rem;
    text-align:center;
  }
.gaming-pagination-info-text{
    color:#ffffff99;
    font-family:Montserrat,sans-serif;
    font-size:0.875rem;
    font-size:0.8125rem;
  }
.gaming-pagination-info-current,.gaming-pagination-info-total{
    color:#5081ff;
    font-weight:700;
    text-shadow:0 0 8px rgba(80,129,255,.5);
    text-shadow:0 0 6px rgba(80,129,255,.5);
  }
@keyframes gaming-pagination-glow{
    0%,to{
      box-shadow:0 0 20px rgba(80,129,255,.2);
    }
    50%{
      box-shadow:0 0 30px rgba(80,129,255,.4);
    }
  }
.gaming-pagination-mobile-scroll{
    overflow-x:auto;
    overflow-y:hidden;
    -webkit-overflow-scrolling:touch;
    scroll-behavior:smooth;
  }
.gaming-pagination-mobile-scroll::-webkit-scrollbar{
    height:2px;
  }
.gaming-pagination-mobile-scroll::-webkit-scrollbar-track{
    background:rgba(80,129,255,.1);
    border-radius:1px;
  }
.gaming-pagination-mobile-scroll::-webkit-scrollbar-thumb{
    background:rgba(80,129,255,.3);
    border-radius:1px;
  }
.gaming-pagination-mobile-scroll::-webkit-scrollbar-thumb:hover{
    background:rgba(80,129,255,.5);
  }
.gaming-pagination-btn:focus{
    box-shadow:0 0 0 2px rgba(80,129,255,.5),0 4px 12px rgba(80,129,255,.3);
    box-shadow:0 0 0 2px rgba(80,129,255,.5),0 2px 6px rgba(80,129,255,.3);
    outline:none;
  }
.gaming-pagination-btn:focus-visible{
    outline:2px solid #5081ff;
    outline-offset:2px;
  }
.select2-container .select2-selection--multiple,.select2-container .select2-selection--single,.select2-container--default .select2-selection--multiple,.select2-container--default .select2-selection--single,.select2-container--gaming .select2-selection--multiple,.select2-container--gaming .select2-selection--single{
    background-color:rgba(30,41,59,.5) !important;
    background:rgba(30,41,59,.5) !important;
    border:1px solid rgba(80,129,255,.3) !important;
    border-radius:0.5rem !important;
    box-sizing:border-box !important;
    color:#fff !important;
    font-family:Signika,ui-sans-serif,system-ui,sans-serif !important;
    height:48px !important;
    min-height:48px !important;
    padding:0.75rem 1rem !important;
    transition:all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  }
.select2-container .select2-selection{
    background-color:rgba(30,41,59,.5) !important;
    background:rgba(30,41,59,.5) !important;
  }
.select2-container .select2-selection--multiple,.select2-container--default .select2-selection--multiple,.select2-container--gaming .select2-selection--multiple{
    align-items:flex-start !important;
    background-color:rgba(30,41,59,.5) !important;
    background:rgba(30,41,59,.5) !important;
    display:flex !important;
    flex-wrap:wrap !important;
    gap:0.25rem !important;
    max-height:120px !important;
    min-height:48px !important;
    overflow-y:auto !important;
    padding:0.375rem 0.75rem !important;
  }
.select2-container .select2-selection--multiple::-webkit-scrollbar{
    width:4px !important;
  }
.select2-container .select2-selection--multiple::-webkit-scrollbar-track{
    background:rgba(30,41,59,.3) !important;
    border-radius:2px !important;
  }
.select2-container .select2-selection--multiple::-webkit-scrollbar-thumb{
    background:#5081ff !important;
    border-radius:2px !important;
  }
.select2-container .select2-selection__rendered,.select2-container--default .select2-selection__rendered,.select2-container--gaming .select2-selection__rendered{
    align-items:center !important;
    color:#fff !important;
    display:flex !important;
    height:100% !important;
    line-height:1.5 !important;
    margin:0 !important;
    padding:0 !important;
  }
.select2-container .select2-selection__rendered,.select2-container .select2-selection__rendered *{
    background-color:transparent !important;
    background:transparent !important;
  }
.select2-container .select2-selection__placeholder,.select2-container--default .select2-selection__placeholder,.select2-container--gaming .select2-selection__placeholder{
    color:#9ca3af !important;
  }
.select2-container .select2-selection__arrow,.select2-container--default .select2-selection__arrow,.select2-container--gaming .select2-selection__arrow{
    height:46px !important;
    right:1rem !important;
    top:1px !important;
  }
.select2-container .select2-selection__arrow b,.select2-container--default .select2-selection__arrow b,.select2-container--gaming .select2-selection__arrow b{
    border-color:#9ca3af transparent transparent transparent !important;
    border-style:solid !important;
    border-width:5px 4px 0 4px !important;
    height:0 !important;
    left:50% !important;
    margin-left:-4px !important;
    margin-top:-2px !important;
    position:absolute !important;
    top:50% !important;
    width:0 !important;
  }
.select2-container--default.select2-container--focus .select2-selection,.select2-container--gaming.select2-container--focus .select2-selection,.select2-container.select2-container--focus .select2-selection,.select2-container.select2-container--focus .select2-selection--multiple,.select2-container.select2-container--focus .select2-selection--single{
    background-color:rgba(30,41,59,.5) !important;
    background:rgba(30,41,59,.5) !important;
    border-color:#5081ff !important;
    box-shadow:0 0 0 2px rgba(80,129,255,.2) !important;
    outline:none !important;
  }
.select2-container .select2-selection--multiple:hover,.select2-container .select2-selection--single:hover,.select2-container .select2-selection:hover{
    background-color:rgba(30,41,59,.5) !important;
    background:rgba(30,41,59,.5) !important;
    border-color:#5081ff !important;
  }
.select2-container .select2-selection__choice,.select2-container--default .select2-selection__choice,.select2-container--gaming .select2-selection__choice{
    align-items:center !important;
    background:linear-gradient(135deg, #5081ff, #3463db) !important;
    border:1px solid #3463db !important;
    border-radius:0.5rem !important;
    box-shadow:0 2px 4px rgba(0,0,0,.1) !important;
    color:#fff !important;
    display:inline-flex !important;
    font-size:0.875rem !important;
    font-weight:500 !important;
    gap:0.375rem !important;
    margin:0.125rem !important;
    max-width:200px !important;
    overflow:hidden !important;
    padding:0.375rem 0.75rem !important;
    text-overflow:ellipsis !important;
    transition:all 0.2s ease-in-out !important;
    white-space:nowrap !important;
  }
.select2-container .select2-selection__choice:hover,.select2-container--default .select2-selection__choice:hover,.select2-container--gaming .select2-selection__choice:hover{
    background:linear-gradient(135deg, #4b7dff, #2a52be) !important;
    box-shadow:0 4px 8px rgba(80,129,255,.3) !important;
    transform:translateY(-1px) !important;
  }
.select2-container .select2-selection__choice__remove,.select2-container--default .select2-selection__choice__remove,.select2-container--gaming .select2-selection__choice__remove{
    align-items:center !important;
    background:hsla(0,0%,100%,.1) !important;
    border-radius:50% !important;
    color:#fff !important;
    display:flex !important;
    font-size:1rem !important;
    font-weight:700 !important;
    height:18px !important;
    justify-content:center !important;
    margin-left:0.25rem !important;
    margin-right:0 !important;
    padding:0.125rem !important;
    transition:all 0.2s ease-in-out !important;
    width:18px !important;
  }
.select2-container .select2-selection__choice__remove:hover,.select2-container--default .select2-selection__choice__remove:hover,.select2-container--gaming .select2-selection__choice__remove:hover{
    background:#ef4444 !important;
    color:#fff !important;
    transform:scale(1.1) !important;
  }
.gaming-select2-dropdown{
    animation:dropdown-appear 0.2s ease-out !important;
    -webkit-backdrop-filter:blur(10px) !important;
            backdrop-filter:blur(10px) !important;
    background:linear-gradient(145deg, #1e293b, #0f172a) !important;
    border:2px solid rgba(80,129,255,.4) !important;
    border-radius:0.75rem !important;
    box-shadow:0 20px 25px -5px rgba(0,0,0,.1),0 10px 10px -5px rgba(0,0,0,.04),0 0 30px rgba(80,129,255,.4),inset 0 1px 0 hsla(0,0%,100%,.1) !important;
    margin-top:0.5rem !important;
    z-index:99999 !important;
  }
@keyframes dropdown-appear{
    0%{
      opacity:0;
      transform:translateY(-10px) scale(0.95);
    }
    to{
      opacity:1;
      transform:translateY(0) scale(1);
    }
  }
.gaming-select2-dropdown .select2-results__options{
    max-height:250px !important;
    overflow-y:auto !important;
    padding:0.25rem !important;
    scrollbar-color:#5081ff #1e293b !important;
    scrollbar-width:thin !important;
  }
.gaming-select2-dropdown .select2-results__options::-webkit-scrollbar{
    width:6px !important;
  }
.gaming-select2-dropdown .select2-results__options::-webkit-scrollbar-track{
    background:#1e293b !important;
  }
.gaming-select2-dropdown .select2-results__options::-webkit-scrollbar-thumb{
    background:#5081ff !important;
    border-radius:3px !important;
  }
.gaming-select2-dropdown .select2-results__option{
    border-radius:0.5rem !important;
    color:#fff !important;
    cursor:pointer !important;
    font-family:Signika,ui-sans-serif,system-ui,sans-serif !important;
    font-size:0.9rem !important;
    font-weight:500 !important;
    margin:0.125rem 0 !important;
    overflow:hidden !important;
    padding:0.875rem 1.25rem !important;
    position:relative !important;
    transition:all 0.2s cubic-bezier(0.4, 0, 0.2, 1) !important;
  }
.gaming-select2-dropdown .select2-results__option:before{
    background:transparent !important;
    content:"" !important;
    height:100% !important;
    left:0 !important;
    position:absolute !important;
    top:0 !important;
    transition:all 0.2s ease-in-out !important;
    width:3px !important;
  }
.gaming-select2-dropdown .select2-results__option--highlighted{
    background:linear-gradient(135deg, rgba(80,129,255,.3), rgba(52,99,219,.2)) !important;
    color:#fff !important;
    transform:translateX(4px) !important;
  }
.gaming-select2-dropdown .select2-results__option--highlighted:before{
    background:#5081ff !important;
  }
.gaming-select2-dropdown .select2-results__option[aria-selected=true]{
    background:linear-gradient(135deg, #5081ff, #3463db) !important;
    box-shadow:0 2px 4px rgba(80,129,255,.3) !important;
    color:#fff !important;
    font-weight:600 !important;
  }
.gaming-select2-dropdown .select2-results__option[aria-selected=true]:before{
    background:#fff !important;
  }
.gaming-select2-dropdown .select2-results__option[aria-selected=true]:after{
    color:#fff !important;
    content:"✓" !important;
    font-size:1rem !important;
    font-weight:700 !important;
    position:absolute !important;
    right:1rem !important;
    top:50% !important;
    transform:translateY(-50%) !important;
  }
.gaming-select2-dropdown .select2-search{
    border-bottom:1px solid rgba(80,129,255,.2) !important;
    margin-bottom:0.5rem !important;
    padding:0.75rem !important;
  }
.gaming-select2-dropdown .select2-search__field{
    background:linear-gradient(145deg, rgba(30,41,59,.9), rgba(15,23,42,.9)) !important;
    border:2px solid rgba(80,129,255,.3) !important;
    border-radius:0.5rem !important;
    box-shadow:inset 0 2px 4px rgba(0,0,0,.1) !important;
    color:#fff !important;
    font-family:Signika,ui-sans-serif,system-ui,sans-serif !important;
    font-size:0.9rem !important;
    margin:0 !important;
    padding:0.75rem 1rem !important;
    transition:all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    width:100% !important;
  }
.gaming-select2-dropdown .select2-search__field::-moz-placeholder{
    color:#9ca3af !important;
    font-style:italic !important;
  }
.gaming-select2-dropdown .select2-search__field::placeholder{
    color:#9ca3af !important;
    font-style:italic !important;
  }
.gaming-select2-dropdown .select2-search__field:focus{
    background:linear-gradient(145deg, #1e293b, #0f172a) !important;
    border-color:#5081ff !important;
    box-shadow:0 0 0 3px rgba(80,129,255,.2),inset 0 2px 4px rgba(0,0,0,.1),0 4px 6px rgba(80,129,255,.1) !important;
    outline:none !important;
    transform:translateY(-1px) !important;
  }
.select2-container--gaming .select2-selection__clear{
    color:#9ca3af !important;
    font-size:1.2em !important;
    font-weight:700 !important;
    margin-right:0.5rem !important;
  }
.select2-container--gaming .select2-selection__clear:hover{
    color:#ef4444 !important;
  }
.select2-container--gaming.select2-container--loading .select2-selection:after{
    animation:spin 1s linear infinite !important;
    border:2px solid #5081ff !important;
    border-radius:50% !important;
    border-top:2px solid transparent !important;
    content:"" !important;
    height:16px !important;
    position:absolute !important;
    right:2.5rem !important;
    top:50% !important;
    transform:translateY(-50%) !important;
    width:16px !important;
  }
@keyframes spin{
    0%{ transform:translateY(-50%) rotate(0deg); }
    to{ transform:translateY(-50%) rotate(360deg); }
  }
.select2-container--gaming.select2-container--focus .select2-selection,.select2-container--gaming.select2-container--open .select2-selection{
    border-color:#5081ff !important;
    box-shadow:0 0 0 3px rgba(80,129,255,.2) !important;
  }
.select2-container .select2-selection__clear,.select2-container--default .select2-selection__clear,.select2-container--gaming .select2-selection__clear{
    align-items:center !important;
    background:hsla(0,0%,100%,.1) !important;
    border-radius:50% !important;
    color:#9ca3af !important;
    display:flex !important;
    font-size:1.1em !important;
    font-weight:700 !important;
    height:20px !important;
    justify-content:center !important;
    margin-right:0.5rem !important;
    padding:0.25rem !important;
    transition:all 0.2s ease-in-out !important;
    width:20px !important;
  }
.select2-container .select2-selection__clear:hover,.select2-container--default .select2-selection__clear:hover,.select2-container--gaming .select2-selection__clear:hover{
    background:#ef4444 !important;
    color:#fff !important;
    transform:scale(1.1) !important;
  }
.gaming-pagination-controls{
    align-items:center;
    display:flex;
    flex-wrap:wrap;
    gap:1rem;
    justify-content:center;
    margin-bottom:0.5rem;
  }
.gaming-auto-advance-btn{
    align-items:center;
    background:linear-gradient(145deg, rgba(39,36,80,.6), rgba(30,41,59,.4));
    border:1px solid rgba(80,129,255,.3);
    border-radius:0.5rem;
    color:#ffffff99;
    cursor:pointer;
    display:inline-flex;
    font-family:Montserrat,sans-serif;
    font-size:0.75rem;
    font-weight:600;
    gap:0.375rem;
    justify-content:center;
    overflow:hidden;
    padding:0.5rem 0.75rem;
    position:relative;
    transition:all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    -webkit-user-select:none;
    -moz-user-select:none;
    user-select:none;
  }
.gaming-auto-advance-btn:before{
    background:linear-gradient(90deg, transparent, hsla(0,0%,100%,.1), transparent);
    content:"";
    height:100%;
    left:-100%;
    position:absolute;
    top:0;
    transition:left 0.5s ease;
    width:100%;
  }
.gaming-auto-advance-btn:hover:before{
    left:100%;
  }
.gaming-auto-advance-btn:hover{
    background:linear-gradient(145deg, rgba(80,129,255,.2), rgba(75,125,255,.3));
    border-color:rgba(80,129,255,.6);
    
    box-shadow:0 4px 8px rgba(80,129,255,.3),0 0 12px rgba(80,129,255,.2);
    color:#fff;
  }
.gaming-auto-advance-btn--active{
    animation:gaming-auto-advance-pulse 2s infinite;
    background:linear-gradient(135deg, #10b981, #059669 50%, #047857);
    border-color:rgba(16,185,129,.8);
    box-shadow:0 2px 8px rgba(16,185,129,.4),0 0 12px rgba(16,185,129,.3);
    color:#fff;
  }
.gaming-auto-advance-btn--active:hover{
    background:linear-gradient(135deg, #059669, #047857 50%, #065f46);
   
    box-shadow:0 4px 12px rgba(16,185,129,.5),0 0 16px rgba(16,185,129,.4);
  }
.gaming-auto-advance-icon{
    flex-shrink:0;
    transition:transform 0.3s ease;
  }
.gaming-auto-advance-btn:hover .gaming-auto-advance-icon{
    transform:scale(1.1);
  }
.gaming-auto-advance-btn--active .gaming-auto-advance-icon{
    animation:gaming-auto-advance-spin 2s linear infinite;
  }
.gaming-auto-advance-text{
    font-size:0.75rem;
    font-weight:600;
    white-space:nowrap;
  }
.gaming-auto-advance-countdown{
    align-items:center;
    -webkit-backdrop-filter:blur(5px);
            backdrop-filter:blur(5px);
    background:linear-gradient(145deg, rgba(80,129,255,.1), rgba(75,125,255,.2));
    border:1px solid rgba(80,129,255,.4);
    border-radius:0.5rem;
    display:flex;
    flex-direction:column;
    gap:0.25rem;
    padding:0.5rem;
  }
.gaming-countdown-circle{
    align-items:center;
    display:flex;
    justify-content:center;
    position:relative;
  }
.gaming-countdown-svg{
    transform:rotate(-90deg);
  }
.gaming-countdown-progress{
    transition:stroke-dashoffset 1s linear;
  }
.gaming-countdown-text{
    color:#5081ff;
    font-family:Montserrat,sans-serif;
    font-size:0.875rem;
    font-weight:700;
    left:50%;
    position:absolute;
    text-shadow:0 0 4px rgba(80,129,255,.5);
    top:50%;
    transform:translate(-50%, -50%);
  }
.gaming-countdown-label{
    color:#ffffff99;
    font-family:Montserrat,sans-serif;
    font-size:0.6875rem;
    text-align:center;
    white-space:nowrap;
  }
@keyframes gaming-auto-advance-pulse{
    0%,to{
      box-shadow:0 2px 8px rgba(16,185,129,.4),0 0 12px rgba(16,185,129,.3);
    }
    50%{
      box-shadow:0 4px 12px rgba(16,185,129,.6),0 0 20px rgba(16,185,129,.5);
    }
  }
@keyframes gaming-auto-advance-spin{
    0%{
      transform:rotate(0deg);
    }
    to{
      transform:rotate(360deg);
    }
  }
.before\:absolute:before{content:var(--tw-content);position:absolute;}
.before\:inset-0:before{content:var(--tw-content);inset:0px;}
.before\:top-0:before{content:var(--tw-content);top:0px;}
.before\:block:before{content:var(--tw-content);display:block;}
.before\:h-full:before{content:var(--tw-content);height:100%;}
.before\:w-0:before{content:var(--tw-content);width:0px;}
.before\:translate-x-\[-100\%\]:before{content:var(--tw-content);--tw-translate-x:-100%;transform:translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));}
.before\:bg-\[\#fff\]:before{content:var(--tw-content);--tw-bg-opacity:1;background-color:rgb(255 255 255 / var(--tw-bg-opacity, 1));}
.before\:bg-gradient-to-r:before{background-image:linear-gradient(to right, var(--tw-gradient-stops));content:var(--tw-content);}
.before\:from-transparent:before{content:var(--tw-content);--tw-gradient-from:transparent var(--tw-gradient-from-position);--tw-gradient-to:transparent var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to);}
.before\:via-white\/20:before{content:var(--tw-content);--tw-gradient-to:hsla(0,0%,100%,0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), hsla(0,0%,100%,.2) var(--tw-gradient-via-position), var(--tw-gradient-to);}
.before\:to-transparent:before{content:var(--tw-content);--tw-gradient-to:transparent var(--tw-gradient-to-position);}
.before\:transition-transform:before{content:var(--tw-content);transition-duration:150ms;transition-property:transform;transition-timing-function:cubic-bezier(0.4, 0, 0.2, 1);}
.before\:duration-700:before{content:var(--tw-content);transition-duration:700ms;}
.before\:ease-in-out:before{content:var(--tw-content);transition-timing-function:cubic-bezier(0.4, 0, 0.2, 1);}
.before\:content-\[\"\"\]:before{--tw-content:"";content:var(--tw-content);}
.hover\:-translate-y-1:hover{--tw-translate-y:-0.25rem;}
.hover\:-translate-y-1:hover,.hover\:-translate-y-2:hover{transform:translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));}
.hover\:-translate-y-2:hover{--tw-translate-y:-0.5rem;}
.hover\:rotate-90:hover{--tw-rotate:90deg;}
.hover\:rotate-90:hover,.hover\:scale-105:hover{transform:translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));}
.hover\:scale-105:hover{--tw-scale-x:1.05;--tw-scale-y:1.05;}
.hover\:scale-\[1\.02\]:hover{--tw-scale-x:1.02;--tw-scale-y:1.02;}
.hover\:scale-\[1\.02\]:hover,.hover\:scale-\[1\.05\]:hover{transform:translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));}
.hover\:scale-\[1\.05\]:hover{--tw-scale-x:1.05;--tw-scale-y:1.05;}
.hover\:border-\[\#4B7DFF\]\/60:hover{border-color:rgba(75,125,255,.6);}
.hover\:border-\[\#4B7DFF\]\/80:hover{border-color:rgba(75,125,255,.8);}
.hover\:border-\[\#5081FF\]:hover{--tw-border-opacity:1;border-color:rgb(80 129 255 / var(--tw-border-opacity, 1));}
.hover\:border-\[\#6366F1\]:hover{--tw-border-opacity:1;border-color:rgb(99 102 241 / var(--tw-border-opacity, 1));}
.hover\:border-\[\#ff062e\]:hover{--tw-border-opacity:1;border-color:rgb(255 6 46 / var(--tw-border-opacity, 1));}
.hover\:border-gaming-blue:hover{--tw-border-opacity:1;border-color:rgb(80 129 255 / var(--tw-border-opacity, 1));}
.hover\:border-gaming-blue\/40:hover{border-color:rgba(80,129,255,.4);}
.hover\:border-opacity-40:hover{--tw-border-opacity:0.4;}
.hover\:bg-\[\#0052CC\]:hover{--tw-bg-opacity:1;background-color:rgb(0 82 204 / var(--tw-bg-opacity, 1));}
.hover\:bg-\[\#3463DB\]:hover{--tw-bg-opacity:1;background-color:rgb(52 99 219 / var(--tw-bg-opacity, 1));}
.hover\:bg-\[\#4752C4\]:hover{--tw-bg-opacity:1;background-color:rgb(71 82 196 / var(--tw-bg-opacity, 1));}
.hover\:bg-\[\#ff062e\]\/20:hover{background-color:rgba(255,6,46,.2);}
.hover\:bg-gaming-blue\/40:hover{background-color:rgba(80,129,255,.4);}
.hover\:bg-gray-600\/50:hover{background-color:rgba(75,85,99,.5);}
.hover\:bg-mandy-500:hover{--tw-bg-opacity:1;background-color:rgb(229 62 62 / var(--tw-bg-opacity, 1));}
.hover\:bg-red-400\/10:hover{background-color:hsla(0,91%,71%,.1);}
.hover\:bg-opacity-80:hover{--tw-bg-opacity:0.8;}
.hover\:from-\[\#4B7DFF\]:hover{--tw-gradient-from:#4b7dff var(--tw-gradient-from-position);--tw-gradient-to:rgba(75,125,255,0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to);}
.hover\:from-\[\#5081FF\]:hover{--tw-gradient-from:#5081ff var(--tw-gradient-from-position);--tw-gradient-to:rgba(80,129,255,0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to);}
.hover\:from-amber-600:hover{--tw-gradient-from:#d97706 var(--tw-gradient-from-position);--tw-gradient-to:rgba(217,119,6,0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to);}
.hover\:from-red-600:hover{--tw-gradient-from:#dc2626 var(--tw-gradient-from-position);--tw-gradient-to:rgba(220,38,38,0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to);}
.hover\:via-\[\#4B7DFF\]:hover{--tw-gradient-to:rgba(75,125,255,0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), #4b7dff var(--tw-gradient-via-position), var(--tw-gradient-to);}
.hover\:to-\[\#2A52BE\]:hover{--tw-gradient-to:#2a52be var(--tw-gradient-to-position);}
.hover\:to-\[\#5081FF\]:hover{--tw-gradient-to:#5081ff var(--tw-gradient-to-position);}
.hover\:to-\[\#7C3AED\]:hover{--tw-gradient-to:#7c3aed var(--tw-gradient-to-position);}
.hover\:to-orange-600:hover{--tw-gradient-to:#ea580c var(--tw-gradient-to-position);}
.hover\:to-red-700:hover{--tw-gradient-to:#b91c1c var(--tw-gradient-to-position);}
.hover\:text-\[\#fff\]:hover{--tw-text-opacity:1;color:rgb(255 255 255 / var(--tw-text-opacity, 1));}
.hover\:shadow:hover{--tw-shadow:0 1px 3px 0 rgba(0,0,0,.1), 0 1px 2px -1px rgba(0,0,0,.1);--tw-shadow-colored:0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);}
.hover\:shadow-2xl:hover,.hover\:shadow:hover{box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);}
.hover\:shadow-2xl:hover{--tw-shadow:0 25px 50px -12px rgba(0,0,0,.25);--tw-shadow-colored:0 25px 50px -12px var(--tw-shadow-color);}
.hover\:shadow-lg:hover{--tw-shadow:0 10px 15px -3px rgba(0,0,0,.1), 0 4px 6px -4px rgba(0,0,0,.1);--tw-shadow-colored:0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);}
.hover\:shadow-lg:hover,.hover\:shadow-md:hover{box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);}
.hover\:shadow-md:hover{--tw-shadow:0 4px 6px -1px rgba(0,0,0,.1), 0 2px 4px -2px rgba(0,0,0,.1);--tw-shadow-colored:0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);}
.hover\:shadow-xl:hover{--tw-shadow:0 20px 25px -5px rgba(0,0,0,.1), 0 8px 10px -6px rgba(0,0,0,.1);--tw-shadow-colored:0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);}
.hover\:shadow-\[\#0068FF\]\/30:hover{--tw-shadow-color:rgba(0,104,255,.3);--tw-shadow:var(--tw-shadow-colored);}
.hover\:shadow-\[\#4B7DFF\]\/30:hover{--tw-shadow-color:rgba(75,125,255,.3);--tw-shadow:var(--tw-shadow-colored);}
.hover\:shadow-\[\#4B7DFF\]\/40:hover{--tw-shadow-color:rgba(75,125,255,.4);--tw-shadow:var(--tw-shadow-colored);}
.hover\:shadow-\[\#5081FF\]\/40:hover{--tw-shadow-color:rgba(80,129,255,.4);--tw-shadow:var(--tw-shadow-colored);}
.hover\:shadow-\[\#5081FF\]\/50:hover{--tw-shadow-color:rgba(80,129,255,.5);--tw-shadow:var(--tw-shadow-colored);}
.hover\:shadow-\[\#5865F2\]\/30:hover{--tw-shadow-color:rgba(88,101,242,.3);--tw-shadow:var(--tw-shadow-colored);}
.hover\:shadow-\[\#6366F1\]\/50:hover{--tw-shadow-color:rgba(99,102,241,.5);--tw-shadow:var(--tw-shadow-colored);}
.hover\:shadow-mandy-500:hover{--tw-shadow-color:#e53e3e;--tw-shadow:var(--tw-shadow-colored);}
.hover\:before\:translate-x-\[100\%\]:hover:before{content:var(--tw-content);--tw-translate-x:100%;transform:translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));}
.focus\:border-\[\#00FF88\]:focus{--tw-border-opacity:1;border-color:rgb(0 255 136 / var(--tw-border-opacity, 1));}
.focus\:border-\[\#5081FF\]:focus{--tw-border-opacity:1;border-color:rgb(80 129 255 / var(--tw-border-opacity, 1));}
.focus\:border-\[\#9C27B0\]:focus{--tw-border-opacity:1;border-color:rgb(156 39 176 / var(--tw-border-opacity, 1));}
.focus\:border-\[\#FFD700\]:focus{--tw-border-opacity:1;border-color:rgb(255 215 0 / var(--tw-border-opacity, 1));}
.focus\:ring-2:focus{--tw-ring-offset-shadow:var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);--tw-ring-shadow:var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);box-shadow:var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);}
.focus\:ring-\[\#00FF88\]\/20:focus{--tw-ring-color:rgba(0,255,136,.2);}
.focus\:ring-\[\#5081FF\]\/20:focus{--tw-ring-color:rgba(80,129,255,.2);}
.focus\:ring-\[\#9C27B0\]\/20:focus{--tw-ring-color:rgba(156,39,176,.2);}
.focus\:ring-\[\#FFD700\]\/20:focus{--tw-ring-color:rgba(255,215,0,.2);}
.focus\:ring-red-500:focus{--tw-ring-opacity:1;--tw-ring-color:rgb(239 68 68 / var(--tw-ring-opacity, 1));}
.focus\:ring-offset-2:focus{--tw-ring-offset-width:2px;}
.focus\:ring-offset-gray-800:focus{--tw-ring-offset-color:#1f2937;}
.focus-visible\:outline-none:focus-visible{outline:2px solid transparent;outline-offset:2px;}
.focus-visible\:ring-2:focus-visible{--tw-ring-offset-shadow:var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);--tw-ring-shadow:var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);box-shadow:var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);}
.focus-visible\:ring-\[\#0068FF\]:focus-visible{--tw-ring-opacity:1;--tw-ring-color:rgb(0 104 255 / var(--tw-ring-opacity, 1));}
.focus-visible\:ring-\[\#4B7DFF\]:focus-visible{--tw-ring-opacity:1;--tw-ring-color:rgb(75 125 255 / var(--tw-ring-opacity, 1));}
.focus-visible\:ring-\[\#5865F2\]:focus-visible{--tw-ring-opacity:1;--tw-ring-color:rgb(88 101 242 / var(--tw-ring-opacity, 1));}
.focus-visible\:ring-offset-2:focus-visible{--tw-ring-offset-width:2px;}
.disabled\:pointer-events-none:disabled{pointer-events:none;}
.group\/btn:hover .group-hover\/btn\:-translate-x-full{--tw-translate-x:-100%;}
.group\/btn:hover .group-hover\/btn\:-translate-x-full,.group\/btn:hover .group-hover\/btn\:translate-x-1{transform:translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));}
.group\/btn:hover .group-hover\/btn\:translate-x-1{--tw-translate-x:0.25rem;}
.group\/btn:hover .group-hover\/btn\:translate-x-full{--tw-translate-x:100%;}
.group:hover .group-hover\:translate-x-1,.group\/btn:hover .group-hover\/btn\:translate-x-full{transform:translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));}
.group:hover .group-hover\:translate-x-1{--tw-translate-x:0.25rem;}
.group:hover .group-hover\:scale-105{--tw-scale-x:1.05;--tw-scale-y:1.05;}
.group:hover .group-hover\:scale-105,.group:hover .group-hover\:scale-\[1\.02\]{transform:translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));}
.group:hover .group-hover\:scale-\[1\.02\]{--tw-scale-x:1.02;--tw-scale-y:1.02;}
.group\/stat:hover .group-hover\/stat\:scale-x-100{--tw-scale-x:1;transform:translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));}
.group\/btn:hover .group-hover\/btn\:animate-ping{animation:ping 1s cubic-bezier(0, 0, 0.2, 1) infinite;}
.group\/btn:hover .group-hover\/btn\:animate-pulse{animation:pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;}
.group:hover .group-hover\:border-blue-400\/60{border-color:rgba(96,165,250,.6);}
.group:hover .group-hover\:text-white{--tw-text-opacity:1;color:rgb(255 255 255 / var(--tw-text-opacity, 1));}
.group:hover .group-hover\:opacity-100,.group\/btn:hover .group-hover\/btn\:opacity-100,.group\/stat:hover .group-hover\/stat\:opacity-100{opacity:1;}
.group:hover .group-hover\:opacity-20{opacity:0.2;}
.group\/btn:hover .group-hover\/btn\:shadow-\[\#00FFFF\]\/50{--tw-shadow-color:rgba(0,255,255,.5);--tw-shadow:var(--tw-shadow-colored);}
.group:hover .group-hover\:shadow-blue-500\/25{--tw-shadow-color:rgba(59,130,246,.25);--tw-shadow:var(--tw-shadow-colored);}
.group:hover .group-hover\:brightness-110{--tw-brightness:brightness(1.1);filter:var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);}
.ltr\:right-0:where([dir=ltr],[dir=ltr] *){right:0px;}
.ltr\:right-6:where([dir=ltr],[dir=ltr] *){right:1.5rem;}
.ltr\:ml-1:where([dir=ltr],[dir=ltr] *){margin-left:0.25rem;}
.rtl\:left-0:where([dir=rtl],[dir=rtl] *){left:0px;}
.rtl\:left-6:where([dir=rtl],[dir=rtl] *){left:1.5rem;}
.rtl\:mr-1:where([dir=rtl],[dir=rtl] *){margin-right:0.25rem;}
.rtl\:translate-x-full:where([dir=rtl],[dir=rtl] *){--tw-translate-x:100%;transform:translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));}
.rtl\:rotate-180:where([dir=rtl],[dir=rtl] *){--tw-rotate:180deg;transform:translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));}
@media (min-width:555px){@media not all and (min-width:768px){.min-\[555px\]\:max-md\:text-\[18px\]{font-size:18px}}}
@media (min-width:640px){.\!container{max-width:640px !important;}.container{max-width:640px;}.sm\:inline{display:inline;}.sm\:max-w-sm{max-width:24rem;}.sm\:grid-cols-2{grid-template-columns:repeat(2, minmax(0, 1fr));}.sm\:p-0{padding:0px;}}
@media (min-width:640px){.sm\:pointer-events-none{pointer-events:none}.sm\:absolute{position:absolute}.sm\:relative{position:relative}.sm\:inset-auto{inset:auto}.sm\:right-4{right:1rem}.sm\:z-40{z-index:40}.sm\:-mx-dynamic-1{margin-left:-1px;margin-right:-1px}.sm\:-mx-dynamic-10{margin-left:-10px;margin-right:-10px}.sm\:-mx-dynamic-100{margin-left:-100px;margin-right:-100px}.sm\:-mx-dynamic-11{margin-left:-11px;margin-right:-11px}.sm\:-mx-dynamic-12{margin-left:-12px;margin-right:-12px}.sm\:-mx-dynamic-13{margin-left:-13px;margin-right:-13px}.sm\:-mx-dynamic-14{margin-left:-14px;margin-right:-14px}.sm\:-mx-dynamic-15{margin-left:-15px;margin-right:-15px}.sm\:-mx-dynamic-16{margin-left:-16px;margin-right:-16px}.sm\:-mx-dynamic-17{margin-left:-17px;margin-right:-17px}.sm\:-mx-dynamic-18{margin-left:-18px;margin-right:-18px}.sm\:-mx-dynamic-19{margin-left:-19px;margin-right:-19px}.sm\:-mx-dynamic-2{margin-left:-2px;margin-right:-2px}.sm\:-mx-dynamic-20{margin-left:-20px;margin-right:-20px}.sm\:-mx-dynamic-21{margin-left:-21px;margin-right:-21px}.sm\:-mx-dynamic-22{margin-left:-22px;margin-right:-22px}.sm\:-mx-dynamic-23{margin-left:-23px;margin-right:-23px}.sm\:-mx-dynamic-24{margin-left:-24px;margin-right:-24px}.sm\:-mx-dynamic-25{margin-left:-25px;margin-right:-25px}.sm\:-mx-dynamic-26{margin-left:-26px;margin-right:-26px}.sm\:-mx-dynamic-27{margin-left:-27px;margin-right:-27px}.sm\:-mx-dynamic-28{margin-left:-28px;margin-right:-28px}.sm\:-mx-dynamic-29{margin-left:-29px;margin-right:-29px}.sm\:-mx-dynamic-3{margin-left:-3px;margin-right:-3px}.sm\:-mx-dynamic-30{margin-left:-30px;margin-right:-30px}.sm\:-mx-dynamic-31{margin-left:-31px;margin-right:-31px}.sm\:-mx-dynamic-32{margin-left:-32px;margin-right:-32px}.sm\:-mx-dynamic-33{margin-left:-33px;margin-right:-33px}.sm\:-mx-dynamic-34{margin-left:-34px;margin-right:-34px}.sm\:-mx-dynamic-35{margin-left:-35px;margin-right:-35px}.sm\:-mx-dynamic-36{margin-left:-36px;margin-right:-36px}.sm\:-mx-dynamic-37{margin-left:-37px;margin-right:-37px}.sm\:-mx-dynamic-38{margin-left:-38px;margin-right:-38px}.sm\:-mx-dynamic-39{margin-left:-39px;margin-right:-39px}.sm\:-mx-dynamic-4{margin-left:-4px;margin-right:-4px}.sm\:-mx-dynamic-40{margin-left:-40px;margin-right:-40px}.sm\:-mx-dynamic-41{margin-left:-41px;margin-right:-41px}.sm\:-mx-dynamic-42{margin-left:-42px;margin-right:-42px}.sm\:-mx-dynamic-43{margin-left:-43px;margin-right:-43px}.sm\:-mx-dynamic-44{margin-left:-44px;margin-right:-44px}.sm\:-mx-dynamic-45{margin-left:-45px;margin-right:-45px}.sm\:-mx-dynamic-46{margin-left:-46px;margin-right:-46px}.sm\:-mx-dynamic-47{margin-left:-47px;margin-right:-47px}.sm\:-mx-dynamic-48{margin-left:-48px;margin-right:-48px}.sm\:-mx-dynamic-49{margin-left:-49px;margin-right:-49px}.sm\:-mx-dynamic-5{margin-left:-5px;margin-right:-5px}.sm\:-mx-dynamic-50{margin-left:-50px;margin-right:-50px}.sm\:-mx-dynamic-51{margin-left:-51px;margin-right:-51px}.sm\:-mx-dynamic-52{margin-left:-52px;margin-right:-52px}.sm\:-mx-dynamic-53{margin-left:-53px;margin-right:-53px}.sm\:-mx-dynamic-54{margin-left:-54px;margin-right:-54px}.sm\:-mx-dynamic-55{margin-left:-55px;margin-right:-55px}.sm\:-mx-dynamic-56{margin-left:-56px;margin-right:-56px}.sm\:-mx-dynamic-57{margin-left:-57px;margin-right:-57px}.sm\:-mx-dynamic-58{margin-left:-58px;margin-right:-58px}.sm\:-mx-dynamic-59{margin-left:-59px;margin-right:-59px}.sm\:-mx-dynamic-6{margin-left:-6px;margin-right:-6px}.sm\:-mx-dynamic-60{margin-left:-60px;margin-right:-60px}.sm\:-mx-dynamic-61{margin-left:-61px;margin-right:-61px}.sm\:-mx-dynamic-62{margin-left:-62px;margin-right:-62px}.sm\:-mx-dynamic-63{margin-left:-63px;margin-right:-63px}.sm\:-mx-dynamic-64{margin-left:-64px;margin-right:-64px}.sm\:-mx-dynamic-65{margin-left:-65px;margin-right:-65px}.sm\:-mx-dynamic-66{margin-left:-66px;margin-right:-66px}.sm\:-mx-dynamic-67{margin-left:-67px;margin-right:-67px}.sm\:-mx-dynamic-68{margin-left:-68px;margin-right:-68px}.sm\:-mx-dynamic-69{margin-left:-69px;margin-right:-69px}.sm\:-mx-dynamic-7{margin-left:-7px;margin-right:-7px}.sm\:-mx-dynamic-70{margin-left:-70px;margin-right:-70px}.sm\:-mx-dynamic-71{margin-left:-71px;margin-right:-71px}.sm\:-mx-dynamic-72{margin-left:-72px;margin-right:-72px}.sm\:-mx-dynamic-73{margin-left:-73px;margin-right:-73px}.sm\:-mx-dynamic-74{margin-left:-74px;margin-right:-74px}.sm\:-mx-dynamic-75{margin-left:-75px;margin-right:-75px}.sm\:-mx-dynamic-76{margin-left:-76px;margin-right:-76px}.sm\:-mx-dynamic-77{margin-left:-77px;margin-right:-77px}.sm\:-mx-dynamic-78{margin-left:-78px;margin-right:-78px}.sm\:-mx-dynamic-79{margin-left:-79px;margin-right:-79px}.sm\:-mx-dynamic-8{margin-left:-8px;margin-right:-8px}.sm\:-mx-dynamic-80{margin-left:-80px;margin-right:-80px}.sm\:-mx-dynamic-81{margin-left:-81px;margin-right:-81px}.sm\:-mx-dynamic-82{margin-left:-82px;margin-right:-82px}.sm\:-mx-dynamic-83{margin-left:-83px;margin-right:-83px}.sm\:-mx-dynamic-84{margin-left:-84px;margin-right:-84px}.sm\:-mx-dynamic-85{margin-left:-85px;margin-right:-85px}.sm\:-mx-dynamic-86{margin-left:-86px;margin-right:-86px}.sm\:-mx-dynamic-87{margin-left:-87px;margin-right:-87px}.sm\:-mx-dynamic-88{margin-left:-88px;margin-right:-88px}.sm\:-mx-dynamic-89{margin-left:-89px;margin-right:-89px}.sm\:-mx-dynamic-9{margin-left:-9px;margin-right:-9px}.sm\:-mx-dynamic-90{margin-left:-90px;margin-right:-90px}.sm\:-mx-dynamic-91{margin-left:-91px;margin-right:-91px}.sm\:-mx-dynamic-92{margin-left:-92px;margin-right:-92px}.sm\:-mx-dynamic-93{margin-left:-93px;margin-right:-93px}.sm\:-mx-dynamic-94{margin-left:-94px;margin-right:-94px}.sm\:-mx-dynamic-95{margin-left:-95px;margin-right:-95px}.sm\:-mx-dynamic-96{margin-left:-96px;margin-right:-96px}.sm\:-mx-dynamic-97{margin-left:-97px;margin-right:-97px}.sm\:-mx-dynamic-98{margin-left:-98px;margin-right:-98px}.sm\:-mx-dynamic-99{margin-left:-99px;margin-right:-99px}.sm\:\!mb-\[16px\]{margin-bottom:16px!important}.sm\:ms-1-24{margin-inline-start:4.166666666666666%}.sm\:ms-10-24{margin-inline-start:41.66666666666667%}.sm\:ms-11-24{margin-inline-start:45.83333333333333%}.sm\:ms-12-24{margin-inline-start:50%}.sm\:ms-13-24{margin-inline-start:54.166666666666664%}.sm\:ms-14-24{margin-inline-start:58.333333333333336%}.sm\:ms-15-24{margin-inline-start:62.5%}.sm\:ms-16-24{margin-inline-start:66.66666666666666%}.sm\:ms-17-24{margin-inline-start:70.83333333333334%}.sm\:ms-18-24{margin-inline-start:75%}.sm\:ms-19-24{margin-inline-start:79.16666666666666%}.sm\:ms-2-24{margin-inline-start:8.333333333333332%}.sm\:ms-20-24{margin-inline-start:83.33333333333334%}.sm\:ms-21-24{margin-inline-start:87.5%}.sm\:ms-22-24{margin-inline-start:91.66666666666666%}.sm\:ms-23-24{margin-inline-start:95.83333333333334%}.sm\:ms-24-24{margin-inline-start:100%}.sm\:ms-3-24{margin-inline-start:12.5%}.sm\:ms-4-24{margin-inline-start:16.666666666666664%}.sm\:ms-5-24{margin-inline-start:20.833333333333336%}.sm\:ms-6-24{margin-inline-start:25%}.sm\:ms-7-24{margin-inline-start:29.166666666666668%}.sm\:ms-8-24{margin-inline-start:33.33333333333333%}.sm\:ms-9-24{margin-inline-start:37.5%}.sm\:mt-10{margin-top:2.5rem}.sm\:mt-\[16px\]{margin-top:16px}.sm\:block{display:block}.sm\:flex{display:flex}.sm\:hidden{display:none}.sm\:h-\[calc\(80vh-90px\)\]{height:calc(80vh - 90px)}.sm\:h-max{height:-moz-max-content;height:max-content}.sm\:max-h-\[80vh\]{max-height:80vh}.sm\:max-h-\[calc\(80vh-48px\)\]{max-height:calc(80vh - 48px)}.sm\:w-1-24{width:4.166666666666666%}.sm\:w-10-24{width:41.66666666666667%}.sm\:w-11-24{width:45.83333333333333%}.sm\:w-12-24{width:50%}.sm\:w-13-24{width:54.166666666666664%}.sm\:w-14-24{width:58.333333333333336%}.sm\:w-15-24{width:62.5%}.sm\:w-16-24{width:66.66666666666666%}.sm\:w-17-24{width:70.83333333333334%}.sm\:w-18-24{width:75%}.sm\:w-19-24{width:79.16666666666666%}.sm\:w-2-24{width:8.333333333333332%}.sm\:w-20-24{width:83.33333333333334%}.sm\:w-21-24{width:87.5%}.sm\:w-22-24{width:91.66666666666666%}.sm\:w-23-24{width:95.83333333333334%}.sm\:w-24-24{width:100%}.sm\:w-3-24{width:12.5%}.sm\:w-4-24{width:16.666666666666664%}.sm\:w-5-24{width:20.833333333333336%}.sm\:w-6-24{width:25%}.sm\:w-7-24{width:29.166666666666668%}.sm\:w-8-24{width:33.33333333333333%}.sm\:w-9{width:2.25rem}.sm\:w-9-24{width:37.5%}.sm\:w-\[180px\]{width:180px}.sm\:w-\[200px\]{width:200px}.sm\:w-\[263px\]{width:263px}.sm\:w-\[270px\]{width:270px}.sm\:w-\[352px\]{width:352px}.sm\:w-\[600px\]{width:600px}.sm\:w-\[calc\(100\%-102px\)\]{width:calc(100% - 102px)}.sm\:w-col-1{width:100%}.sm\:w-col-10{width:10%}.sm\:w-col-11{width:9.090909090909092%}.sm\:w-col-12{width:8.333333333333334%}.sm\:w-col-13{width:7.6923076923076925%}.sm\:w-col-14{width:7.142857142857143%}.sm\:w-col-15{width:6.666666666666667%}.sm\:w-col-16{width:6.25%}.sm\:w-col-17{width:5.882352941176471%}.sm\:w-col-18{width:5.555555555555555%}.sm\:w-col-19{width:5.2631578947368425%}.sm\:w-col-2{width:50%}.sm\:w-col-20{width:5%}.sm\:w-col-21{width:4.761904761904762%}.sm\:w-col-22{width:4.545454545454546%}.sm\:w-col-23{width:4.3478260869565215%}.sm\:w-col-24{width:4.166666666666667%}.sm\:w-col-3{width:33.333333333333336%}.sm\:w-col-4{width:25%}.sm\:w-col-5{width:20%}.sm\:w-col-6{width:16.666666666666668%}.sm\:w-col-7{width:14.285714285714286%}.sm\:w-col-8{width:12.5%}.sm\:w-col-9{width:11.11111111111111%}.sm\:flex-1{flex:1 1 0%}.sm\:cursor-auto{cursor:auto}.sm\:items-start{align-items:flex-start}.sm\:items-end{align-items:flex-end}.sm\:items-center{align-items:center}.sm\:items-stretch{align-items:stretch}.sm\:justify-start{justify-content:flex-start}.sm\:justify-end{justify-content:flex-end}.sm\:justify-center{justify-content:center}.sm\:justify-between{justify-content:space-between}.sm\:justify-around{justify-content:space-around}.sm\:justify-evenly{justify-content:space-evenly}.sm\:gap-\[24px\]{gap:24px}.sm\:gap-x-4{-moz-column-gap:1rem;column-gap:1rem}.sm\:gap-y-dynamic-1{row-gap:1px}.sm\:gap-y-dynamic-10{row-gap:10px}.sm\:gap-y-dynamic-100{row-gap:100px}.sm\:gap-y-dynamic-11{row-gap:11px}.sm\:gap-y-dynamic-12{row-gap:12px}.sm\:gap-y-dynamic-13{row-gap:13px}.sm\:gap-y-dynamic-14{row-gap:14px}.sm\:gap-y-dynamic-15{row-gap:15px}.sm\:gap-y-dynamic-16{row-gap:16px}.sm\:gap-y-dynamic-17{row-gap:17px}.sm\:gap-y-dynamic-18{row-gap:18px}.sm\:gap-y-dynamic-19{row-gap:19px}.sm\:gap-y-dynamic-2{row-gap:2px}.sm\:gap-y-dynamic-20{row-gap:20px}.sm\:gap-y-dynamic-21{row-gap:21px}.sm\:gap-y-dynamic-22{row-gap:22px}.sm\:gap-y-dynamic-23{row-gap:23px}.sm\:gap-y-dynamic-24{row-gap:24px}.sm\:gap-y-dynamic-25{row-gap:25px}.sm\:gap-y-dynamic-26{row-gap:26px}.sm\:gap-y-dynamic-27{row-gap:27px}.sm\:gap-y-dynamic-28{row-gap:28px}.sm\:gap-y-dynamic-29{row-gap:29px}.sm\:gap-y-dynamic-3{row-gap:3px}.sm\:gap-y-dynamic-30{row-gap:30px}.sm\:gap-y-dynamic-31{row-gap:31px}.sm\:gap-y-dynamic-32{row-gap:32px}.sm\:gap-y-dynamic-33{row-gap:33px}.sm\:gap-y-dynamic-34{row-gap:34px}.sm\:gap-y-dynamic-35{row-gap:35px}.sm\:gap-y-dynamic-36{row-gap:36px}.sm\:gap-y-dynamic-37{row-gap:37px}.sm\:gap-y-dynamic-38{row-gap:38px}.sm\:gap-y-dynamic-39{row-gap:39px}.sm\:gap-y-dynamic-4{row-gap:4px}.sm\:gap-y-dynamic-40{row-gap:40px}.sm\:gap-y-dynamic-41{row-gap:41px}.sm\:gap-y-dynamic-42{row-gap:42px}.sm\:gap-y-dynamic-43{row-gap:43px}.sm\:gap-y-dynamic-44{row-gap:44px}.sm\:gap-y-dynamic-45{row-gap:45px}.sm\:gap-y-dynamic-46{row-gap:46px}.sm\:gap-y-dynamic-47{row-gap:47px}.sm\:gap-y-dynamic-48{row-gap:48px}.sm\:gap-y-dynamic-49{row-gap:49px}.sm\:gap-y-dynamic-5{row-gap:5px}.sm\:gap-y-dynamic-50{row-gap:50px}.sm\:gap-y-dynamic-51{row-gap:51px}.sm\:gap-y-dynamic-52{row-gap:52px}.sm\:gap-y-dynamic-53{row-gap:53px}.sm\:gap-y-dynamic-54{row-gap:54px}.sm\:gap-y-dynamic-55{row-gap:55px}.sm\:gap-y-dynamic-56{row-gap:56px}.sm\:gap-y-dynamic-57{row-gap:57px}.sm\:gap-y-dynamic-58{row-gap:58px}.sm\:gap-y-dynamic-59{row-gap:59px}.sm\:gap-y-dynamic-6{row-gap:6px}.sm\:gap-y-dynamic-60{row-gap:60px}.sm\:gap-y-dynamic-61{row-gap:61px}.sm\:gap-y-dynamic-62{row-gap:62px}.sm\:gap-y-dynamic-63{row-gap:63px}.sm\:gap-y-dynamic-64{row-gap:64px}.sm\:gap-y-dynamic-65{row-gap:65px}.sm\:gap-y-dynamic-66{row-gap:66px}.sm\:gap-y-dynamic-67{row-gap:67px}.sm\:gap-y-dynamic-68{row-gap:68px}.sm\:gap-y-dynamic-69{row-gap:69px}.sm\:gap-y-dynamic-7{row-gap:7px}.sm\:gap-y-dynamic-70{row-gap:70px}.sm\:gap-y-dynamic-71{row-gap:71px}.sm\:gap-y-dynamic-72{row-gap:72px}.sm\:gap-y-dynamic-73{row-gap:73px}.sm\:gap-y-dynamic-74{row-gap:74px}.sm\:gap-y-dynamic-75{row-gap:75px}.sm\:gap-y-dynamic-76{row-gap:76px}.sm\:gap-y-dynamic-77{row-gap:77px}.sm\:gap-y-dynamic-78{row-gap:78px}.sm\:gap-y-dynamic-79{row-gap:79px}.sm\:gap-y-dynamic-8{row-gap:8px}.sm\:gap-y-dynamic-80{row-gap:80px}.sm\:gap-y-dynamic-81{row-gap:81px}.sm\:gap-y-dynamic-82{row-gap:82px}.sm\:gap-y-dynamic-83{row-gap:83px}.sm\:gap-y-dynamic-84{row-gap:84px}.sm\:gap-y-dynamic-85{row-gap:85px}.sm\:gap-y-dynamic-86{row-gap:86px}.sm\:gap-y-dynamic-87{row-gap:87px}.sm\:gap-y-dynamic-88{row-gap:88px}.sm\:gap-y-dynamic-89{row-gap:89px}.sm\:gap-y-dynamic-9{row-gap:9px}.sm\:gap-y-dynamic-90{row-gap:90px}.sm\:gap-y-dynamic-91{row-gap:91px}.sm\:gap-y-dynamic-92{row-gap:92px}.sm\:gap-y-dynamic-93{row-gap:93px}.sm\:gap-y-dynamic-94{row-gap:94px}.sm\:gap-y-dynamic-95{row-gap:95px}.sm\:gap-y-dynamic-96{row-gap:96px}.sm\:gap-y-dynamic-97{row-gap:97px}.sm\:gap-y-dynamic-98{row-gap:98px}.sm\:gap-y-dynamic-99{row-gap:99px}.sm\:rounded-3xl{border-radius:1.5rem}.sm\:px-6{padding-left:1.5rem;padding-right:1.5rem}.sm\:px-9{padding-left:2.25rem;padding-right:2.25rem}.sm\:px-\[48\.5px\]{padding-left:48.5px;padding-right:48.5px}.sm\:px-\[60px\]{padding-left:60px;padding-right:60px}.sm\:px-dynamic-1{padding-left:1px;padding-right:1px}.sm\:px-dynamic-10{padding-left:10px;padding-right:10px}.sm\:px-dynamic-100{padding-left:100px;padding-right:100px}.sm\:px-dynamic-11{padding-left:11px;padding-right:11px}.sm\:px-dynamic-12{padding-left:12px;padding-right:12px}.sm\:px-dynamic-13{padding-left:13px;padding-right:13px}.sm\:px-dynamic-14{padding-left:14px;padding-right:14px}.sm\:px-dynamic-15{padding-left:15px;padding-right:15px}.sm\:px-dynamic-16{padding-left:16px;padding-right:16px}.sm\:px-dynamic-17{padding-left:17px;padding-right:17px}.sm\:px-dynamic-18{padding-left:18px;padding-right:18px}.sm\:px-dynamic-19{padding-left:19px;padding-right:19px}.sm\:px-dynamic-2{padding-left:2px;padding-right:2px}.sm\:px-dynamic-20{padding-left:20px;padding-right:20px}.sm\:px-dynamic-21{padding-left:21px;padding-right:21px}.sm\:px-dynamic-22{padding-left:22px;padding-right:22px}.sm\:px-dynamic-23{padding-left:23px;padding-right:23px}.sm\:px-dynamic-24{padding-left:24px;padding-right:24px}.sm\:px-dynamic-25{padding-left:25px;padding-right:25px}.sm\:px-dynamic-26{padding-left:26px;padding-right:26px}.sm\:px-dynamic-27{padding-left:27px;padding-right:27px}.sm\:px-dynamic-28{padding-left:28px;padding-right:28px}.sm\:px-dynamic-29{padding-left:29px;padding-right:29px}.sm\:px-dynamic-3{padding-left:3px;padding-right:3px}.sm\:px-dynamic-30{padding-left:30px;padding-right:30px}.sm\:px-dynamic-31{padding-left:31px;padding-right:31px}.sm\:px-dynamic-32{padding-left:32px;padding-right:32px}.sm\:px-dynamic-33{padding-left:33px;padding-right:33px}.sm\:px-dynamic-34{padding-left:34px;padding-right:34px}.sm\:px-dynamic-35{padding-left:35px;padding-right:35px}.sm\:px-dynamic-36{padding-left:36px;padding-right:36px}.sm\:px-dynamic-37{padding-left:37px;padding-right:37px}.sm\:px-dynamic-38{padding-left:38px;padding-right:38px}.sm\:px-dynamic-39{padding-left:39px;padding-right:39px}.sm\:px-dynamic-4{padding-left:4px;padding-right:4px}.sm\:px-dynamic-40{padding-left:40px;padding-right:40px}.sm\:px-dynamic-41{padding-left:41px;padding-right:41px}.sm\:px-dynamic-42{padding-left:42px;padding-right:42px}.sm\:px-dynamic-43{padding-left:43px;padding-right:43px}.sm\:px-dynamic-44{padding-left:44px;padding-right:44px}.sm\:px-dynamic-45{padding-left:45px;padding-right:45px}.sm\:px-dynamic-46{padding-left:46px;padding-right:46px}.sm\:px-dynamic-47{padding-left:47px;padding-right:47px}.sm\:px-dynamic-48{padding-left:48px;padding-right:48px}.sm\:px-dynamic-49{padding-left:49px;padding-right:49px}.sm\:px-dynamic-5{padding-left:5px;padding-right:5px}.sm\:px-dynamic-50{padding-left:50px;padding-right:50px}.sm\:px-dynamic-51{padding-left:51px;padding-right:51px}.sm\:px-dynamic-52{padding-left:52px;padding-right:52px}.sm\:px-dynamic-53{padding-left:53px;padding-right:53px}.sm\:px-dynamic-54{padding-left:54px;padding-right:54px}.sm\:px-dynamic-55{padding-left:55px;padding-right:55px}.sm\:px-dynamic-56{padding-left:56px;padding-right:56px}.sm\:px-dynamic-57{padding-left:57px;padding-right:57px}.sm\:px-dynamic-58{padding-left:58px;padding-right:58px}.sm\:px-dynamic-59{padding-left:59px;padding-right:59px}.sm\:px-dynamic-6{padding-left:6px;padding-right:6px}.sm\:px-dynamic-60{padding-left:60px;padding-right:60px}.sm\:px-dynamic-61{padding-left:61px;padding-right:61px}.sm\:px-dynamic-62{padding-left:62px;padding-right:62px}.sm\:px-dynamic-63{padding-left:63px;padding-right:63px}.sm\:px-dynamic-64{padding-left:64px;padding-right:64px}.sm\:px-dynamic-65{padding-left:65px;padding-right:65px}.sm\:px-dynamic-66{padding-left:66px;padding-right:66px}.sm\:px-dynamic-67{padding-left:67px;padding-right:67px}.sm\:px-dynamic-68{padding-left:68px;padding-right:68px}.sm\:px-dynamic-69{padding-left:69px;padding-right:69px}.sm\:px-dynamic-7{padding-left:7px;padding-right:7px}.sm\:px-dynamic-70{padding-left:70px;padding-right:70px}.sm\:px-dynamic-71{padding-left:71px;padding-right:71px}.sm\:px-dynamic-72{padding-left:72px;padding-right:72px}.sm\:px-dynamic-73{padding-left:73px;padding-right:73px}.sm\:px-dynamic-74{padding-left:74px;padding-right:74px}.sm\:px-dynamic-75{padding-left:75px;padding-right:75px}.sm\:px-dynamic-76{padding-left:76px;padding-right:76px}.sm\:px-dynamic-77{padding-left:77px;padding-right:77px}.sm\:px-dynamic-78{padding-left:78px;padding-right:78px}.sm\:px-dynamic-79{padding-left:79px;padding-right:79px}.sm\:px-dynamic-8{padding-left:8px;padding-right:8px}.sm\:px-dynamic-80{padding-left:80px;padding-right:80px}.sm\:px-dynamic-81{padding-left:81px;padding-right:81px}.sm\:px-dynamic-82{padding-left:82px;padding-right:82px}.sm\:px-dynamic-83{padding-left:83px;padding-right:83px}.sm\:px-dynamic-84{padding-left:84px;padding-right:84px}.sm\:px-dynamic-85{padding-left:85px;padding-right:85px}.sm\:px-dynamic-86{padding-left:86px;padding-right:86px}.sm\:px-dynamic-87{padding-left:87px;padding-right:87px}.sm\:px-dynamic-88{padding-left:88px;padding-right:88px}.sm\:px-dynamic-89{padding-left:89px;padding-right:89px}.sm\:px-dynamic-9{padding-left:9px;padding-right:9px}.sm\:px-dynamic-90{padding-left:90px;padding-right:90px}.sm\:px-dynamic-91{padding-left:91px;padding-right:91px}.sm\:px-dynamic-92{padding-left:92px;padding-right:92px}.sm\:px-dynamic-93{padding-left:93px;padding-right:93px}.sm\:px-dynamic-94{padding-left:94px;padding-right:94px}.sm\:px-dynamic-95{padding-left:95px;padding-right:95px}.sm\:px-dynamic-96{padding-left:96px;padding-right:96px}.sm\:px-dynamic-97{padding-left:97px;padding-right:97px}.sm\:px-dynamic-98{padding-left:98px;padding-right:98px}.sm\:px-dynamic-99{padding-left:99px;padding-right:99px}.sm\:text-left{text-align:left}.sm\:text-\[14px\]{font-size:14px}}
@media not all and (min-width:640px){.max-sm\:\!mb-0{margin-bottom:0!important}.max-sm\:\!mb-\[16px\]{margin-bottom:16px!important}.max-sm\:mb-\[16px\]{margin-bottom:16px}.max-sm\:mt-\[20px\]{margin-top:20px}.max-sm\:hidden{display:none}.max-sm\:h-\[calc\(100vh-114px\)\]{height:calc(100vh - 114px)}.max-sm\:h-\[calc\(100vh-150px\)\]{height:calc(100vh - 150px)}.max-sm\:h-\[calc\(100vh-68px\)\]{height:calc(100vh - 68px)}.max-sm\:h-full{height:100%}.max-sm\:w-full{width:100%}.max-sm\:flex-col{flex-direction:column}.max-sm\:gap-3{gap:.75rem}.max-sm\:text-center{text-align:center}}
@media (min-width:641px) and (max-width:1023px){
    .gaming-attribute-grid{
      gap:0.625rem;
      grid-template-columns:repeat(auto-fill, minmax(55px, 1fr));
    }
    .gaming-attribute-grid[style*="minmax(50px"]{
      gap:0.5rem !important;
      grid-template-columns:repeat(auto-fill, minmax(48px, 1fr)) !important;
    }
    .gaming-attribute-section{
      margin-bottom:1.5rem !important;
      padding:1.25rem 1.75rem !important;
    }}
@media (min-width:641px) and (max-width:1024px){
    .account-title-truncated{
      max-width:220px;
    }}
@media (min-width:641px) and (max-width:767px){
    .gaming-thumbnail{
      box-sizing:border-box !important;
      display:block !important;
      min-height:54px !important;
      min-width:72px !important;
      width:72px !important;
    }

    .gaming-thumbnail,.gaming-thumbnail img{
      margin:0 !important;
      padding:0 !important;
    }

    .gaming-thumbnail img{
      height:100% !important;
      left:0 !important;
      -o-object-fit:cover !important;
         object-fit:cover !important;
      -o-object-position:center !important;
         object-position:center !important;
      position:absolute !important;
      top:0 !important;
      width:100% !important;
    }}
@media (min-width:768px){.\!container{max-width:768px !important;}.container{max-width:768px;}.md\:h-16{height:4rem;}.md\:max-h-\[180px\]{max-height:180px;}.md\:max-w-\[345px\]{max-width:345px;}.md\:grid-cols-2{grid-template-columns:repeat(2, minmax(0, 1fr));}.md\:grid-cols-3{grid-template-columns:repeat(3, minmax(0, 1fr));}.md\:gap-2{gap:0.5rem;}.md\:gap-5{gap:1.25rem;}.md\:\!px-\[0\.65rem\]{padding-left:0.65rem !important;padding-right:0.65rem !important;}.md\:px-10{padding-left:2.5rem;padding-right:2.5rem;}.md\:px-20{padding-left:5rem;padding-right:5rem;}.md\:px-3{padding-left:0.75rem;padding-right:0.75rem;}.md\:py-2{padding-bottom:0.5rem;padding-top:0.5rem;}.md\:text-3xl{font-size:1.875rem;line-height:2.25rem;}.md\:text-4xl{font-size:2.25rem;line-height:2.5rem;}.md\:text-5xl{font-size:3rem;line-height:1;}.md\:text-sm{font-size:0.875rem;line-height:1.25rem;}.md\:text-xl{font-size:1.25rem;line-height:1.75rem;}}
@media (min-width:768px) and (max-width:1023px){
    .gaming-thumbnail{
      box-sizing:border-box !important;
      display:block !important;
      min-height:60px !important;
      min-width:80px !important;
      width:80px !important;
    }

    .gaming-thumbnail,.gaming-thumbnail img{
      margin:0 !important;
      padding:0 !important;
    }

    .gaming-thumbnail img{
      height:100% !important;
      left:0 !important;
      -o-object-fit:cover !important;
         object-fit:cover !important;
      -o-object-position:center !important;
         object-position:center !important;
      position:absolute !important;
      top:0 !important;
      width:100% !important;
    }
    .hero-carousel .swiper-slide{
        aspect-ratio:716/203;
    }}
@media (min-width:768px){.md\:-bottom-\[24px\]{bottom:-24px}.md\:col-span-2{grid-column:span 2 / span 2}.md\:col-span-4{grid-column:span 4 / span 4}.md\:col-span-9{grid-column:span 9 / span 9}.md\:row-span-2{grid-row:span 2 / span 2}.md\:-mx-dynamic-1{margin-left:-1px;margin-right:-1px}.md\:-mx-dynamic-10{margin-left:-10px;margin-right:-10px}.md\:-mx-dynamic-100{margin-left:-100px;margin-right:-100px}.md\:-mx-dynamic-11{margin-left:-11px;margin-right:-11px}.md\:-mx-dynamic-12{margin-left:-12px;margin-right:-12px}.md\:-mx-dynamic-13{margin-left:-13px;margin-right:-13px}.md\:-mx-dynamic-14{margin-left:-14px;margin-right:-14px}.md\:-mx-dynamic-15{margin-left:-15px;margin-right:-15px}.md\:-mx-dynamic-16{margin-left:-16px;margin-right:-16px}.md\:-mx-dynamic-17{margin-left:-17px;margin-right:-17px}.md\:-mx-dynamic-18{margin-left:-18px;margin-right:-18px}.md\:-mx-dynamic-19{margin-left:-19px;margin-right:-19px}.md\:-mx-dynamic-2{margin-left:-2px;margin-right:-2px}.md\:-mx-dynamic-20{margin-left:-20px;margin-right:-20px}.md\:-mx-dynamic-21{margin-left:-21px;margin-right:-21px}.md\:-mx-dynamic-22{margin-left:-22px;margin-right:-22px}.md\:-mx-dynamic-23{margin-left:-23px;margin-right:-23px}.md\:-mx-dynamic-24{margin-left:-24px;margin-right:-24px}.md\:-mx-dynamic-25{margin-left:-25px;margin-right:-25px}.md\:-mx-dynamic-26{margin-left:-26px;margin-right:-26px}.md\:-mx-dynamic-27{margin-left:-27px;margin-right:-27px}.md\:-mx-dynamic-28{margin-left:-28px;margin-right:-28px}.md\:-mx-dynamic-29{margin-left:-29px;margin-right:-29px}.md\:-mx-dynamic-3{margin-left:-3px;margin-right:-3px}.md\:-mx-dynamic-30{margin-left:-30px;margin-right:-30px}.md\:-mx-dynamic-31{margin-left:-31px;margin-right:-31px}.md\:-mx-dynamic-32{margin-left:-32px;margin-right:-32px}.md\:-mx-dynamic-33{margin-left:-33px;margin-right:-33px}.md\:-mx-dynamic-34{margin-left:-34px;margin-right:-34px}.md\:-mx-dynamic-35{margin-left:-35px;margin-right:-35px}.md\:-mx-dynamic-36{margin-left:-36px;margin-right:-36px}.md\:-mx-dynamic-37{margin-left:-37px;margin-right:-37px}.md\:-mx-dynamic-38{margin-left:-38px;margin-right:-38px}.md\:-mx-dynamic-39{margin-left:-39px;margin-right:-39px}.md\:-mx-dynamic-4{margin-left:-4px;margin-right:-4px}.md\:-mx-dynamic-40{margin-left:-40px;margin-right:-40px}.md\:-mx-dynamic-41{margin-left:-41px;margin-right:-41px}.md\:-mx-dynamic-42{margin-left:-42px;margin-right:-42px}.md\:-mx-dynamic-43{margin-left:-43px;margin-right:-43px}.md\:-mx-dynamic-44{margin-left:-44px;margin-right:-44px}.md\:-mx-dynamic-45{margin-left:-45px;margin-right:-45px}.md\:-mx-dynamic-46{margin-left:-46px;margin-right:-46px}.md\:-mx-dynamic-47{margin-left:-47px;margin-right:-47px}.md\:-mx-dynamic-48{margin-left:-48px;margin-right:-48px}.md\:-mx-dynamic-49{margin-left:-49px;margin-right:-49px}.md\:-mx-dynamic-5{margin-left:-5px;margin-right:-5px}.md\:-mx-dynamic-50{margin-left:-50px;margin-right:-50px}.md\:-mx-dynamic-51{margin-left:-51px;margin-right:-51px}.md\:-mx-dynamic-52{margin-left:-52px;margin-right:-52px}.md\:-mx-dynamic-53{margin-left:-53px;margin-right:-53px}.md\:-mx-dynamic-54{margin-left:-54px;margin-right:-54px}.md\:-mx-dynamic-55{margin-left:-55px;margin-right:-55px}.md\:-mx-dynamic-56{margin-left:-56px;margin-right:-56px}.md\:-mx-dynamic-57{margin-left:-57px;margin-right:-57px}.md\:-mx-dynamic-58{margin-left:-58px;margin-right:-58px}.md\:-mx-dynamic-59{margin-left:-59px;margin-right:-59px}.md\:-mx-dynamic-6{margin-left:-6px;margin-right:-6px}.md\:-mx-dynamic-60{margin-left:-60px;margin-right:-60px}.md\:-mx-dynamic-61{margin-left:-61px;margin-right:-61px}.md\:-mx-dynamic-62{margin-left:-62px;margin-right:-62px}.md\:-mx-dynamic-63{margin-left:-63px;margin-right:-63px}.md\:-mx-dynamic-64{margin-left:-64px;margin-right:-64px}.md\:-mx-dynamic-65{margin-left:-65px;margin-right:-65px}.md\:-mx-dynamic-66{margin-left:-66px;margin-right:-66px}.md\:-mx-dynamic-67{margin-left:-67px;margin-right:-67px}.md\:-mx-dynamic-68{margin-left:-68px;margin-right:-68px}.md\:-mx-dynamic-69{margin-left:-69px;margin-right:-69px}.md\:-mx-dynamic-7{margin-left:-7px;margin-right:-7px}.md\:-mx-dynamic-70{margin-left:-70px;margin-right:-70px}.md\:-mx-dynamic-71{margin-left:-71px;margin-right:-71px}.md\:-mx-dynamic-72{margin-left:-72px;margin-right:-72px}.md\:-mx-dynamic-73{margin-left:-73px;margin-right:-73px}.md\:-mx-dynamic-74{margin-left:-74px;margin-right:-74px}.md\:-mx-dynamic-75{margin-left:-75px;margin-right:-75px}.md\:-mx-dynamic-76{margin-left:-76px;margin-right:-76px}.md\:-mx-dynamic-77{margin-left:-77px;margin-right:-77px}.md\:-mx-dynamic-78{margin-left:-78px;margin-right:-78px}.md\:-mx-dynamic-79{margin-left:-79px;margin-right:-79px}.md\:-mx-dynamic-8{margin-left:-8px;margin-right:-8px}.md\:-mx-dynamic-80{margin-left:-80px;margin-right:-80px}.md\:-mx-dynamic-81{margin-left:-81px;margin-right:-81px}.md\:-mx-dynamic-82{margin-left:-82px;margin-right:-82px}.md\:-mx-dynamic-83{margin-left:-83px;margin-right:-83px}.md\:-mx-dynamic-84{margin-left:-84px;margin-right:-84px}.md\:-mx-dynamic-85{margin-left:-85px;margin-right:-85px}.md\:-mx-dynamic-86{margin-left:-86px;margin-right:-86px}.md\:-mx-dynamic-87{margin-left:-87px;margin-right:-87px}.md\:-mx-dynamic-88{margin-left:-88px;margin-right:-88px}.md\:-mx-dynamic-89{margin-left:-89px;margin-right:-89px}.md\:-mx-dynamic-9{margin-left:-9px;margin-right:-9px}.md\:-mx-dynamic-90{margin-left:-90px;margin-right:-90px}.md\:-mx-dynamic-91{margin-left:-91px;margin-right:-91px}.md\:-mx-dynamic-92{margin-left:-92px;margin-right:-92px}.md\:-mx-dynamic-93{margin-left:-93px;margin-right:-93px}.md\:-mx-dynamic-94{margin-left:-94px;margin-right:-94px}.md\:-mx-dynamic-95{margin-left:-95px;margin-right:-95px}.md\:-mx-dynamic-96{margin-left:-96px;margin-right:-96px}.md\:-mx-dynamic-97{margin-left:-97px;margin-right:-97px}.md\:-mx-dynamic-98{margin-left:-98px;margin-right:-98px}.md\:-mx-dynamic-99{margin-left:-99px;margin-right:-99px}.md\:my-\[24px\]{margin-bottom:24px;margin-top:24px}.md\:-mr-\[16px\]{margin-right:-16px}.md\:mb-\[16px\]{margin-bottom:16px}.md\:ml-\[24px\]{margin-left:24px}.md\:ml-\[53px\]{margin-left:53px}.md\:mr-\[24px\]{margin-right:24px}.md\:ms-1-24{margin-inline-start:4.166666666666666%}.md\:ms-10-24{margin-inline-start:41.66666666666667%}.md\:ms-11-24{margin-inline-start:45.83333333333333%}.md\:ms-12-24{margin-inline-start:50%}.md\:ms-13-24{margin-inline-start:54.166666666666664%}.md\:ms-14-24{margin-inline-start:58.333333333333336%}.md\:ms-15-24{margin-inline-start:62.5%}.md\:ms-16-24{margin-inline-start:66.66666666666666%}.md\:ms-17-24{margin-inline-start:70.83333333333334%}.md\:ms-18-24{margin-inline-start:75%}.md\:ms-19-24{margin-inline-start:79.16666666666666%}.md\:ms-2-24{margin-inline-start:8.333333333333332%}.md\:ms-20-24{margin-inline-start:83.33333333333334%}.md\:ms-21-24{margin-inline-start:87.5%}.md\:ms-22-24{margin-inline-start:91.66666666666666%}.md\:ms-23-24{margin-inline-start:95.83333333333334%}.md\:ms-24-24{margin-inline-start:100%}.md\:ms-3-24{margin-inline-start:12.5%}.md\:ms-4-24{margin-inline-start:16.666666666666664%}.md\:ms-5-24{margin-inline-start:20.833333333333336%}.md\:ms-6-24{margin-inline-start:25%}.md\:ms-7-24{margin-inline-start:29.166666666666668%}.md\:ms-8-24{margin-inline-start:33.33333333333333%}.md\:ms-9-24{margin-inline-start:37.5%}.md\:mt-\[0\.3em\]{margin-top:.3em}.md\:mt-\[16px\]{margin-top:16px}.md\:mt-\[20px\]{margin-top:20px}.md\:mt-\[24px\]{margin-top:24px}.md\:mt-\[36px\]{margin-top:36px}.md\:mt-\[48px\]{margin-top:48px}.md\:block{display:block}.md\:flex{display:flex}.md\:hidden{display:none}.md\:aspect-\[134\/180\]{aspect-ratio:134/180}.md\:aspect-\[222\/116\]{aspect-ratio:222/116}.md\:aspect-\[228\/94\]{aspect-ratio:228/94}.md\:aspect-\[716\/203\]{aspect-ratio:716/203}.md\:h-\[320px\]{height:320px}.md\:h-\[44px\]{height:44px}.md\:h-full{height:100%}.md\:w-1-24{width:4.166666666666666%}.md\:w-10-24{width:41.66666666666667%}.md\:w-11-24{width:45.83333333333333%}.md\:w-12-24{width:50%}.md\:w-13-24{width:54.166666666666664%}.md\:w-14-24{width:58.333333333333336%}.md\:w-15-24{width:62.5%}.md\:w-16-24{width:66.66666666666666%}.md\:w-17-24{width:70.83333333333334%}.md\:w-18-24{width:75%}.md\:w-19-24{width:79.16666666666666%}.md\:w-2-24{width:8.333333333333332%}.md\:w-20-24{width:83.33333333333334%}.md\:w-21-24{width:87.5%}.md\:w-22-24{width:91.66666666666666%}.md\:w-23-24{width:95.83333333333334%}.md\:w-24-24{width:100%}.md\:w-3-24{width:12.5%}.md\:w-4-24{width:16.666666666666664%}.md\:w-5-24{width:20.833333333333336%}.md\:w-6-24{width:25%}.md\:w-7-24{width:29.166666666666668%}.md\:w-8-24{width:33.33333333333333%}.md\:w-9-24{width:37.5%}.md\:w-\[242px\]{width:242px}.md\:w-\[304px\]{width:304px}.md\:w-\[312px\]{width:312px}.md\:w-\[44px\]{width:44px}.md\:w-auto{width:auto}.md\:w-col-1{width:100%}.md\:w-col-10{width:10%}.md\:w-col-11{width:9.090909090909092%}.md\:w-col-12{width:8.333333333333334%}.md\:w-col-13{width:7.6923076923076925%}.md\:w-col-14{width:7.142857142857143%}.md\:w-col-15{width:6.666666666666667%}.md\:w-col-16{width:6.25%}.md\:w-col-17{width:5.882352941176471%}.md\:w-col-18{width:5.555555555555555%}.md\:w-col-19{width:5.2631578947368425%}.md\:w-col-2{width:50%}.md\:w-col-20{width:5%}.md\:w-col-21{width:4.761904761904762%}.md\:w-col-22{width:4.545454545454546%}.md\:w-col-23{width:4.3478260869565215%}.md\:w-col-24{width:4.166666666666667%}.md\:w-col-3{width:33.333333333333336%}.md\:w-col-4{width:25%}.md\:w-col-5{width:20%}.md\:w-col-6{width:16.666666666666668%}.md\:w-col-7{width:14.285714285714286%}.md\:w-col-8{width:12.5%}.md\:w-col-9{width:11.11111111111111%}.md\:w-full{width:100%}.md\:max-w-\[262px\]{max-width:262px}.md\:max-w-\[304px\]{max-width:304px}.md\:max-w-\[312px\]{max-width:312px}.md\:max-w-\[566px\]{max-width:566px}.md\:flex-1{flex:1 1 0%}.md\:grid-cols-12{grid-template-columns:repeat(12,minmax(0,1fr));grid-template-columns:repeat(12, minmax(0, 1fr))}.md\:grid-cols-7{grid-template-columns:repeat(7,minmax(0,1fr));grid-template-columns:repeat(7, minmax(0, 1fr))}.md\:flex-col{flex-direction:column}.md\:flex-wrap{flex-wrap:wrap}.md\:items-start{align-items:flex-start}.md\:items-end{align-items:flex-end}.md\:items-center{align-items:center}.md\:items-stretch{align-items:stretch}.md\:justify-start{justify-content:flex-start}.md\:justify-end{justify-content:flex-end}.md\:justify-center{justify-content:center}.md\:justify-between{justify-content:space-between}.md\:justify-around{justify-content:space-around}.md\:justify-evenly{justify-content:space-evenly}.md\:gap-\[16px\]{gap:16px}.md\:gap-x-\[24px\]{-moz-column-gap:24px;column-gap:24px}.md\:gap-y-dynamic-1{row-gap:1px}.md\:gap-y-dynamic-10{row-gap:10px}.md\:gap-y-dynamic-100{row-gap:100px}.md\:gap-y-dynamic-11{row-gap:11px}.md\:gap-y-dynamic-12{row-gap:12px}.md\:gap-y-dynamic-13{row-gap:13px}.md\:gap-y-dynamic-14{row-gap:14px}.md\:gap-y-dynamic-15{row-gap:15px}.md\:gap-y-dynamic-16{row-gap:16px}.md\:gap-y-dynamic-17{row-gap:17px}.md\:gap-y-dynamic-18{row-gap:18px}.md\:gap-y-dynamic-19{row-gap:19px}.md\:gap-y-dynamic-2{row-gap:2px}.md\:gap-y-dynamic-20{row-gap:20px}.md\:gap-y-dynamic-21{row-gap:21px}.md\:gap-y-dynamic-22{row-gap:22px}.md\:gap-y-dynamic-23{row-gap:23px}.md\:gap-y-dynamic-24{row-gap:24px}.md\:gap-y-dynamic-25{row-gap:25px}.md\:gap-y-dynamic-26{row-gap:26px}.md\:gap-y-dynamic-27{row-gap:27px}.md\:gap-y-dynamic-28{row-gap:28px}.md\:gap-y-dynamic-29{row-gap:29px}.md\:gap-y-dynamic-3{row-gap:3px}.md\:gap-y-dynamic-30{row-gap:30px}.md\:gap-y-dynamic-31{row-gap:31px}.md\:gap-y-dynamic-32{row-gap:32px}.md\:gap-y-dynamic-33{row-gap:33px}.md\:gap-y-dynamic-34{row-gap:34px}.md\:gap-y-dynamic-35{row-gap:35px}.md\:gap-y-dynamic-36{row-gap:36px}.md\:gap-y-dynamic-37{row-gap:37px}.md\:gap-y-dynamic-38{row-gap:38px}.md\:gap-y-dynamic-39{row-gap:39px}.md\:gap-y-dynamic-4{row-gap:4px}.md\:gap-y-dynamic-40{row-gap:40px}.md\:gap-y-dynamic-41{row-gap:41px}.md\:gap-y-dynamic-42{row-gap:42px}.md\:gap-y-dynamic-43{row-gap:43px}.md\:gap-y-dynamic-44{row-gap:44px}.md\:gap-y-dynamic-45{row-gap:45px}.md\:gap-y-dynamic-46{row-gap:46px}.md\:gap-y-dynamic-47{row-gap:47px}.md\:gap-y-dynamic-48{row-gap:48px}.md\:gap-y-dynamic-49{row-gap:49px}.md\:gap-y-dynamic-5{row-gap:5px}.md\:gap-y-dynamic-50{row-gap:50px}.md\:gap-y-dynamic-51{row-gap:51px}.md\:gap-y-dynamic-52{row-gap:52px}.md\:gap-y-dynamic-53{row-gap:53px}.md\:gap-y-dynamic-54{row-gap:54px}.md\:gap-y-dynamic-55{row-gap:55px}.md\:gap-y-dynamic-56{row-gap:56px}.md\:gap-y-dynamic-57{row-gap:57px}.md\:gap-y-dynamic-58{row-gap:58px}.md\:gap-y-dynamic-59{row-gap:59px}.md\:gap-y-dynamic-6{row-gap:6px}.md\:gap-y-dynamic-60{row-gap:60px}.md\:gap-y-dynamic-61{row-gap:61px}.md\:gap-y-dynamic-62{row-gap:62px}.md\:gap-y-dynamic-63{row-gap:63px}.md\:gap-y-dynamic-64{row-gap:64px}.md\:gap-y-dynamic-65{row-gap:65px}.md\:gap-y-dynamic-66{row-gap:66px}.md\:gap-y-dynamic-67{row-gap:67px}.md\:gap-y-dynamic-68{row-gap:68px}.md\:gap-y-dynamic-69{row-gap:69px}.md\:gap-y-dynamic-7{row-gap:7px}.md\:gap-y-dynamic-70{row-gap:70px}.md\:gap-y-dynamic-71{row-gap:71px}.md\:gap-y-dynamic-72{row-gap:72px}.md\:gap-y-dynamic-73{row-gap:73px}.md\:gap-y-dynamic-74{row-gap:74px}.md\:gap-y-dynamic-75{row-gap:75px}.md\:gap-y-dynamic-76{row-gap:76px}.md\:gap-y-dynamic-77{row-gap:77px}.md\:gap-y-dynamic-78{row-gap:78px}.md\:gap-y-dynamic-79{row-gap:79px}.md\:gap-y-dynamic-8{row-gap:8px}.md\:gap-y-dynamic-80{row-gap:80px}.md\:gap-y-dynamic-81{row-gap:81px}.md\:gap-y-dynamic-82{row-gap:82px}.md\:gap-y-dynamic-83{row-gap:83px}.md\:gap-y-dynamic-84{row-gap:84px}.md\:gap-y-dynamic-85{row-gap:85px}.md\:gap-y-dynamic-86{row-gap:86px}.md\:gap-y-dynamic-87{row-gap:87px}.md\:gap-y-dynamic-88{row-gap:88px}.md\:gap-y-dynamic-89{row-gap:89px}.md\:gap-y-dynamic-9{row-gap:9px}.md\:gap-y-dynamic-90{row-gap:90px}.md\:gap-y-dynamic-91{row-gap:91px}.md\:gap-y-dynamic-92{row-gap:92px}.md\:gap-y-dynamic-93{row-gap:93px}.md\:gap-y-dynamic-94{row-gap:94px}.md\:gap-y-dynamic-95{row-gap:95px}.md\:gap-y-dynamic-96{row-gap:96px}.md\:gap-y-dynamic-97{row-gap:97px}.md\:gap-y-dynamic-98{row-gap:98px}.md\:gap-y-dynamic-99{row-gap:99px}.md\:space-y-\[16px\]>:not([hidden])~:not([hidden]){--tw-space-y-reverse:0;margin-bottom:calc(16px*var(--tw-space-y-reverse));margin-top:calc(16px*(1 - var(--tw-space-y-reverse)))}.md\:\!rounded-\[24px\]{border-radius:24px!important}.md\:rounded-\[16px\]{border-radius:16px}.md\:rounded-\[24px\]{border-radius:24px}.md\:border-none{border-style:none}.md\:bg-\[\#0000005C\]{background-color:#0000005c}.md\:bg-\[\#ffffff14\]{background-color:#ffffff14}.md\:bg-transparent{background-color:transparent}.md\:\!p-0{padding:0!important}.md\:p-0{padding:0}.md\:px-\[24px\]{padding-left:24px;padding-right:24px}.md\:px-\[32px\]{padding-left:32px;padding-right:32px}.md\:px-dynamic-1{padding-left:1px;padding-right:1px}.md\:px-dynamic-10{padding-left:10px;padding-right:10px}.md\:px-dynamic-100{padding-left:100px;padding-right:100px}.md\:px-dynamic-11{padding-left:11px;padding-right:11px}.md\:px-dynamic-12{padding-left:12px;padding-right:12px}.md\:px-dynamic-13{padding-left:13px;padding-right:13px}.md\:px-dynamic-14{padding-left:14px;padding-right:14px}.md\:px-dynamic-15{padding-left:15px;padding-right:15px}.md\:px-dynamic-16{padding-left:16px;padding-right:16px}.md\:px-dynamic-17{padding-left:17px;padding-right:17px}.md\:px-dynamic-18{padding-left:18px;padding-right:18px}.md\:px-dynamic-19{padding-left:19px;padding-right:19px}.md\:px-dynamic-2{padding-left:2px;padding-right:2px}.md\:px-dynamic-20{padding-left:20px;padding-right:20px}.md\:px-dynamic-21{padding-left:21px;padding-right:21px}.md\:px-dynamic-22{padding-left:22px;padding-right:22px}.md\:px-dynamic-23{padding-left:23px;padding-right:23px}.md\:px-dynamic-24{padding-left:24px;padding-right:24px}.md\:px-dynamic-25{padding-left:25px;padding-right:25px}.md\:px-dynamic-26{padding-left:26px;padding-right:26px}.md\:px-dynamic-27{padding-left:27px;padding-right:27px}.md\:px-dynamic-28{padding-left:28px;padding-right:28px}.md\:px-dynamic-29{padding-left:29px;padding-right:29px}.md\:px-dynamic-3{padding-left:3px;padding-right:3px}.md\:px-dynamic-30{padding-left:30px;padding-right:30px}.md\:px-dynamic-31{padding-left:31px;padding-right:31px}.md\:px-dynamic-32{padding-left:32px;padding-right:32px}.md\:px-dynamic-33{padding-left:33px;padding-right:33px}.md\:px-dynamic-34{padding-left:34px;padding-right:34px}.md\:px-dynamic-35{padding-left:35px;padding-right:35px}.md\:px-dynamic-36{padding-left:36px;padding-right:36px}.md\:px-dynamic-37{padding-left:37px;padding-right:37px}.md\:px-dynamic-38{padding-left:38px;padding-right:38px}.md\:px-dynamic-39{padding-left:39px;padding-right:39px}.md\:px-dynamic-4{padding-left:4px;padding-right:4px}.md\:px-dynamic-40{padding-left:40px;padding-right:40px}.md\:px-dynamic-41{padding-left:41px;padding-right:41px}.md\:px-dynamic-42{padding-left:42px;padding-right:42px}.md\:px-dynamic-43{padding-left:43px;padding-right:43px}.md\:px-dynamic-44{padding-left:44px;padding-right:44px}.md\:px-dynamic-45{padding-left:45px;padding-right:45px}.md\:px-dynamic-46{padding-left:46px;padding-right:46px}.md\:px-dynamic-47{padding-left:47px;padding-right:47px}.md\:px-dynamic-48{padding-left:48px;padding-right:48px}.md\:px-dynamic-49{padding-left:49px;padding-right:49px}.md\:px-dynamic-5{padding-left:5px;padding-right:5px}.md\:px-dynamic-50{padding-left:50px;padding-right:50px}.md\:px-dynamic-51{padding-left:51px;padding-right:51px}.md\:px-dynamic-52{padding-left:52px;padding-right:52px}.md\:px-dynamic-53{padding-left:53px;padding-right:53px}.md\:px-dynamic-54{padding-left:54px;padding-right:54px}.md\:px-dynamic-55{padding-left:55px;padding-right:55px}.md\:px-dynamic-56{padding-left:56px;padding-right:56px}.md\:px-dynamic-57{padding-left:57px;padding-right:57px}.md\:px-dynamic-58{padding-left:58px;padding-right:58px}.md\:px-dynamic-59{padding-left:59px;padding-right:59px}.md\:px-dynamic-6{padding-left:6px;padding-right:6px}.md\:px-dynamic-60{padding-left:60px;padding-right:60px}.md\:px-dynamic-61{padding-left:61px;padding-right:61px}.md\:px-dynamic-62{padding-left:62px;padding-right:62px}.md\:px-dynamic-63{padding-left:63px;padding-right:63px}.md\:px-dynamic-64{padding-left:64px;padding-right:64px}.md\:px-dynamic-65{padding-left:65px;padding-right:65px}.md\:px-dynamic-66{padding-left:66px;padding-right:66px}.md\:px-dynamic-67{padding-left:67px;padding-right:67px}.md\:px-dynamic-68{padding-left:68px;padding-right:68px}.md\:px-dynamic-69{padding-left:69px;padding-right:69px}.md\:px-dynamic-7{padding-left:7px;padding-right:7px}.md\:px-dynamic-70{padding-left:70px;padding-right:70px}.md\:px-dynamic-71{padding-left:71px;padding-right:71px}.md\:px-dynamic-72{padding-left:72px;padding-right:72px}.md\:px-dynamic-73{padding-left:73px;padding-right:73px}.md\:px-dynamic-74{padding-left:74px;padding-right:74px}.md\:px-dynamic-75{padding-left:75px;padding-right:75px}.md\:px-dynamic-76{padding-left:76px;padding-right:76px}.md\:px-dynamic-77{padding-left:77px;padding-right:77px}.md\:px-dynamic-78{padding-left:78px;padding-right:78px}.md\:px-dynamic-79{padding-left:79px;padding-right:79px}.md\:px-dynamic-8{padding-left:8px;padding-right:8px}.md\:px-dynamic-80{padding-left:80px;padding-right:80px}.md\:px-dynamic-81{padding-left:81px;padding-right:81px}.md\:px-dynamic-82{padding-left:82px;padding-right:82px}.md\:px-dynamic-83{padding-left:83px;padding-right:83px}.md\:px-dynamic-84{padding-left:84px;padding-right:84px}.md\:px-dynamic-85{padding-left:85px;padding-right:85px}.md\:px-dynamic-86{padding-left:86px;padding-right:86px}.md\:px-dynamic-87{padding-left:87px;padding-right:87px}.md\:px-dynamic-88{padding-left:88px;padding-right:88px}.md\:px-dynamic-89{padding-left:89px;padding-right:89px}.md\:px-dynamic-9{padding-left:9px;padding-right:9px}.md\:px-dynamic-90{padding-left:90px;padding-right:90px}.md\:px-dynamic-91{padding-left:91px;padding-right:91px}.md\:px-dynamic-92{padding-left:92px;padding-right:92px}.md\:px-dynamic-93{padding-left:93px;padding-right:93px}.md\:px-dynamic-94{padding-left:94px;padding-right:94px}.md\:px-dynamic-95{padding-left:95px;padding-right:95px}.md\:px-dynamic-96{padding-left:96px;padding-right:96px}.md\:px-dynamic-97{padding-left:97px;padding-right:97px}.md\:px-dynamic-98{padding-left:98px;padding-right:98px}.md\:px-dynamic-99{padding-left:99px;padding-right:99px}.md\:py-\[16px\]{padding-bottom:16px;padding-top:16px}.md\:pb-\[48px\]{padding-bottom:48px}.md\:pr-\[16px\]{padding-right:16px}.md\:text-\[16px\]{font-size:16px}.md\:text-\[17px\]{font-size:17px}.md\:text-\[18px\]{font-size:18px}.md\:text-\[20px\]{font-size:20px}.md\:text-\[24px\]{font-size:24px}.md\:text-\[32px\]{font-size:32px}.md\:leading-\[1\.4\]{line-height:1.4}.md\:leading-\[15px\]{line-height:15px}.md\:leading-\[20px\]{line-height:20px}.md\:leading-\[45px\]{line-height:45px}.md\:text-\[\#fff9\]{color:#fff9}.md\:transition-all{transition-duration:.15s;transition-duration:150ms;transition-property:all;transition-timing-function:cubic-bezier(.4,0,.2,1);transition-timing-function:cubic-bezier(0.4, 0, 0.2, 1)}.md\:duration-300{transition-duration:.3s;transition-duration:300ms}.md\:hover\:scale-110:hover{--tw-scale-x:1.1;--tw-scale-y:1.1}.group:hover .md\:group-hover\:scale-\[1\.25\],.md\:hover\:scale-110:hover{transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.group:hover .md\:group-hover\:scale-\[1\.25\]{--tw-scale-x:1.25;--tw-scale-y:1.25;transform:translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.group:hover .md\:group-hover\:text-\[\#fff\]{color:rgb(255 255 255/var(--tw-text-opacity,1));--tw-text-opacity:1;color:rgb(255 255 255 / var(--tw-text-opacity, 1))}@media not all and (min-width:1024px){.md\:max-lg\:text-\[16px\]{font-size:16px}.md\:max-lg\:leading-\[22px\]{line-height:22px}}@media (max-width:939px){.md\:max-\[939px\]\:min-w-\[304px\]{min-width:304px}}@media (max-width:894px){.md\:max-\[894px\]\:min-w-\[48px\]{min-width:48px}.md\:max-\[894px\]\:gap-\[16px\]{gap:16px}.md\:max-\[894px\]\:text-\[40px\]{font-size:40px}}}
@media not all and (min-width:768px){.max-md\:absolute{position:absolute}.max-md\:-top-\[12px\]{top:-12px}.max-md\:right-0{right:0}.max-md\:order-1{order:1}.max-md\:order-2{order:2}.max-md\:\!-mx-\[16px\]{margin-left:-16px !important;margin-right:-16px !important}.max-md\:\!my-\[24px\]{margin-bottom:24px!important;margin-top:24px!important}.max-md\:-mx-\[16px\]{
        margin-left:-16px;
        margin-right:-16px}.max-md\:\!mb-\[24px\]{margin-bottom:24px!important}.max-md\:mb-3{margin-bottom:.75rem}.max-md\:mb-\[24px\]{margin-bottom:24px}.max-md\:ml-auto{margin-left:auto}.max-md\:mt-0{margin-top:0;margin-top:0px}.max-md\:mt-\[12px\]{margin-top:12px}.max-md\:mt-\[24px\]{margin-top:24px}.max-md\:block{display:block}.max-md\:hidden{display:none}.max-md\:size-\[200px\]{height:200px;width:200px}.max-md\:h-\[48px\]{height:48px}.max-md\:h-\[88px\]{height:88px}.max-md\:w-full{width:100%}.max-md\:flex-1{flex:1 1 0%}.max-md\:flex-col{flex-direction:column}.max-md\:rounded-\[24px\]{border-radius:24px}.max-md\:\!bg-\[\#3A3E64\]{--tw-bg-opacity:1!important;background-color:rgb(58 62 100/var(--tw-bg-opacity,1))!important}.max-md\:bg-\[\#272450\]{--tw-bg-opacity:1;background-color:rgb(39 36 80/var(--tw-bg-opacity,1))}.max-md\:bg-transparent{background-color:transparent}.max-md\:\!p-\[16px\]{padding:16px!important}.max-md\:p-0{padding:0}.max-md\:p-4{padding:1rem}.max-md\:\!px-\[16px\]{padding-left:16px!important;padding-right:16px!important}.min-\[555px\]\:max-md\:text-\[18px\]{font-size:18px}}
@media (min-width:769px){
    .gaming-thumbnail-header{
      margin-bottom:0.375rem;
      padding-bottom:0.375rem;
    }

    .gaming-thumbnail-title{
      font-size:0.75rem !important;
    }

    .gaming-thumbnail-count{
      font-size:0.625rem !important;
      padding:0.125rem 0.375rem !important;
    }}
@media (min-width:769px) and (max-width:1024px){
    .gaming-pagination-container{
      gap:1.125rem;
      padding:1.5rem 1.25rem;
    }

    .gaming-pagination-list{
      gap:0.625rem;
      gap:0.5rem;
    }

    .gaming-pagination-btn{
      font-size:0.9375rem;
      font-size:0.8125rem;
      height:3rem;
      height:2rem;
      min-width:3rem;
      min-width:2rem;
      padding:0.625rem 0.875rem;
      padding:0.375rem 0.5rem;
    }

    .gaming-pagination-btn--nav{
      min-width:3.5rem;
      min-width:2.25rem;
      padding:0.625rem 1rem;
    }

    .gaming-pagination-text{
      font-size:0.9375rem;
      font-size:0.8125rem;
    }

    .gaming-pagination-info-text{
      font-size:0.9375rem;
      font-size:0.875rem;
    }

    .gaming-pagination-icon{
      height:16px;
      width:16px;
    }}
@media (min-width:1024px){.\!container{max-width:1024px !important;}.container{max-width:1024px;}
  .recharge-payment-section{
    width:33.333333% !important;
  }
  .recharge-history-section{
    width:66.666667% !important;
  }
    .gaming-attribute-grid{
      gap:0.875rem;
      grid-template-columns:repeat(auto-fill, minmax(65px, 1fr));
    }

    .gaming-thumbnail img{
      max-height:100% !important;
      max-width:100% !important;
      -o-object-fit:cover !important;
         object-fit:cover !important;
      -o-object-position:center !important;
         object-position:center !important;
    }

    .gaming-thumbnail img,.gaming-thumbnail-placeholder{
      height:100% !important;
      left:0 !important;
      margin:0 !important;
      padding:0 !important;
      position:absolute !important;
      top:0 !important;
      width:100% !important;
    }

    .gaming-thumbnail-container{
      align-items:flex-start !important;
      box-sizing:border-box !important;
      gap:0.5rem !important;
      max-width:100% !important;
      overflow-x:auto !important;
      overflow-y:hidden !important;
      padding:0.5rem 0.25rem !important;
      scroll-snap-type:x mandatory !important;
      width:100% !important;
    }

    .gaming-thumbnail{
      scroll-snap-align:start !important;
    }

    .gaming-thumbnail-gallery{
      padding:0.5rem;
    }

    .gaming-thumbnail-title{
      font-size:0.8rem;
    }

    .gaming-thumbnail-count{
      font-size:0.7rem;
      padding:0.2rem 0.4rem;
    }
    .gaming-attribute-grid[style*="minmax(50px"]{
      gap:0.625rem !important;
      grid-template-columns:repeat(auto-fill, minmax(55px, 1fr)) !important;
    }
    .hero-carousel .swiper-slide{
        aspect-ratio:986/280;
    }
    .gaming-pagination-container{
      gap:1.25rem;
      gap:1rem;
      padding:2rem;
      padding:1.5rem;
    }

    .gaming-pagination-list{
      gap:0.75rem;
      gap:0.5rem;
    }

    .gaming-pagination-btn{
      font-size:1rem;
      font-size:0.875rem;
      height:3rem;
      height:2.25rem;
      min-width:3rem;
      min-width:2.25rem;
      padding:0.75rem 1rem;
      padding:0.5rem 0.625rem;
    }

    .gaming-pagination-btn--nav{
      min-width:3.5rem;
      min-width:2.75rem;
    }

    .gaming-pagination-text{
      font-size:1rem;
      font-size:0.875rem;
    }

    .gaming-pagination-info-text{
      font-size:1rem;
      font-size:0.9375rem;
    }

    .gaming-pagination-btn--nav:hover{
      background:linear-gradient(145deg, rgba(75,125,255,.4), rgba(80,129,255,.6));
      box-shadow:0 12px 24px rgba(80,129,255,.4),0 0 30px rgba(80,129,255,.3);
    }

    .gaming-pagination-icon{
      height:18px;
      width:18px;
    }

    .lg\:bottom-0{bottom:0px;}

    .lg\:end-auto{inset-inline-end:auto;}

    .lg\:z-30{z-index:30;}

    .lg\:order-1{order:1;}

    .lg\:order-2{order:2;}

    .lg\:col-span-1{grid-column:span 1 / span 1;}

    .lg\:inline-block{display:inline-block;}

    .lg\:\!w-1\/3{width:33.333333% !important;}

    .lg\:\!w-2\/3{width:66.666667% !important;}

    .lg\:\!w-2\/5{width:40% !important;}

    .lg\:\!w-3\/5{width:60% !important;}

    .lg\:w-1\/3{width:33.333333%;}

    .lg\:w-2\/3{width:66.666667%;}

    .lg\:w-2\/5{width:40%;}

    .lg\:w-3\/5{width:60%;}

    .lg\:w-80{width:20rem;}

    .lg\:translate-x-0{--tw-translate-x:0px;transform:translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));}

    .lg\:grid-cols-2{grid-template-columns:repeat(2, minmax(0, 1fr));}

    .lg\:grid-cols-3{grid-template-columns:repeat(3, minmax(0, 1fr));}

    .lg\:gap-6{gap:1.5rem;}

    .lg\:p-6{padding:1.5rem;}

    .lg\:px-10{padding-left:2.5rem;padding-right:2.5rem;}

    .lg\:py-0{padding-bottom:0px;padding-top:0px;}

    .lg\:text-5xl{font-size:3rem;line-height:1;}

    .rtl\:lg\:translate-x-0:where([dir=rtl],[dir=rtl] *){--tw-translate-x:0px;transform:translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));}}
@media (min-width:1024px) and (max-width:1279px){
    .gaming-thumbnail{
      box-sizing:border-box !important;
      display:block !important;
      min-height:66px !important;
      min-width:88px !important;
      width:88px !important;
    }

    .gaming-thumbnail,.gaming-thumbnail img{
      margin:0 !important;
      padding:0 !important;
    }

    .gaming-thumbnail img{
      height:100% !important;
      left:0 !important;
      -o-object-fit:cover !important;
         object-fit:cover !important;
      -o-object-position:center !important;
         object-position:center !important;
      position:absolute !important;
      top:0 !important;
      width:100% !important;
    }

    .gaming-thumbnail-container{
      gap:1rem;
    }}
@media (min-width:1024px){.lg\:absolute{position:absolute}.lg\:col-span-3{grid-column:span 3 / span 3}.lg\:row-span-2{grid-row:span 2 / span 2}.lg\:-mx-dynamic-1{margin-left:-1px;margin-right:-1px}.lg\:-mx-dynamic-10{margin-left:-10px;margin-right:-10px}.lg\:-mx-dynamic-100{margin-left:-100px;margin-right:-100px}.lg\:-mx-dynamic-11{margin-left:-11px;margin-right:-11px}.lg\:-mx-dynamic-12{margin-left:-12px;margin-right:-12px}.lg\:-mx-dynamic-13{margin-left:-13px;margin-right:-13px}.lg\:-mx-dynamic-14{margin-left:-14px;margin-right:-14px}.lg\:-mx-dynamic-15{margin-left:-15px;margin-right:-15px}.lg\:-mx-dynamic-16{margin-left:-16px;margin-right:-16px}.lg\:-mx-dynamic-17{margin-left:-17px;margin-right:-17px}.lg\:-mx-dynamic-18{margin-left:-18px;margin-right:-18px}.lg\:-mx-dynamic-19{margin-left:-19px;margin-right:-19px}.lg\:-mx-dynamic-2{margin-left:-2px;margin-right:-2px}.lg\:-mx-dynamic-20{margin-left:-20px;margin-right:-20px}.lg\:-mx-dynamic-21{margin-left:-21px;margin-right:-21px}.lg\:-mx-dynamic-22{margin-left:-22px;margin-right:-22px}.lg\:-mx-dynamic-23{margin-left:-23px;margin-right:-23px}.lg\:-mx-dynamic-24{margin-left:-24px;margin-right:-24px}.lg\:-mx-dynamic-25{margin-left:-25px;margin-right:-25px}.lg\:-mx-dynamic-26{margin-left:-26px;margin-right:-26px}.lg\:-mx-dynamic-27{margin-left:-27px;margin-right:-27px}.lg\:-mx-dynamic-28{margin-left:-28px;margin-right:-28px}.lg\:-mx-dynamic-29{margin-left:-29px;margin-right:-29px}.lg\:-mx-dynamic-3{margin-left:-3px;margin-right:-3px}.lg\:-mx-dynamic-30{margin-left:-30px;margin-right:-30px}.lg\:-mx-dynamic-31{margin-left:-31px;margin-right:-31px}.lg\:-mx-dynamic-32{margin-left:-32px;margin-right:-32px}.lg\:-mx-dynamic-33{margin-left:-33px;margin-right:-33px}.lg\:-mx-dynamic-34{margin-left:-34px;margin-right:-34px}.lg\:-mx-dynamic-35{margin-left:-35px;margin-right:-35px}.lg\:-mx-dynamic-36{margin-left:-36px;margin-right:-36px}.lg\:-mx-dynamic-37{margin-left:-37px;margin-right:-37px}.lg\:-mx-dynamic-38{margin-left:-38px;margin-right:-38px}.lg\:-mx-dynamic-39{margin-left:-39px;margin-right:-39px}.lg\:-mx-dynamic-4{margin-left:-4px;margin-right:-4px}.lg\:-mx-dynamic-40{margin-left:-40px;margin-right:-40px}.lg\:-mx-dynamic-41{margin-left:-41px;margin-right:-41px}.lg\:-mx-dynamic-42{margin-left:-42px;margin-right:-42px}.lg\:-mx-dynamic-43{margin-left:-43px;margin-right:-43px}.lg\:-mx-dynamic-44{margin-left:-44px;margin-right:-44px}.lg\:-mx-dynamic-45{margin-left:-45px;margin-right:-45px}.lg\:-mx-dynamic-46{margin-left:-46px;margin-right:-46px}.lg\:-mx-dynamic-47{margin-left:-47px;margin-right:-47px}.lg\:-mx-dynamic-48{margin-left:-48px;margin-right:-48px}.lg\:-mx-dynamic-49{margin-left:-49px;margin-right:-49px}.lg\:-mx-dynamic-5{margin-left:-5px;margin-right:-5px}.lg\:-mx-dynamic-50{margin-left:-50px;margin-right:-50px}.lg\:-mx-dynamic-51{margin-left:-51px;margin-right:-51px}.lg\:-mx-dynamic-52{margin-left:-52px;margin-right:-52px}.lg\:-mx-dynamic-53{margin-left:-53px;margin-right:-53px}.lg\:-mx-dynamic-54{margin-left:-54px;margin-right:-54px}.lg\:-mx-dynamic-55{margin-left:-55px;margin-right:-55px}.lg\:-mx-dynamic-56{margin-left:-56px;margin-right:-56px}.lg\:-mx-dynamic-57{margin-left:-57px;margin-right:-57px}.lg\:-mx-dynamic-58{margin-left:-58px;margin-right:-58px}.lg\:-mx-dynamic-59{margin-left:-59px;margin-right:-59px}.lg\:-mx-dynamic-6{margin-left:-6px;margin-right:-6px}.lg\:-mx-dynamic-60{margin-left:-60px;margin-right:-60px}.lg\:-mx-dynamic-61{margin-left:-61px;margin-right:-61px}.lg\:-mx-dynamic-62{margin-left:-62px;margin-right:-62px}.lg\:-mx-dynamic-63{margin-left:-63px;margin-right:-63px}.lg\:-mx-dynamic-64{margin-left:-64px;margin-right:-64px}.lg\:-mx-dynamic-65{margin-left:-65px;margin-right:-65px}.lg\:-mx-dynamic-66{margin-left:-66px;margin-right:-66px}.lg\:-mx-dynamic-67{margin-left:-67px;margin-right:-67px}.lg\:-mx-dynamic-68{margin-left:-68px;margin-right:-68px}.lg\:-mx-dynamic-69{margin-left:-69px;margin-right:-69px}.lg\:-mx-dynamic-7{margin-left:-7px;margin-right:-7px}.lg\:-mx-dynamic-70{margin-left:-70px;margin-right:-70px}.lg\:-mx-dynamic-71{margin-left:-71px;margin-right:-71px}.lg\:-mx-dynamic-72{margin-left:-72px;margin-right:-72px}.lg\:-mx-dynamic-73{margin-left:-73px;margin-right:-73px}.lg\:-mx-dynamic-74{margin-left:-74px;margin-right:-74px}.lg\:-mx-dynamic-75{margin-left:-75px;margin-right:-75px}.lg\:-mx-dynamic-76{margin-left:-76px;margin-right:-76px}.lg\:-mx-dynamic-77{margin-left:-77px;margin-right:-77px}.lg\:-mx-dynamic-78{margin-left:-78px;margin-right:-78px}.lg\:-mx-dynamic-79{margin-left:-79px;margin-right:-79px}.lg\:-mx-dynamic-8{margin-left:-8px;margin-right:-8px}.lg\:-mx-dynamic-80{margin-left:-80px;margin-right:-80px}.lg\:-mx-dynamic-81{margin-left:-81px;margin-right:-81px}.lg\:-mx-dynamic-82{margin-left:-82px;margin-right:-82px}.lg\:-mx-dynamic-83{margin-left:-83px;margin-right:-83px}.lg\:-mx-dynamic-84{margin-left:-84px;margin-right:-84px}.lg\:-mx-dynamic-85{margin-left:-85px;margin-right:-85px}.lg\:-mx-dynamic-86{margin-left:-86px;margin-right:-86px}.lg\:-mx-dynamic-87{margin-left:-87px;margin-right:-87px}.lg\:-mx-dynamic-88{margin-left:-88px;margin-right:-88px}.lg\:-mx-dynamic-89{margin-left:-89px;margin-right:-89px}.lg\:-mx-dynamic-9{margin-left:-9px;margin-right:-9px}.lg\:-mx-dynamic-90{margin-left:-90px;margin-right:-90px}.lg\:-mx-dynamic-91{margin-left:-91px;margin-right:-91px}.lg\:-mx-dynamic-92{margin-left:-92px;margin-right:-92px}.lg\:-mx-dynamic-93{margin-left:-93px;margin-right:-93px}.lg\:-mx-dynamic-94{margin-left:-94px;margin-right:-94px}.lg\:-mx-dynamic-95{margin-left:-95px;margin-right:-95px}.lg\:-mx-dynamic-96{margin-left:-96px;margin-right:-96px}.lg\:-mx-dynamic-97{margin-left:-97px;margin-right:-97px}.lg\:-mx-dynamic-98{margin-left:-98px;margin-right:-98px}.lg\:-mx-dynamic-99{margin-left:-99px;margin-right:-99px}.lg\:my-\[36px\]{margin-bottom:36px;margin-top:36px}.lg\:-mr-\[16px\]{margin-right:-16px}.lg\:mb-\[24px\]{margin-bottom:24px}.lg\:mb-\[48px\]{margin-bottom:48px}.lg\:ms-1-24{margin-inline-start:4.166666666666666%}.lg\:ms-10-24{margin-inline-start:41.66666666666667%}.lg\:ms-11-24{margin-inline-start:45.83333333333333%}.lg\:ms-12-24{margin-inline-start:50%}.lg\:ms-13-24{margin-inline-start:54.166666666666664%}.lg\:ms-14-24{margin-inline-start:58.333333333333336%}.lg\:ms-15-24{margin-inline-start:62.5%}.lg\:ms-16-24{margin-inline-start:66.66666666666666%}.lg\:ms-17-24{margin-inline-start:70.83333333333334%}.lg\:ms-18-24{margin-inline-start:75%}.lg\:ms-19-24{margin-inline-start:79.16666666666666%}.lg\:ms-2-24{margin-inline-start:8.333333333333332%}.lg\:ms-20-24{margin-inline-start:83.33333333333334%}.lg\:ms-21-24{margin-inline-start:87.5%}.lg\:ms-22-24{margin-inline-start:91.66666666666666%}.lg\:ms-23-24{margin-inline-start:95.83333333333334%}.lg\:ms-24-24{margin-inline-start:100%}.lg\:ms-3-24{margin-inline-start:12.5%}.lg\:ms-4-24{margin-inline-start:16.666666666666664%}.lg\:ms-5-24{margin-inline-start:20.833333333333336%}.lg\:ms-6-24{margin-inline-start:25%}.lg\:ms-7-24{margin-inline-start:29.166666666666668%}.lg\:ms-8-24{margin-inline-start:33.33333333333333%}.lg\:ms-9-24{margin-inline-start:37.5%}.lg\:mt-6{margin-top:1.5rem}.lg\:mt-\[12px\]{margin-top:12px}.lg\:mt-\[16px\]{margin-top:16px}.lg\:mt-\[24px\]{margin-top:24px}.lg\:mt-\[32px\]{margin-top:32px}.lg\:mt-\[36px\]{margin-top:36px}.lg\:line-clamp-4{display:-webkit-box;overflow:hidden;-webkit-box-orient:vertical;-webkit-line-clamp:4}.lg\:block{display:block}.lg\:inline{display:inline}.lg\:flex{display:flex}.lg\:hidden{display:none}.lg\:aspect-\[134\/180\]{aspect-ratio:134/180}.lg\:aspect-\[191\/345\]{aspect-ratio:191/345}.lg\:aspect-\[312\/164\]{aspect-ratio:312/164}.lg\:aspect-\[318\/132\]{aspect-ratio:318/132}.lg\:aspect-\[986\/280\]{aspect-ratio:986/280}.lg\:size-\[44px\]{height:44px;width:44px}.lg\:h-\[15px\]{height:15px}.lg\:h-\[345px\]{height:345px}.lg\:h-\[420px\]{height:420px}.lg\:h-\[44px\]{height:44px}.lg\:h-\[95px\]{height:95px}.lg\:w-1-24{width:4.166666666666666%}.lg\:w-10-24{width:41.66666666666667%}.lg\:w-11-24{width:45.83333333333333%}.lg\:w-12-24{width:50%}.lg\:w-13-24{width:54.166666666666664%}.lg\:w-14-24{width:58.333333333333336%}.lg\:w-15-24{width:62.5%}.lg\:w-16-24{width:66.66666666666666%}.lg\:w-17-24{width:70.83333333333334%}.lg\:w-18-24{width:75%}.lg\:w-19-24{width:79.16666666666666%}.lg\:w-2-24{width:8.333333333333332%}.lg\:w-20-24{width:83.33333333333334%}.lg\:w-21-24{width:87.5%}.lg\:w-22-24{width:91.66666666666666%}.lg\:w-23-24{width:95.83333333333334%}.lg\:w-24-24{width:100%}.lg\:w-3-24{width:12.5%}.lg\:w-4-24{width:16.666666666666664%}.lg\:w-5-24{width:20.833333333333336%}.lg\:w-6-24{width:25%}.lg\:w-7-24{width:29.166666666666668%}.lg\:w-8-24{width:33.33333333333333%}.lg\:w-9-24{width:37.5%}.lg\:w-\[110px\]{width:110px}.lg\:w-\[191px\]{width:191px}.lg\:w-\[276px\]{width:276px}.lg\:w-\[312px\]{width:312px}.lg\:w-\[360px\]{width:360px}.lg\:w-\[388px\]{width:388px}.lg\:w-\[44px\]{width:44px}.lg\:w-\[734px\]{width:734px}.lg\:w-\[800px\]{width:800px}.lg\:w-\[872px\]{width:872px}.lg\:w-col-1{width:100%}.lg\:w-col-10{width:10%}.lg\:w-col-11{width:9.090909090909092%}.lg\:w-col-12{width:8.333333333333334%}.lg\:w-col-13{width:7.6923076923076925%}.lg\:w-col-14{width:7.142857142857143%}.lg\:w-col-15{width:6.666666666666667%}.lg\:w-col-16{width:6.25%}.lg\:w-col-17{width:5.882352941176471%}.lg\:w-col-18{width:5.555555555555555%}.lg\:w-col-19{width:5.2631578947368425%}.lg\:w-col-2{width:50%}.lg\:w-col-20{width:5%}.lg\:w-col-21{width:4.761904761904762%}.lg\:w-col-22{width:4.545454545454546%}.lg\:w-col-23{width:4.3478260869565215%}.lg\:w-col-24{width:4.166666666666667%}.lg\:w-col-3{width:33.333333333333336%}.lg\:w-col-4{width:25%}.lg\:w-col-5{width:20%}.lg\:w-col-6{width:16.666666666666668%}.lg\:w-col-7{width:14.285714285714286%}.lg\:w-col-8{width:12.5%}.lg\:w-col-9{width:11.11111111111111%}.lg\:w-full{width:100%}.lg\:w-max{width:-moz-max-content;width:max-content}.lg\:max-w-\[1030px\]{max-width:1030px}.lg\:max-w-\[110px\]{max-width:110px}.lg\:max-w-\[116px\]{max-width:116px}.lg\:max-w-\[312px\]{max-width:312px}.lg\:max-w-\[424px\]{max-width:424px}.lg\:max-w-\[536px\]{max-width:536px}.lg\:flex-1{flex:1 1 0%}.lg\:grid-cols-4{grid-template-columns:repeat(4,minmax(0,1fr));grid-template-columns:repeat(4, minmax(0, 1fr))}.lg\:grid-cols-7{grid-template-columns:repeat(7,minmax(0,1fr))}.lg\:flex-row{flex-direction:row}.lg\:flex-col{flex-direction:column}.lg\:flex-wrap{flex-wrap:wrap}.lg\:items-start{align-items:flex-start}.lg\:items-end{align-items:flex-end}.lg\:items-center{align-items:center}.lg\:items-stretch{align-items:stretch}.lg\:justify-start{justify-content:flex-start}.lg\:justify-end{justify-content:flex-end}.lg\:justify-center{justify-content:center}.lg\:justify-between{justify-content:space-between}.lg\:justify-around{justify-content:space-around}.lg\:justify-evenly{justify-content:space-evenly}.lg\:gap-4{gap:1rem}.lg\:gap-\[16px\]{gap:16px}.lg\:gap-\[24px\]{gap:24px}.lg\:gap-\[60px\]{gap:60px}.lg\:gap-x-\[24px\]{-moz-column-gap:24px;column-gap:24px}.lg\:gap-y-dynamic-1{row-gap:1px}.lg\:gap-y-dynamic-10{row-gap:10px}.lg\:gap-y-dynamic-100{row-gap:100px}.lg\:gap-y-dynamic-11{row-gap:11px}.lg\:gap-y-dynamic-12{row-gap:12px}.lg\:gap-y-dynamic-13{row-gap:13px}.lg\:gap-y-dynamic-14{row-gap:14px}.lg\:gap-y-dynamic-15{row-gap:15px}.lg\:gap-y-dynamic-16{row-gap:16px}.lg\:gap-y-dynamic-17{row-gap:17px}.lg\:gap-y-dynamic-18{row-gap:18px}.lg\:gap-y-dynamic-19{row-gap:19px}.lg\:gap-y-dynamic-2{row-gap:2px}.lg\:gap-y-dynamic-20{row-gap:20px}.lg\:gap-y-dynamic-21{row-gap:21px}.lg\:gap-y-dynamic-22{row-gap:22px}.lg\:gap-y-dynamic-23{row-gap:23px}.lg\:gap-y-dynamic-24{row-gap:24px}.lg\:gap-y-dynamic-25{row-gap:25px}.lg\:gap-y-dynamic-26{row-gap:26px}.lg\:gap-y-dynamic-27{row-gap:27px}.lg\:gap-y-dynamic-28{row-gap:28px}.lg\:gap-y-dynamic-29{row-gap:29px}.lg\:gap-y-dynamic-3{row-gap:3px}.lg\:gap-y-dynamic-30{row-gap:30px}.lg\:gap-y-dynamic-31{row-gap:31px}.lg\:gap-y-dynamic-32{row-gap:32px}.lg\:gap-y-dynamic-33{row-gap:33px}.lg\:gap-y-dynamic-34{row-gap:34px}.lg\:gap-y-dynamic-35{row-gap:35px}.lg\:gap-y-dynamic-36{row-gap:36px}.lg\:gap-y-dynamic-37{row-gap:37px}.lg\:gap-y-dynamic-38{row-gap:38px}.lg\:gap-y-dynamic-39{row-gap:39px}.lg\:gap-y-dynamic-4{row-gap:4px}.lg\:gap-y-dynamic-40{row-gap:40px}.lg\:gap-y-dynamic-41{row-gap:41px}.lg\:gap-y-dynamic-42{row-gap:42px}.lg\:gap-y-dynamic-43{row-gap:43px}.lg\:gap-y-dynamic-44{row-gap:44px}.lg\:gap-y-dynamic-45{row-gap:45px}.lg\:gap-y-dynamic-46{row-gap:46px}.lg\:gap-y-dynamic-47{row-gap:47px}.lg\:gap-y-dynamic-48{row-gap:48px}.lg\:gap-y-dynamic-49{row-gap:49px}.lg\:gap-y-dynamic-5{row-gap:5px}.lg\:gap-y-dynamic-50{row-gap:50px}.lg\:gap-y-dynamic-51{row-gap:51px}.lg\:gap-y-dynamic-52{row-gap:52px}.lg\:gap-y-dynamic-53{row-gap:53px}.lg\:gap-y-dynamic-54{row-gap:54px}.lg\:gap-y-dynamic-55{row-gap:55px}.lg\:gap-y-dynamic-56{row-gap:56px}.lg\:gap-y-dynamic-57{row-gap:57px}.lg\:gap-y-dynamic-58{row-gap:58px}.lg\:gap-y-dynamic-59{row-gap:59px}.lg\:gap-y-dynamic-6{row-gap:6px}.lg\:gap-y-dynamic-60{row-gap:60px}.lg\:gap-y-dynamic-61{row-gap:61px}.lg\:gap-y-dynamic-62{row-gap:62px}.lg\:gap-y-dynamic-63{row-gap:63px}.lg\:gap-y-dynamic-64{row-gap:64px}.lg\:gap-y-dynamic-65{row-gap:65px}.lg\:gap-y-dynamic-66{row-gap:66px}.lg\:gap-y-dynamic-67{row-gap:67px}.lg\:gap-y-dynamic-68{row-gap:68px}.lg\:gap-y-dynamic-69{row-gap:69px}.lg\:gap-y-dynamic-7{row-gap:7px}.lg\:gap-y-dynamic-70{row-gap:70px}.lg\:gap-y-dynamic-71{row-gap:71px}.lg\:gap-y-dynamic-72{row-gap:72px}.lg\:gap-y-dynamic-73{row-gap:73px}.lg\:gap-y-dynamic-74{row-gap:74px}.lg\:gap-y-dynamic-75{row-gap:75px}.lg\:gap-y-dynamic-76{row-gap:76px}.lg\:gap-y-dynamic-77{row-gap:77px}.lg\:gap-y-dynamic-78{row-gap:78px}.lg\:gap-y-dynamic-79{row-gap:79px}.lg\:gap-y-dynamic-8{row-gap:8px}.lg\:gap-y-dynamic-80{row-gap:80px}.lg\:gap-y-dynamic-81{row-gap:81px}.lg\:gap-y-dynamic-82{row-gap:82px}.lg\:gap-y-dynamic-83{row-gap:83px}.lg\:gap-y-dynamic-84{row-gap:84px}.lg\:gap-y-dynamic-85{row-gap:85px}.lg\:gap-y-dynamic-86{row-gap:86px}.lg\:gap-y-dynamic-87{row-gap:87px}.lg\:gap-y-dynamic-88{row-gap:88px}.lg\:gap-y-dynamic-89{row-gap:89px}.lg\:gap-y-dynamic-9{row-gap:9px}.lg\:gap-y-dynamic-90{row-gap:90px}.lg\:gap-y-dynamic-91{row-gap:91px}.lg\:gap-y-dynamic-92{row-gap:92px}.lg\:gap-y-dynamic-93{row-gap:93px}.lg\:gap-y-dynamic-94{row-gap:94px}.lg\:gap-y-dynamic-95{row-gap:95px}.lg\:gap-y-dynamic-96{row-gap:96px}.lg\:gap-y-dynamic-97{row-gap:97px}.lg\:gap-y-dynamic-98{row-gap:98px}.lg\:gap-y-dynamic-99{row-gap:99px}.lg\:rounded-\[16px\]{border-radius:16px}.lg\:rounded-\[24px\]{border-radius:24px}.lg\:border-x-4{border-left-width:4px;border-right-width:4px}.lg\:border-t-4{border-top-width:4px}.lg\:border-\[\#69B1FF3D\]{border-color:#69b1ff3d}.lg\:bg-\[\#ffffff14\]{background-color:#ffffff14}.lg\:bg-gradient-to-b{background-image:linear-gradient(to bottom,var(--tw-gradient-stops))}.lg\:from-\[\#272450\]{--tw-gradient-from:#272450 var(--tw-gradient-from-position);--tw-gradient-to:rgb(39 36 80/0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from),var(--tw-gradient-to)}.lg\:to-\[\#27245000\]{--tw-gradient-to:#27245000 var(--tw-gradient-to-position)}.lg\:p-4{padding:1rem}.lg\:p-\[24px\]{padding:24px}.lg\:p-\[60px\]{padding:60px}.lg\:px-6{padding-left:1.5rem;padding-right:1.5rem}.lg\:px-\[16px\]{padding-left:16px;padding-right:16px}.lg\:px-\[24px\]{padding-left:24px;padding-right:24px}.lg\:px-dynamic-1{padding-left:1px;padding-right:1px}.lg\:px-dynamic-10{padding-left:10px;padding-right:10px}.lg\:px-dynamic-100{padding-left:100px;padding-right:100px}.lg\:px-dynamic-11{padding-left:11px;padding-right:11px}.lg\:px-dynamic-12{padding-left:12px;padding-right:12px}.lg\:px-dynamic-13{padding-left:13px;padding-right:13px}.lg\:px-dynamic-14{padding-left:14px;padding-right:14px}.lg\:px-dynamic-15{padding-left:15px;padding-right:15px}.lg\:px-dynamic-16{padding-left:16px;padding-right:16px}.lg\:px-dynamic-17{padding-left:17px;padding-right:17px}.lg\:px-dynamic-18{padding-left:18px;padding-right:18px}.lg\:px-dynamic-19{padding-left:19px;padding-right:19px}.lg\:px-dynamic-2{padding-left:2px;padding-right:2px}.lg\:px-dynamic-20{padding-left:20px;padding-right:20px}.lg\:px-dynamic-21{padding-left:21px;padding-right:21px}.lg\:px-dynamic-22{padding-left:22px;padding-right:22px}.lg\:px-dynamic-23{padding-left:23px;padding-right:23px}.lg\:px-dynamic-24{padding-left:24px;padding-right:24px}.lg\:px-dynamic-25{padding-left:25px;padding-right:25px}.lg\:px-dynamic-26{padding-left:26px;padding-right:26px}.lg\:px-dynamic-27{padding-left:27px;padding-right:27px}.lg\:px-dynamic-28{padding-left:28px;padding-right:28px}.lg\:px-dynamic-29{padding-left:29px;padding-right:29px}.lg\:px-dynamic-3{padding-left:3px;padding-right:3px}.lg\:px-dynamic-30{padding-left:30px;padding-right:30px}.lg\:px-dynamic-31{padding-left:31px;padding-right:31px}.lg\:px-dynamic-32{padding-left:32px;padding-right:32px}.lg\:px-dynamic-33{padding-left:33px;padding-right:33px}.lg\:px-dynamic-34{padding-left:34px;padding-right:34px}.lg\:px-dynamic-35{padding-left:35px;padding-right:35px}.lg\:px-dynamic-36{padding-left:36px;padding-right:36px}.lg\:px-dynamic-37{padding-left:37px;padding-right:37px}.lg\:px-dynamic-38{padding-left:38px;padding-right:38px}.lg\:px-dynamic-39{padding-left:39px;padding-right:39px}.lg\:px-dynamic-4{padding-left:4px;padding-right:4px}.lg\:px-dynamic-40{padding-left:40px;padding-right:40px}.lg\:px-dynamic-41{padding-left:41px;padding-right:41px}.lg\:px-dynamic-42{padding-left:42px;padding-right:42px}.lg\:px-dynamic-43{padding-left:43px;padding-right:43px}.lg\:px-dynamic-44{padding-left:44px;padding-right:44px}.lg\:px-dynamic-45{padding-left:45px;padding-right:45px}.lg\:px-dynamic-46{padding-left:46px;padding-right:46px}.lg\:px-dynamic-47{padding-left:47px;padding-right:47px}.lg\:px-dynamic-48{padding-left:48px;padding-right:48px}.lg\:px-dynamic-49{padding-left:49px;padding-right:49px}.lg\:px-dynamic-5{padding-left:5px;padding-right:5px}.lg\:px-dynamic-50{padding-left:50px;padding-right:50px}.lg\:px-dynamic-51{padding-left:51px;padding-right:51px}.lg\:px-dynamic-52{padding-left:52px;padding-right:52px}.lg\:px-dynamic-53{padding-left:53px;padding-right:53px}.lg\:px-dynamic-54{padding-left:54px;padding-right:54px}.lg\:px-dynamic-55{padding-left:55px;padding-right:55px}.lg\:px-dynamic-56{padding-left:56px;padding-right:56px}.lg\:px-dynamic-57{padding-left:57px;padding-right:57px}.lg\:px-dynamic-58{padding-left:58px;padding-right:58px}.lg\:px-dynamic-59{padding-left:59px;padding-right:59px}.lg\:px-dynamic-6{padding-left:6px;padding-right:6px}.lg\:px-dynamic-60{padding-left:60px;padding-right:60px}.lg\:px-dynamic-61{padding-left:61px;padding-right:61px}.lg\:px-dynamic-62{padding-left:62px;padding-right:62px}.lg\:px-dynamic-63{padding-left:63px;padding-right:63px}.lg\:px-dynamic-64{padding-left:64px;padding-right:64px}.lg\:px-dynamic-65{padding-left:65px;padding-right:65px}.lg\:px-dynamic-66{padding-left:66px;padding-right:66px}.lg\:px-dynamic-67{padding-left:67px;padding-right:67px}.lg\:px-dynamic-68{padding-left:68px;padding-right:68px}.lg\:px-dynamic-69{padding-left:69px;padding-right:69px}.lg\:px-dynamic-7{padding-left:7px;padding-right:7px}.lg\:px-dynamic-70{padding-left:70px;padding-right:70px}.lg\:px-dynamic-71{padding-left:71px;padding-right:71px}.lg\:px-dynamic-72{padding-left:72px;padding-right:72px}.lg\:px-dynamic-73{padding-left:73px;padding-right:73px}.lg\:px-dynamic-74{padding-left:74px;padding-right:74px}.lg\:px-dynamic-75{padding-left:75px;padding-right:75px}.lg\:px-dynamic-76{padding-left:76px;padding-right:76px}.lg\:px-dynamic-77{padding-left:77px;padding-right:77px}.lg\:px-dynamic-78{padding-left:78px;padding-right:78px}.lg\:px-dynamic-79{padding-left:79px;padding-right:79px}.lg\:px-dynamic-8{padding-left:8px;padding-right:8px}.lg\:px-dynamic-80{padding-left:80px;padding-right:80px}.lg\:px-dynamic-81{padding-left:81px;padding-right:81px}.lg\:px-dynamic-82{padding-left:82px;padding-right:82px}.lg\:px-dynamic-83{padding-left:83px;padding-right:83px}.lg\:px-dynamic-84{padding-left:84px;padding-right:84px}.lg\:px-dynamic-85{padding-left:85px;padding-right:85px}.lg\:px-dynamic-86{padding-left:86px;padding-right:86px}.lg\:px-dynamic-87{padding-left:87px;padding-right:87px}.lg\:px-dynamic-88{padding-left:88px;padding-right:88px}.lg\:px-dynamic-89{padding-left:89px;padding-right:89px}.lg\:px-dynamic-9{padding-left:9px;padding-right:9px}.lg\:px-dynamic-90{padding-left:90px;padding-right:90px}.lg\:px-dynamic-91{padding-left:91px;padding-right:91px}.lg\:px-dynamic-92{padding-left:92px;padding-right:92px}.lg\:px-dynamic-93{padding-left:93px;padding-right:93px}.lg\:px-dynamic-94{padding-left:94px;padding-right:94px}.lg\:px-dynamic-95{padding-left:95px;padding-right:95px}.lg\:px-dynamic-96{padding-left:96px;padding-right:96px}.lg\:px-dynamic-97{padding-left:97px;padding-right:97px}.lg\:px-dynamic-98{padding-left:98px;padding-right:98px}.lg\:px-dynamic-99{padding-left:99px;padding-right:99px}.lg\:py-5{padding-bottom:1.25rem;padding-top:1.25rem}.lg\:py-\[20px\]{padding-bottom:20px;padding-top:20px}.lg\:pr-\[16px\]{padding-right:16px}.lg\:pr-\[40px\]{padding-right:40px}.lg\:pt-\[16px\]{padding-top:16px}.lg\:text-\[14px\]{font-size:14px}.lg\:text-\[16px\]{font-size:16px}.lg\:text-\[20px\]{font-size:20px}.lg\:text-\[24px\]{font-size:24px}.lg\:text-lg{font-size:1.125rem;line-height:1.75rem}.lg\:leading-\[15px\]{line-height:15px}.lg\:leading-\[22px\]{line-height:22px}.lg\:leading-\[28px\]{line-height:28px}.lg\:leading-\[34px\]{line-height:34px}.lg\:leading-\[none\]{line-height:none}.lg\:text-white{--tw-text-opacity:1;color:rgb(255 255 255/var(--tw-text-opacity,1))}.lg\:opacity-0{opacity:0}.lg\:hover\:bg-\[\#3463DB\]:hover{--tw-bg-opacity:1;background-color:rgb(52 99 219/var(--tw-bg-opacity,1))}}
@media not all and (min-width:1024px){.max-lg\:fixed{position:fixed}.max-lg\:inset-0{inset:0}.max-lg\:bottom-0{bottom:0}.max-lg\:left-0{left:0}.max-lg\:right-0{right:0}.max-lg\:col-span-3{grid-column:span 3 / span 3}.max-lg\:\!my-\[24px\]{margin-bottom:24px!important;margin-top:24px!important}.max-lg\:-mx-\[24px\]{margin-left:-24px;margin-right:-24px}.max-lg\:-mx-\[8px\]{margin-left:-8px;margin-right:-8px}.max-lg\:mx-auto{margin-left:auto;margin-right:auto}.max-lg\:my-\[24px\]{margin-bottom:24px;margin-top:24px}.max-lg\:\!mb-\[48px\]{margin-bottom:48px!important}.max-lg\:\!mt-\[24px\]{margin-top:24px!important}.max-lg\:mb-0{margin-bottom:0}.max-lg\:mb-\[55px\]{margin-bottom:55px}.max-lg\:mt-6{margin-top:1.5rem}.max-lg\:mt-\[12px\]{margin-top:12px}.max-lg\:mt-\[24px\]{margin-top:24px}.max-lg\:mt-\[36px\]{margin-top:36px}.max-lg\:hidden{display:none}.max-lg\:h-\[116px\]{height:116px}.max-lg\:h-\[48px\]{height:48px}.max-lg\:w-\[310px\]{width:310px}.max-lg\:w-full{width:100%}.max-lg\:max-w-\[714px\]{max-width:714px}.max-lg\:translate-y-0{--tw-translate-y:0px}.max-lg\:translate-y-0,.max-lg\:translate-y-\[266px\]{transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.max-lg\:translate-y-\[266px\]{--tw-translate-y:266px}.max-lg\:flex-col{flex-direction:column}.max-lg\:items-start{align-items:flex-start}.max-lg\:justify-between{justify-content:space-between}.max-lg\:gap-2{gap:.5rem}.max-lg\:gap-\[16px\]{gap:16px}.max-lg\:gap-y-\[12px\]{row-gap:12px}.max-lg\:rounded-\[12px\]{border-radius:12px}.max-lg\:rounded-none{border-radius:0}.max-lg\:border-t{border-top-width:1px}.max-lg\:border-none{border-style:none}.max-lg\:border-t-\[\#FFFFFF1F\]{border-top-color:#ffffff1f}.max-lg\:bg-\[\#********\]{background-color:#********}.max-lg\:\!px-\[16px\]{padding-left:16px!important;padding-right:16px!important}.max-lg\:pt-6{padding-top:1.5rem}.max-lg\:text-\[14px\]{font-size:14px}.max-lg\:leading-\[17px\]{line-height:17px}.max-lg\:hover\:bg-\[\#5081FF\]:hover{--tw-bg-opacity:1;background-color:rgb(80 129 255/var(--tw-bg-opacity,1))}.md\:max-lg\:text-\[16px\]{font-size:16px}.md\:max-lg\:leading-\[22px\]{line-height:22px}}
@media (min-width:1025px){
    .account-title-truncated{
      max-width:280px;
    }}
@media (min-width:1280px){.\!container{max-width:1280px !important;}.container{max-width:1280px;}
    .gaming-thumbnail{
      box-sizing:border-box !important;
      display:block !important;
      min-height:72px !important;
      min-width:96px !important;
      width:96px !important;
    }

    .gaming-thumbnail,.gaming-thumbnail img{
      margin:0 !important;
      padding:0 !important;
    }

    .gaming-thumbnail img{
      height:100% !important;
      left:0 !important;
      -o-object-fit:cover !important;
         object-fit:cover !important;
      -o-object-position:center !important;
         object-position:center !important;
      position:absolute !important;
      top:0 !important;
      width:100% !important;
    }

    .gaming-thumbnail-container{
      gap:1.25rem;
    }

    .xl\:h-12{height:3rem;}

    .xl\:max-h-\[170px\]{max-height:170px;}

    .xl\:w-96{width:24rem;}

    .xl\:min-w-\[200px\]{min-width:200px;}

    .xl\:max-w-\[320px\]{max-width:320px;}

    .xl\:grid-cols-3{grid-template-columns:repeat(3, minmax(0, 1fr));}

    .xl\:grid-cols-4{grid-template-columns:repeat(4, minmax(0, 1fr));}

    .xl\:gap-5{gap:1.25rem;}

    .xl\:gap-6{gap:1.5rem;}

    .xl\:px-20{padding-left:5rem;padding-right:5rem;}

    .xl\:px-40{padding-left:10rem;padding-right:10rem;}

    .xl\:text-4xl{font-size:2.25rem;line-height:2.5rem;}}
@media (min-width:1280px){.xl\:-mx-dynamic-1{margin-left:-1px;margin-right:-1px}.xl\:-mx-dynamic-10{margin-left:-10px;margin-right:-10px}.xl\:-mx-dynamic-100{margin-left:-100px;margin-right:-100px}.xl\:-mx-dynamic-11{margin-left:-11px;margin-right:-11px}.xl\:-mx-dynamic-12{margin-left:-12px;margin-right:-12px}.xl\:-mx-dynamic-13{margin-left:-13px;margin-right:-13px}.xl\:-mx-dynamic-14{margin-left:-14px;margin-right:-14px}.xl\:-mx-dynamic-15{margin-left:-15px;margin-right:-15px}.xl\:-mx-dynamic-16{margin-left:-16px;margin-right:-16px}.xl\:-mx-dynamic-17{margin-left:-17px;margin-right:-17px}.xl\:-mx-dynamic-18{margin-left:-18px;margin-right:-18px}.xl\:-mx-dynamic-19{margin-left:-19px;margin-right:-19px}.xl\:-mx-dynamic-2{margin-left:-2px;margin-right:-2px}.xl\:-mx-dynamic-20{margin-left:-20px;margin-right:-20px}.xl\:-mx-dynamic-21{margin-left:-21px;margin-right:-21px}.xl\:-mx-dynamic-22{margin-left:-22px;margin-right:-22px}.xl\:-mx-dynamic-23{margin-left:-23px;margin-right:-23px}.xl\:-mx-dynamic-24{margin-left:-24px;margin-right:-24px}.xl\:-mx-dynamic-25{margin-left:-25px;margin-right:-25px}.xl\:-mx-dynamic-26{margin-left:-26px;margin-right:-26px}.xl\:-mx-dynamic-27{margin-left:-27px;margin-right:-27px}.xl\:-mx-dynamic-28{margin-left:-28px;margin-right:-28px}.xl\:-mx-dynamic-29{margin-left:-29px;margin-right:-29px}.xl\:-mx-dynamic-3{margin-left:-3px;margin-right:-3px}.xl\:-mx-dynamic-30{margin-left:-30px;margin-right:-30px}.xl\:-mx-dynamic-31{margin-left:-31px;margin-right:-31px}.xl\:-mx-dynamic-32{margin-left:-32px;margin-right:-32px}.xl\:-mx-dynamic-33{margin-left:-33px;margin-right:-33px}.xl\:-mx-dynamic-34{margin-left:-34px;margin-right:-34px}.xl\:-mx-dynamic-35{margin-left:-35px;margin-right:-35px}.xl\:-mx-dynamic-36{margin-left:-36px;margin-right:-36px}.xl\:-mx-dynamic-37{margin-left:-37px;margin-right:-37px}.xl\:-mx-dynamic-38{margin-left:-38px;margin-right:-38px}.xl\:-mx-dynamic-39{margin-left:-39px;margin-right:-39px}.xl\:-mx-dynamic-4{margin-left:-4px;margin-right:-4px}.xl\:-mx-dynamic-40{margin-left:-40px;margin-right:-40px}.xl\:-mx-dynamic-41{margin-left:-41px;margin-right:-41px}.xl\:-mx-dynamic-42{margin-left:-42px;margin-right:-42px}.xl\:-mx-dynamic-43{margin-left:-43px;margin-right:-43px}.xl\:-mx-dynamic-44{margin-left:-44px;margin-right:-44px}.xl\:-mx-dynamic-45{margin-left:-45px;margin-right:-45px}.xl\:-mx-dynamic-46{margin-left:-46px;margin-right:-46px}.xl\:-mx-dynamic-47{margin-left:-47px;margin-right:-47px}.xl\:-mx-dynamic-48{margin-left:-48px;margin-right:-48px}.xl\:-mx-dynamic-49{margin-left:-49px;margin-right:-49px}.xl\:-mx-dynamic-5{margin-left:-5px;margin-right:-5px}.xl\:-mx-dynamic-50{margin-left:-50px;margin-right:-50px}.xl\:-mx-dynamic-51{margin-left:-51px;margin-right:-51px}.xl\:-mx-dynamic-52{margin-left:-52px;margin-right:-52px}.xl\:-mx-dynamic-53{margin-left:-53px;margin-right:-53px}.xl\:-mx-dynamic-54{margin-left:-54px;margin-right:-54px}.xl\:-mx-dynamic-55{margin-left:-55px;margin-right:-55px}.xl\:-mx-dynamic-56{margin-left:-56px;margin-right:-56px}.xl\:-mx-dynamic-57{margin-left:-57px;margin-right:-57px}.xl\:-mx-dynamic-58{margin-left:-58px;margin-right:-58px}.xl\:-mx-dynamic-59{margin-left:-59px;margin-right:-59px}.xl\:-mx-dynamic-6{margin-left:-6px;margin-right:-6px}.xl\:-mx-dynamic-60{margin-left:-60px;margin-right:-60px}.xl\:-mx-dynamic-61{margin-left:-61px;margin-right:-61px}.xl\:-mx-dynamic-62{margin-left:-62px;margin-right:-62px}.xl\:-mx-dynamic-63{margin-left:-63px;margin-right:-63px}.xl\:-mx-dynamic-64{margin-left:-64px;margin-right:-64px}.xl\:-mx-dynamic-65{margin-left:-65px;margin-right:-65px}.xl\:-mx-dynamic-66{margin-left:-66px;margin-right:-66px}.xl\:-mx-dynamic-67{margin-left:-67px;margin-right:-67px}.xl\:-mx-dynamic-68{margin-left:-68px;margin-right:-68px}.xl\:-mx-dynamic-69{margin-left:-69px;margin-right:-69px}.xl\:-mx-dynamic-7{margin-left:-7px;margin-right:-7px}.xl\:-mx-dynamic-70{margin-left:-70px;margin-right:-70px}.xl\:-mx-dynamic-71{margin-left:-71px;margin-right:-71px}.xl\:-mx-dynamic-72{margin-left:-72px;margin-right:-72px}.xl\:-mx-dynamic-73{margin-left:-73px;margin-right:-73px}.xl\:-mx-dynamic-74{margin-left:-74px;margin-right:-74px}.xl\:-mx-dynamic-75{margin-left:-75px;margin-right:-75px}.xl\:-mx-dynamic-76{margin-left:-76px;margin-right:-76px}.xl\:-mx-dynamic-77{margin-left:-77px;margin-right:-77px}.xl\:-mx-dynamic-78{margin-left:-78px;margin-right:-78px}.xl\:-mx-dynamic-79{margin-left:-79px;margin-right:-79px}.xl\:-mx-dynamic-8{margin-left:-8px;margin-right:-8px}.xl\:-mx-dynamic-80{margin-left:-80px;margin-right:-80px}.xl\:-mx-dynamic-81{margin-left:-81px;margin-right:-81px}.xl\:-mx-dynamic-82{margin-left:-82px;margin-right:-82px}.xl\:-mx-dynamic-83{margin-left:-83px;margin-right:-83px}.xl\:-mx-dynamic-84{margin-left:-84px;margin-right:-84px}.xl\:-mx-dynamic-85{margin-left:-85px;margin-right:-85px}.xl\:-mx-dynamic-86{margin-left:-86px;margin-right:-86px}.xl\:-mx-dynamic-87{margin-left:-87px;margin-right:-87px}.xl\:-mx-dynamic-88{margin-left:-88px;margin-right:-88px}.xl\:-mx-dynamic-89{margin-left:-89px;margin-right:-89px}.xl\:-mx-dynamic-9{margin-left:-9px;margin-right:-9px}.xl\:-mx-dynamic-90{margin-left:-90px;margin-right:-90px}.xl\:-mx-dynamic-91{margin-left:-91px;margin-right:-91px}.xl\:-mx-dynamic-92{margin-left:-92px;margin-right:-92px}.xl\:-mx-dynamic-93{margin-left:-93px;margin-right:-93px}.xl\:-mx-dynamic-94{margin-left:-94px;margin-right:-94px}.xl\:-mx-dynamic-95{margin-left:-95px;margin-right:-95px}.xl\:-mx-dynamic-96{margin-left:-96px;margin-right:-96px}.xl\:-mx-dynamic-97{margin-left:-97px;margin-right:-97px}.xl\:-mx-dynamic-98{margin-left:-98px;margin-right:-98px}.xl\:-mx-dynamic-99{margin-left:-99px;margin-right:-99px}.xl\:ms-1-24{margin-inline-start:4.166666666666666%}.xl\:ms-10-24{margin-inline-start:41.66666666666667%}.xl\:ms-11-24{margin-inline-start:45.83333333333333%}.xl\:ms-12-24{margin-inline-start:50%}.xl\:ms-13-24{margin-inline-start:54.166666666666664%}.xl\:ms-14-24{margin-inline-start:58.333333333333336%}.xl\:ms-15-24{margin-inline-start:62.5%}.xl\:ms-16-24{margin-inline-start:66.66666666666666%}.xl\:ms-17-24{margin-inline-start:70.83333333333334%}.xl\:ms-18-24{margin-inline-start:75%}.xl\:ms-19-24{margin-inline-start:79.16666666666666%}.xl\:ms-2-24{margin-inline-start:8.333333333333332%}.xl\:ms-20-24{margin-inline-start:83.33333333333334%}.xl\:ms-21-24{margin-inline-start:87.5%}.xl\:ms-22-24{margin-inline-start:91.66666666666666%}.xl\:ms-23-24{margin-inline-start:95.83333333333334%}.xl\:ms-24-24{margin-inline-start:100%}.xl\:ms-3-24{margin-inline-start:12.5%}.xl\:ms-4-24{margin-inline-start:16.666666666666664%}.xl\:ms-5-24{margin-inline-start:20.833333333333336%}.xl\:ms-6-24{margin-inline-start:25%}.xl\:ms-7-24{margin-inline-start:29.166666666666668%}.xl\:ms-8-24{margin-inline-start:33.33333333333333%}.xl\:ms-9-24{margin-inline-start:37.5%}.xl\:flex{display:flex}.xl\:\!hidden{display:none!important}.xl\:hidden{display:none}.xl\:w-1-24{width:4.166666666666666%}.xl\:w-10-24{width:41.66666666666667%}.xl\:w-11-24{width:45.83333333333333%}.xl\:w-12-24{width:50%}.xl\:w-13-24{width:54.166666666666664%}.xl\:w-14-24{width:58.333333333333336%}.xl\:w-15-24{width:62.5%}.xl\:w-16-24{width:66.66666666666666%}.xl\:w-17-24{width:70.83333333333334%}.xl\:w-18-24{width:75%}.xl\:w-19-24{width:79.16666666666666%}.xl\:w-2-24{width:8.333333333333332%}.xl\:w-20-24{width:83.33333333333334%}.xl\:w-21-24{width:87.5%}.xl\:w-22-24{width:91.66666666666666%}.xl\:w-23-24{width:95.83333333333334%}.xl\:w-24-24{width:100%}.xl\:w-3-24{width:12.5%}.xl\:w-4-24{width:16.666666666666664%}.xl\:w-5-24{width:20.833333333333336%}.xl\:w-6-24{width:25%}.xl\:w-7-24{width:29.166666666666668%}.xl\:w-8-24{width:33.33333333333333%}.xl\:w-9-24{width:37.5%}.xl\:w-\[276px\]{width:276px}.xl\:w-col-1{width:100%}.xl\:w-col-10{width:10%}.xl\:w-col-11{width:9.090909090909092%}.xl\:w-col-12{width:8.333333333333334%}.xl\:w-col-13{width:7.6923076923076925%}.xl\:w-col-14{width:7.142857142857143%}.xl\:w-col-15{width:6.666666666666667%}.xl\:w-col-16{width:6.25%}.xl\:w-col-17{width:5.882352941176471%}.xl\:w-col-18{width:5.555555555555555%}.xl\:w-col-19{width:5.2631578947368425%}.xl\:w-col-2{width:50%}.xl\:w-col-20{width:5%}.xl\:w-col-21{width:4.761904761904762%}.xl\:w-col-22{width:4.545454545454546%}.xl\:w-col-23{width:4.3478260869565215%}.xl\:w-col-24{width:4.166666666666667%}.xl\:w-col-3{width:33.333333333333336%}.xl\:w-col-4{width:25%}.xl\:w-col-5{width:20%}.xl\:w-col-6{width:16.666666666666668%}.xl\:w-col-7{width:14.285714285714286%}.xl\:w-col-8{width:12.5%}.xl\:w-col-9{width:11.11111111111111%}.xl\:items-start{align-items:flex-start}.xl\:items-end{align-items:flex-end}.xl\:items-center{align-items:center}.xl\:items-stretch{align-items:stretch}.xl\:justify-start{justify-content:flex-start}.xl\:justify-end{justify-content:flex-end}.xl\:justify-center{justify-content:center}.xl\:justify-between{justify-content:space-between}.xl\:justify-around{justify-content:space-around}.xl\:justify-evenly{justify-content:space-evenly}.xl\:gap-y-dynamic-1{row-gap:1px}.xl\:gap-y-dynamic-10{row-gap:10px}.xl\:gap-y-dynamic-100{row-gap:100px}.xl\:gap-y-dynamic-11{row-gap:11px}.xl\:gap-y-dynamic-12{row-gap:12px}.xl\:gap-y-dynamic-13{row-gap:13px}.xl\:gap-y-dynamic-14{row-gap:14px}.xl\:gap-y-dynamic-15{row-gap:15px}.xl\:gap-y-dynamic-16{row-gap:16px}.xl\:gap-y-dynamic-17{row-gap:17px}.xl\:gap-y-dynamic-18{row-gap:18px}.xl\:gap-y-dynamic-19{row-gap:19px}.xl\:gap-y-dynamic-2{row-gap:2px}.xl\:gap-y-dynamic-20{row-gap:20px}.xl\:gap-y-dynamic-21{row-gap:21px}.xl\:gap-y-dynamic-22{row-gap:22px}.xl\:gap-y-dynamic-23{row-gap:23px}.xl\:gap-y-dynamic-24{row-gap:24px}.xl\:gap-y-dynamic-25{row-gap:25px}.xl\:gap-y-dynamic-26{row-gap:26px}.xl\:gap-y-dynamic-27{row-gap:27px}.xl\:gap-y-dynamic-28{row-gap:28px}.xl\:gap-y-dynamic-29{row-gap:29px}.xl\:gap-y-dynamic-3{row-gap:3px}.xl\:gap-y-dynamic-30{row-gap:30px}.xl\:gap-y-dynamic-31{row-gap:31px}.xl\:gap-y-dynamic-32{row-gap:32px}.xl\:gap-y-dynamic-33{row-gap:33px}.xl\:gap-y-dynamic-34{row-gap:34px}.xl\:gap-y-dynamic-35{row-gap:35px}.xl\:gap-y-dynamic-36{row-gap:36px}.xl\:gap-y-dynamic-37{row-gap:37px}.xl\:gap-y-dynamic-38{row-gap:38px}.xl\:gap-y-dynamic-39{row-gap:39px}.xl\:gap-y-dynamic-4{row-gap:4px}.xl\:gap-y-dynamic-40{row-gap:40px}.xl\:gap-y-dynamic-41{row-gap:41px}.xl\:gap-y-dynamic-42{row-gap:42px}.xl\:gap-y-dynamic-43{row-gap:43px}.xl\:gap-y-dynamic-44{row-gap:44px}.xl\:gap-y-dynamic-45{row-gap:45px}.xl\:gap-y-dynamic-46{row-gap:46px}.xl\:gap-y-dynamic-47{row-gap:47px}.xl\:gap-y-dynamic-48{row-gap:48px}.xl\:gap-y-dynamic-49{row-gap:49px}.xl\:gap-y-dynamic-5{row-gap:5px}.xl\:gap-y-dynamic-50{row-gap:50px}.xl\:gap-y-dynamic-51{row-gap:51px}.xl\:gap-y-dynamic-52{row-gap:52px}.xl\:gap-y-dynamic-53{row-gap:53px}.xl\:gap-y-dynamic-54{row-gap:54px}.xl\:gap-y-dynamic-55{row-gap:55px}.xl\:gap-y-dynamic-56{row-gap:56px}.xl\:gap-y-dynamic-57{row-gap:57px}.xl\:gap-y-dynamic-58{row-gap:58px}.xl\:gap-y-dynamic-59{row-gap:59px}.xl\:gap-y-dynamic-6{row-gap:6px}.xl\:gap-y-dynamic-60{row-gap:60px}.xl\:gap-y-dynamic-61{row-gap:61px}.xl\:gap-y-dynamic-62{row-gap:62px}.xl\:gap-y-dynamic-63{row-gap:63px}.xl\:gap-y-dynamic-64{row-gap:64px}.xl\:gap-y-dynamic-65{row-gap:65px}.xl\:gap-y-dynamic-66{row-gap:66px}.xl\:gap-y-dynamic-67{row-gap:67px}.xl\:gap-y-dynamic-68{row-gap:68px}.xl\:gap-y-dynamic-69{row-gap:69px}.xl\:gap-y-dynamic-7{row-gap:7px}.xl\:gap-y-dynamic-70{row-gap:70px}.xl\:gap-y-dynamic-71{row-gap:71px}.xl\:gap-y-dynamic-72{row-gap:72px}.xl\:gap-y-dynamic-73{row-gap:73px}.xl\:gap-y-dynamic-74{row-gap:74px}.xl\:gap-y-dynamic-75{row-gap:75px}.xl\:gap-y-dynamic-76{row-gap:76px}.xl\:gap-y-dynamic-77{row-gap:77px}.xl\:gap-y-dynamic-78{row-gap:78px}.xl\:gap-y-dynamic-79{row-gap:79px}.xl\:gap-y-dynamic-8{row-gap:8px}.xl\:gap-y-dynamic-80{row-gap:80px}.xl\:gap-y-dynamic-81{row-gap:81px}.xl\:gap-y-dynamic-82{row-gap:82px}.xl\:gap-y-dynamic-83{row-gap:83px}.xl\:gap-y-dynamic-84{row-gap:84px}.xl\:gap-y-dynamic-85{row-gap:85px}.xl\:gap-y-dynamic-86{row-gap:86px}.xl\:gap-y-dynamic-87{row-gap:87px}.xl\:gap-y-dynamic-88{row-gap:88px}.xl\:gap-y-dynamic-89{row-gap:89px}.xl\:gap-y-dynamic-9{row-gap:9px}.xl\:gap-y-dynamic-90{row-gap:90px}.xl\:gap-y-dynamic-91{row-gap:91px}.xl\:gap-y-dynamic-92{row-gap:92px}.xl\:gap-y-dynamic-93{row-gap:93px}.xl\:gap-y-dynamic-94{row-gap:94px}.xl\:gap-y-dynamic-95{row-gap:95px}.xl\:gap-y-dynamic-96{row-gap:96px}.xl\:gap-y-dynamic-97{row-gap:97px}.xl\:gap-y-dynamic-98{row-gap:98px}.xl\:gap-y-dynamic-99{row-gap:99px}.xl\:px-dynamic-1{padding-left:1px;padding-right:1px}.xl\:px-dynamic-10{padding-left:10px;padding-right:10px}.xl\:px-dynamic-100{padding-left:100px;padding-right:100px}.xl\:px-dynamic-11{padding-left:11px;padding-right:11px}.xl\:px-dynamic-12{padding-left:12px;padding-right:12px}.xl\:px-dynamic-13{padding-left:13px;padding-right:13px}.xl\:px-dynamic-14{padding-left:14px;padding-right:14px}.xl\:px-dynamic-15{padding-left:15px;padding-right:15px}.xl\:px-dynamic-16{padding-left:16px;padding-right:16px}.xl\:px-dynamic-17{padding-left:17px;padding-right:17px}.xl\:px-dynamic-18{padding-left:18px;padding-right:18px}.xl\:px-dynamic-19{padding-left:19px;padding-right:19px}.xl\:px-dynamic-2{padding-left:2px;padding-right:2px}.xl\:px-dynamic-20{padding-left:20px;padding-right:20px}.xl\:px-dynamic-21{padding-left:21px;padding-right:21px}.xl\:px-dynamic-22{padding-left:22px;padding-right:22px}.xl\:px-dynamic-23{padding-left:23px;padding-right:23px}.xl\:px-dynamic-24{padding-left:24px;padding-right:24px}.xl\:px-dynamic-25{padding-left:25px;padding-right:25px}.xl\:px-dynamic-26{padding-left:26px;padding-right:26px}.xl\:px-dynamic-27{padding-left:27px;padding-right:27px}.xl\:px-dynamic-28{padding-left:28px;padding-right:28px}.xl\:px-dynamic-29{padding-left:29px;padding-right:29px}.xl\:px-dynamic-3{padding-left:3px;padding-right:3px}.xl\:px-dynamic-30{padding-left:30px;padding-right:30px}.xl\:px-dynamic-31{padding-left:31px;padding-right:31px}.xl\:px-dynamic-32{padding-left:32px;padding-right:32px}.xl\:px-dynamic-33{padding-left:33px;padding-right:33px}.xl\:px-dynamic-34{padding-left:34px;padding-right:34px}.xl\:px-dynamic-35{padding-left:35px;padding-right:35px}.xl\:px-dynamic-36{padding-left:36px;padding-right:36px}.xl\:px-dynamic-37{padding-left:37px;padding-right:37px}.xl\:px-dynamic-38{padding-left:38px;padding-right:38px}.xl\:px-dynamic-39{padding-left:39px;padding-right:39px}.xl\:px-dynamic-4{padding-left:4px;padding-right:4px}.xl\:px-dynamic-40{padding-left:40px;padding-right:40px}.xl\:px-dynamic-41{padding-left:41px;padding-right:41px}.xl\:px-dynamic-42{padding-left:42px;padding-right:42px}.xl\:px-dynamic-43{padding-left:43px;padding-right:43px}.xl\:px-dynamic-44{padding-left:44px;padding-right:44px}.xl\:px-dynamic-45{padding-left:45px;padding-right:45px}.xl\:px-dynamic-46{padding-left:46px;padding-right:46px}.xl\:px-dynamic-47{padding-left:47px;padding-right:47px}.xl\:px-dynamic-48{padding-left:48px;padding-right:48px}.xl\:px-dynamic-49{padding-left:49px;padding-right:49px}.xl\:px-dynamic-5{padding-left:5px;padding-right:5px}.xl\:px-dynamic-50{padding-left:50px;padding-right:50px}.xl\:px-dynamic-51{padding-left:51px;padding-right:51px}.xl\:px-dynamic-52{padding-left:52px;padding-right:52px}.xl\:px-dynamic-53{padding-left:53px;padding-right:53px}.xl\:px-dynamic-54{padding-left:54px;padding-right:54px}.xl\:px-dynamic-55{padding-left:55px;padding-right:55px}.xl\:px-dynamic-56{padding-left:56px;padding-right:56px}.xl\:px-dynamic-57{padding-left:57px;padding-right:57px}.xl\:px-dynamic-58{padding-left:58px;padding-right:58px}.xl\:px-dynamic-59{padding-left:59px;padding-right:59px}.xl\:px-dynamic-6{padding-left:6px;padding-right:6px}.xl\:px-dynamic-60{padding-left:60px;padding-right:60px}.xl\:px-dynamic-61{padding-left:61px;padding-right:61px}.xl\:px-dynamic-62{padding-left:62px;padding-right:62px}.xl\:px-dynamic-63{padding-left:63px;padding-right:63px}.xl\:px-dynamic-64{padding-left:64px;padding-right:64px}.xl\:px-dynamic-65{padding-left:65px;padding-right:65px}.xl\:px-dynamic-66{padding-left:66px;padding-right:66px}.xl\:px-dynamic-67{padding-left:67px;padding-right:67px}.xl\:px-dynamic-68{padding-left:68px;padding-right:68px}.xl\:px-dynamic-69{padding-left:69px;padding-right:69px}.xl\:px-dynamic-7{padding-left:7px;padding-right:7px}.xl\:px-dynamic-70{padding-left:70px;padding-right:70px}.xl\:px-dynamic-71{padding-left:71px;padding-right:71px}.xl\:px-dynamic-72{padding-left:72px;padding-right:72px}.xl\:px-dynamic-73{padding-left:73px;padding-right:73px}.xl\:px-dynamic-74{padding-left:74px;padding-right:74px}.xl\:px-dynamic-75{padding-left:75px;padding-right:75px}.xl\:px-dynamic-76{padding-left:76px;padding-right:76px}.xl\:px-dynamic-77{padding-left:77px;padding-right:77px}.xl\:px-dynamic-78{padding-left:78px;padding-right:78px}.xl\:px-dynamic-79{padding-left:79px;padding-right:79px}.xl\:px-dynamic-8{padding-left:8px;padding-right:8px}.xl\:px-dynamic-80{padding-left:80px;padding-right:80px}.xl\:px-dynamic-81{padding-left:81px;padding-right:81px}.xl\:px-dynamic-82{padding-left:82px;padding-right:82px}.xl\:px-dynamic-83{padding-left:83px;padding-right:83px}.xl\:px-dynamic-84{padding-left:84px;padding-right:84px}.xl\:px-dynamic-85{padding-left:85px;padding-right:85px}.xl\:px-dynamic-86{padding-left:86px;padding-right:86px}.xl\:px-dynamic-87{padding-left:87px;padding-right:87px}.xl\:px-dynamic-88{padding-left:88px;padding-right:88px}.xl\:px-dynamic-89{padding-left:89px;padding-right:89px}.xl\:px-dynamic-9{padding-left:9px;padding-right:9px}.xl\:px-dynamic-90{padding-left:90px;padding-right:90px}.xl\:px-dynamic-91{padding-left:91px;padding-right:91px}.xl\:px-dynamic-92{padding-left:92px;padding-right:92px}.xl\:px-dynamic-93{padding-left:93px;padding-right:93px}.xl\:px-dynamic-94{padding-left:94px;padding-right:94px}.xl\:px-dynamic-95{padding-left:95px;padding-right:95px}.xl\:px-dynamic-96{padding-left:96px;padding-right:96px}.xl\:px-dynamic-97{padding-left:97px;padding-right:97px}.xl\:px-dynamic-98{padding-left:98px;padding-right:98px}.xl\:px-dynamic-99{padding-left:99px;padding-right:99px}}
@media not all and (min-width:1280px){.max-xl\:mt-12{margin-top:3rem}.max-xl\:mt-\[24px\]{margin-top:24px}.max-xl\:hidden{display:none}.max-xl\:space-y-\[24px\]>:not([hidden])~:not([hidden]){--tw-space-y-reverse:0;margin-bottom:calc(24px*var(--tw-space-y-reverse));margin-top:calc(24px*(1 - var(--tw-space-y-reverse)))}}
@media (min-width:1352px){.container{max-width:1352px}}
@media (min-width:1440px){
    .account-title-truncated{
      max-width:320px;
    }}
@media (min-width:1536px){.\!container{max-width:1536px !important;}.container{max-width:1536px;}.\32xl\:gap-5{gap:1.25rem;}}
@media (min-width:1536px){.\32xl\:-mx-dynamic-1{margin-left:-1px;margin-right:-1px}.\32xl\:-mx-dynamic-10{margin-left:-10px;margin-right:-10px}.\32xl\:-mx-dynamic-100{margin-left:-100px;margin-right:-100px}.\32xl\:-mx-dynamic-11{margin-left:-11px;margin-right:-11px}.\32xl\:-mx-dynamic-12{margin-left:-12px;margin-right:-12px}.\32xl\:-mx-dynamic-13{margin-left:-13px;margin-right:-13px}.\32xl\:-mx-dynamic-14{margin-left:-14px;margin-right:-14px}.\32xl\:-mx-dynamic-15{margin-left:-15px;margin-right:-15px}.\32xl\:-mx-dynamic-16{margin-left:-16px;margin-right:-16px}.\32xl\:-mx-dynamic-17{margin-left:-17px;margin-right:-17px}.\32xl\:-mx-dynamic-18{margin-left:-18px;margin-right:-18px}.\32xl\:-mx-dynamic-19{margin-left:-19px;margin-right:-19px}.\32xl\:-mx-dynamic-2{margin-left:-2px;margin-right:-2px}.\32xl\:-mx-dynamic-20{margin-left:-20px;margin-right:-20px}.\32xl\:-mx-dynamic-21{margin-left:-21px;margin-right:-21px}.\32xl\:-mx-dynamic-22{margin-left:-22px;margin-right:-22px}.\32xl\:-mx-dynamic-23{margin-left:-23px;margin-right:-23px}.\32xl\:-mx-dynamic-24{margin-left:-24px;margin-right:-24px}.\32xl\:-mx-dynamic-25{margin-left:-25px;margin-right:-25px}.\32xl\:-mx-dynamic-26{margin-left:-26px;margin-right:-26px}.\32xl\:-mx-dynamic-27{margin-left:-27px;margin-right:-27px}.\32xl\:-mx-dynamic-28{margin-left:-28px;margin-right:-28px}.\32xl\:-mx-dynamic-29{margin-left:-29px;margin-right:-29px}.\32xl\:-mx-dynamic-3{margin-left:-3px;margin-right:-3px}.\32xl\:-mx-dynamic-30{margin-left:-30px;margin-right:-30px}.\32xl\:-mx-dynamic-31{margin-left:-31px;margin-right:-31px}.\32xl\:-mx-dynamic-32{margin-left:-32px;margin-right:-32px}.\32xl\:-mx-dynamic-33{margin-left:-33px;margin-right:-33px}.\32xl\:-mx-dynamic-34{margin-left:-34px;margin-right:-34px}.\32xl\:-mx-dynamic-35{margin-left:-35px;margin-right:-35px}.\32xl\:-mx-dynamic-36{margin-left:-36px;margin-right:-36px}.\32xl\:-mx-dynamic-37{margin-left:-37px;margin-right:-37px}.\32xl\:-mx-dynamic-38{margin-left:-38px;margin-right:-38px}.\32xl\:-mx-dynamic-39{margin-left:-39px;margin-right:-39px}.\32xl\:-mx-dynamic-4{margin-left:-4px;margin-right:-4px}.\32xl\:-mx-dynamic-40{margin-left:-40px;margin-right:-40px}.\32xl\:-mx-dynamic-41{margin-left:-41px;margin-right:-41px}.\32xl\:-mx-dynamic-42{margin-left:-42px;margin-right:-42px}.\32xl\:-mx-dynamic-43{margin-left:-43px;margin-right:-43px}.\32xl\:-mx-dynamic-44{margin-left:-44px;margin-right:-44px}.\32xl\:-mx-dynamic-45{margin-left:-45px;margin-right:-45px}.\32xl\:-mx-dynamic-46{margin-left:-46px;margin-right:-46px}.\32xl\:-mx-dynamic-47{margin-left:-47px;margin-right:-47px}.\32xl\:-mx-dynamic-48{margin-left:-48px;margin-right:-48px}.\32xl\:-mx-dynamic-49{margin-left:-49px;margin-right:-49px}.\32xl\:-mx-dynamic-5{margin-left:-5px;margin-right:-5px}.\32xl\:-mx-dynamic-50{margin-left:-50px;margin-right:-50px}.\32xl\:-mx-dynamic-51{margin-left:-51px;margin-right:-51px}.\32xl\:-mx-dynamic-52{margin-left:-52px;margin-right:-52px}.\32xl\:-mx-dynamic-53{margin-left:-53px;margin-right:-53px}.\32xl\:-mx-dynamic-54{margin-left:-54px;margin-right:-54px}.\32xl\:-mx-dynamic-55{margin-left:-55px;margin-right:-55px}.\32xl\:-mx-dynamic-56{margin-left:-56px;margin-right:-56px}.\32xl\:-mx-dynamic-57{margin-left:-57px;margin-right:-57px}.\32xl\:-mx-dynamic-58{margin-left:-58px;margin-right:-58px}.\32xl\:-mx-dynamic-59{margin-left:-59px;margin-right:-59px}.\32xl\:-mx-dynamic-6{margin-left:-6px;margin-right:-6px}.\32xl\:-mx-dynamic-60{margin-left:-60px;margin-right:-60px}.\32xl\:-mx-dynamic-61{margin-left:-61px;margin-right:-61px}.\32xl\:-mx-dynamic-62{margin-left:-62px;margin-right:-62px}.\32xl\:-mx-dynamic-63{margin-left:-63px;margin-right:-63px}.\32xl\:-mx-dynamic-64{margin-left:-64px;margin-right:-64px}.\32xl\:-mx-dynamic-65{margin-left:-65px;margin-right:-65px}.\32xl\:-mx-dynamic-66{margin-left:-66px;margin-right:-66px}.\32xl\:-mx-dynamic-67{margin-left:-67px;margin-right:-67px}.\32xl\:-mx-dynamic-68{margin-left:-68px;margin-right:-68px}.\32xl\:-mx-dynamic-69{margin-left:-69px;margin-right:-69px}.\32xl\:-mx-dynamic-7{margin-left:-7px;margin-right:-7px}.\32xl\:-mx-dynamic-70{margin-left:-70px;margin-right:-70px}.\32xl\:-mx-dynamic-71{margin-left:-71px;margin-right:-71px}.\32xl\:-mx-dynamic-72{margin-left:-72px;margin-right:-72px}.\32xl\:-mx-dynamic-73{margin-left:-73px;margin-right:-73px}.\32xl\:-mx-dynamic-74{margin-left:-74px;margin-right:-74px}.\32xl\:-mx-dynamic-75{margin-left:-75px;margin-right:-75px}.\32xl\:-mx-dynamic-76{margin-left:-76px;margin-right:-76px}.\32xl\:-mx-dynamic-77{margin-left:-77px;margin-right:-77px}.\32xl\:-mx-dynamic-78{margin-left:-78px;margin-right:-78px}.\32xl\:-mx-dynamic-79{margin-left:-79px;margin-right:-79px}.\32xl\:-mx-dynamic-8{margin-left:-8px;margin-right:-8px}.\32xl\:-mx-dynamic-80{margin-left:-80px;margin-right:-80px}.\32xl\:-mx-dynamic-81{margin-left:-81px;margin-right:-81px}.\32xl\:-mx-dynamic-82{margin-left:-82px;margin-right:-82px}.\32xl\:-mx-dynamic-83{margin-left:-83px;margin-right:-83px}.\32xl\:-mx-dynamic-84{margin-left:-84px;margin-right:-84px}.\32xl\:-mx-dynamic-85{margin-left:-85px;margin-right:-85px}.\32xl\:-mx-dynamic-86{margin-left:-86px;margin-right:-86px}.\32xl\:-mx-dynamic-87{margin-left:-87px;margin-right:-87px}.\32xl\:-mx-dynamic-88{margin-left:-88px;margin-right:-88px}.\32xl\:-mx-dynamic-89{margin-left:-89px;margin-right:-89px}.\32xl\:-mx-dynamic-9{margin-left:-9px;margin-right:-9px}.\32xl\:-mx-dynamic-90{margin-left:-90px;margin-right:-90px}.\32xl\:-mx-dynamic-91{margin-left:-91px;margin-right:-91px}.\32xl\:-mx-dynamic-92{margin-left:-92px;margin-right:-92px}.\32xl\:-mx-dynamic-93{margin-left:-93px;margin-right:-93px}.\32xl\:-mx-dynamic-94{margin-left:-94px;margin-right:-94px}.\32xl\:-mx-dynamic-95{margin-left:-95px;margin-right:-95px}.\32xl\:-mx-dynamic-96{margin-left:-96px;margin-right:-96px}.\32xl\:-mx-dynamic-97{margin-left:-97px;margin-right:-97px}.\32xl\:-mx-dynamic-98{margin-left:-98px;margin-right:-98px}.\32xl\:-mx-dynamic-99{margin-left:-99px;margin-right:-99px}.\32xl\:ms-1-24{margin-inline-start:4.166666666666666%}.\32xl\:ms-10-24{margin-inline-start:41.66666666666667%}.\32xl\:ms-11-24{margin-inline-start:45.83333333333333%}.\32xl\:ms-12-24{margin-inline-start:50%}.\32xl\:ms-13-24{margin-inline-start:54.166666666666664%}.\32xl\:ms-14-24{margin-inline-start:58.333333333333336%}.\32xl\:ms-15-24{margin-inline-start:62.5%}.\32xl\:ms-16-24{margin-inline-start:66.66666666666666%}.\32xl\:ms-17-24{margin-inline-start:70.83333333333334%}.\32xl\:ms-18-24{margin-inline-start:75%}.\32xl\:ms-19-24{margin-inline-start:79.16666666666666%}.\32xl\:ms-2-24{margin-inline-start:8.333333333333332%}.\32xl\:ms-20-24{margin-inline-start:83.33333333333334%}.\32xl\:ms-21-24{margin-inline-start:87.5%}.\32xl\:ms-22-24{margin-inline-start:91.66666666666666%}.\32xl\:ms-23-24{margin-inline-start:95.83333333333334%}.\32xl\:ms-24-24{margin-inline-start:100%}.\32xl\:ms-3-24{margin-inline-start:12.5%}.\32xl\:ms-4-24{margin-inline-start:16.666666666666664%}.\32xl\:ms-5-24{margin-inline-start:20.833333333333336%}.\32xl\:ms-6-24{margin-inline-start:25%}.\32xl\:ms-7-24{margin-inline-start:29.166666666666668%}.\32xl\:ms-8-24{margin-inline-start:33.33333333333333%}.\32xl\:ms-9-24{margin-inline-start:37.5%}.\32xl\:w-1-24{width:4.166666666666666%}.\32xl\:w-10-24{width:41.66666666666667%}.\32xl\:w-11-24{width:45.83333333333333%}.\32xl\:w-12-24{width:50%}.\32xl\:w-13-24{width:54.166666666666664%}.\32xl\:w-14-24{width:58.333333333333336%}.\32xl\:w-15-24{width:62.5%}.\32xl\:w-16-24{width:66.66666666666666%}.\32xl\:w-17-24{width:70.83333333333334%}.\32xl\:w-18-24{width:75%}.\32xl\:w-19-24{width:79.16666666666666%}.\32xl\:w-2-24{width:8.333333333333332%}.\32xl\:w-20-24{width:83.33333333333334%}.\32xl\:w-21-24{width:87.5%}.\32xl\:w-22-24{width:91.66666666666666%}.\32xl\:w-23-24{width:95.83333333333334%}.\32xl\:w-24-24{width:100%}.\32xl\:w-3-24{width:12.5%}.\32xl\:w-4-24{width:16.666666666666664%}.\32xl\:w-5-24{width:20.833333333333336%}.\32xl\:w-6-24{width:25%}.\32xl\:w-7-24{width:29.166666666666668%}.\32xl\:w-8-24{width:33.33333333333333%}.\32xl\:w-9-24{width:37.5%}.\32xl\:w-col-1{width:100%}.\32xl\:w-col-10{width:10%}.\32xl\:w-col-11{width:9.090909090909092%}.\32xl\:w-col-12{width:8.333333333333334%}.\32xl\:w-col-13{width:7.6923076923076925%}.\32xl\:w-col-14{width:7.142857142857143%}.\32xl\:w-col-15{width:6.666666666666667%}.\32xl\:w-col-16{width:6.25%}.\32xl\:w-col-17{width:5.882352941176471%}.\32xl\:w-col-18{width:5.555555555555555%}.\32xl\:w-col-19{width:5.2631578947368425%}.\32xl\:w-col-2{width:50%}.\32xl\:w-col-20{width:5%}.\32xl\:w-col-21{width:4.761904761904762%}.\32xl\:w-col-22{width:4.545454545454546%}.\32xl\:w-col-23{width:4.3478260869565215%}.\32xl\:w-col-24{width:4.166666666666667%}.\32xl\:w-col-3{width:33.333333333333336%}.\32xl\:w-col-4{width:25%}.\32xl\:w-col-5{width:20%}.\32xl\:w-col-6{width:16.666666666666668%}.\32xl\:w-col-7{width:14.285714285714286%}.\32xl\:w-col-8{width:12.5%}.\32xl\:w-col-9{width:11.11111111111111%}.\32xl\:items-start{align-items:flex-start}.\32xl\:items-end{align-items:flex-end}.\32xl\:items-center{align-items:center}.\32xl\:items-stretch{align-items:stretch}.\32xl\:justify-start{justify-content:flex-start}.\32xl\:justify-end{justify-content:flex-end}.\32xl\:justify-center{justify-content:center}.\32xl\:justify-between{justify-content:space-between}.\32xl\:justify-around{justify-content:space-around}.\32xl\:justify-evenly{justify-content:space-evenly}.\32xl\:gap-y-dynamic-1{row-gap:1px}.\32xl\:gap-y-dynamic-10{row-gap:10px}.\32xl\:gap-y-dynamic-100{row-gap:100px}.\32xl\:gap-y-dynamic-11{row-gap:11px}.\32xl\:gap-y-dynamic-12{row-gap:12px}.\32xl\:gap-y-dynamic-13{row-gap:13px}.\32xl\:gap-y-dynamic-14{row-gap:14px}.\32xl\:gap-y-dynamic-15{row-gap:15px}.\32xl\:gap-y-dynamic-16{row-gap:16px}.\32xl\:gap-y-dynamic-17{row-gap:17px}.\32xl\:gap-y-dynamic-18{row-gap:18px}.\32xl\:gap-y-dynamic-19{row-gap:19px}.\32xl\:gap-y-dynamic-2{row-gap:2px}.\32xl\:gap-y-dynamic-20{row-gap:20px}.\32xl\:gap-y-dynamic-21{row-gap:21px}.\32xl\:gap-y-dynamic-22{row-gap:22px}.\32xl\:gap-y-dynamic-23{row-gap:23px}.\32xl\:gap-y-dynamic-24{row-gap:24px}.\32xl\:gap-y-dynamic-25{row-gap:25px}.\32xl\:gap-y-dynamic-26{row-gap:26px}.\32xl\:gap-y-dynamic-27{row-gap:27px}.\32xl\:gap-y-dynamic-28{row-gap:28px}.\32xl\:gap-y-dynamic-29{row-gap:29px}.\32xl\:gap-y-dynamic-3{row-gap:3px}.\32xl\:gap-y-dynamic-30{row-gap:30px}.\32xl\:gap-y-dynamic-31{row-gap:31px}.\32xl\:gap-y-dynamic-32{row-gap:32px}.\32xl\:gap-y-dynamic-33{row-gap:33px}.\32xl\:gap-y-dynamic-34{row-gap:34px}.\32xl\:gap-y-dynamic-35{row-gap:35px}.\32xl\:gap-y-dynamic-36{row-gap:36px}.\32xl\:gap-y-dynamic-37{row-gap:37px}.\32xl\:gap-y-dynamic-38{row-gap:38px}.\32xl\:gap-y-dynamic-39{row-gap:39px}.\32xl\:gap-y-dynamic-4{row-gap:4px}.\32xl\:gap-y-dynamic-40{row-gap:40px}.\32xl\:gap-y-dynamic-41{row-gap:41px}.\32xl\:gap-y-dynamic-42{row-gap:42px}.\32xl\:gap-y-dynamic-43{row-gap:43px}.\32xl\:gap-y-dynamic-44{row-gap:44px}.\32xl\:gap-y-dynamic-45{row-gap:45px}.\32xl\:gap-y-dynamic-46{row-gap:46px}.\32xl\:gap-y-dynamic-47{row-gap:47px}.\32xl\:gap-y-dynamic-48{row-gap:48px}.\32xl\:gap-y-dynamic-49{row-gap:49px}.\32xl\:gap-y-dynamic-5{row-gap:5px}.\32xl\:gap-y-dynamic-50{row-gap:50px}.\32xl\:gap-y-dynamic-51{row-gap:51px}.\32xl\:gap-y-dynamic-52{row-gap:52px}.\32xl\:gap-y-dynamic-53{row-gap:53px}.\32xl\:gap-y-dynamic-54{row-gap:54px}.\32xl\:gap-y-dynamic-55{row-gap:55px}.\32xl\:gap-y-dynamic-56{row-gap:56px}.\32xl\:gap-y-dynamic-57{row-gap:57px}.\32xl\:gap-y-dynamic-58{row-gap:58px}.\32xl\:gap-y-dynamic-59{row-gap:59px}.\32xl\:gap-y-dynamic-6{row-gap:6px}.\32xl\:gap-y-dynamic-60{row-gap:60px}.\32xl\:gap-y-dynamic-61{row-gap:61px}.\32xl\:gap-y-dynamic-62{row-gap:62px}.\32xl\:gap-y-dynamic-63{row-gap:63px}.\32xl\:gap-y-dynamic-64{row-gap:64px}.\32xl\:gap-y-dynamic-65{row-gap:65px}.\32xl\:gap-y-dynamic-66{row-gap:66px}.\32xl\:gap-y-dynamic-67{row-gap:67px}.\32xl\:gap-y-dynamic-68{row-gap:68px}.\32xl\:gap-y-dynamic-69{row-gap:69px}.\32xl\:gap-y-dynamic-7{row-gap:7px}.\32xl\:gap-y-dynamic-70{row-gap:70px}.\32xl\:gap-y-dynamic-71{row-gap:71px}.\32xl\:gap-y-dynamic-72{row-gap:72px}.\32xl\:gap-y-dynamic-73{row-gap:73px}.\32xl\:gap-y-dynamic-74{row-gap:74px}.\32xl\:gap-y-dynamic-75{row-gap:75px}.\32xl\:gap-y-dynamic-76{row-gap:76px}.\32xl\:gap-y-dynamic-77{row-gap:77px}.\32xl\:gap-y-dynamic-78{row-gap:78px}.\32xl\:gap-y-dynamic-79{row-gap:79px}.\32xl\:gap-y-dynamic-8{row-gap:8px}.\32xl\:gap-y-dynamic-80{row-gap:80px}.\32xl\:gap-y-dynamic-81{row-gap:81px}.\32xl\:gap-y-dynamic-82{row-gap:82px}.\32xl\:gap-y-dynamic-83{row-gap:83px}.\32xl\:gap-y-dynamic-84{row-gap:84px}.\32xl\:gap-y-dynamic-85{row-gap:85px}.\32xl\:gap-y-dynamic-86{row-gap:86px}.\32xl\:gap-y-dynamic-87{row-gap:87px}.\32xl\:gap-y-dynamic-88{row-gap:88px}.\32xl\:gap-y-dynamic-89{row-gap:89px}.\32xl\:gap-y-dynamic-9{row-gap:9px}.\32xl\:gap-y-dynamic-90{row-gap:90px}.\32xl\:gap-y-dynamic-91{row-gap:91px}.\32xl\:gap-y-dynamic-92{row-gap:92px}.\32xl\:gap-y-dynamic-93{row-gap:93px}.\32xl\:gap-y-dynamic-94{row-gap:94px}.\32xl\:gap-y-dynamic-95{row-gap:95px}.\32xl\:gap-y-dynamic-96{row-gap:96px}.\32xl\:gap-y-dynamic-97{row-gap:97px}.\32xl\:gap-y-dynamic-98{row-gap:98px}.\32xl\:gap-y-dynamic-99{row-gap:99px}.\32xl\:px-dynamic-1{padding-left:1px;padding-right:1px}.\32xl\:px-dynamic-10{padding-left:10px;padding-right:10px}.\32xl\:px-dynamic-100{padding-left:100px;padding-right:100px}.\32xl\:px-dynamic-11{padding-left:11px;padding-right:11px}.\32xl\:px-dynamic-12{padding-left:12px;padding-right:12px}.\32xl\:px-dynamic-13{padding-left:13px;padding-right:13px}.\32xl\:px-dynamic-14{padding-left:14px;padding-right:14px}.\32xl\:px-dynamic-15{padding-left:15px;padding-right:15px}.\32xl\:px-dynamic-16{padding-left:16px;padding-right:16px}.\32xl\:px-dynamic-17{padding-left:17px;padding-right:17px}.\32xl\:px-dynamic-18{padding-left:18px;padding-right:18px}.\32xl\:px-dynamic-19{padding-left:19px;padding-right:19px}.\32xl\:px-dynamic-2{padding-left:2px;padding-right:2px}.\32xl\:px-dynamic-20{padding-left:20px;padding-right:20px}.\32xl\:px-dynamic-21{padding-left:21px;padding-right:21px}.\32xl\:px-dynamic-22{padding-left:22px;padding-right:22px}.\32xl\:px-dynamic-23{padding-left:23px;padding-right:23px}.\32xl\:px-dynamic-24{padding-left:24px;padding-right:24px}.\32xl\:px-dynamic-25{padding-left:25px;padding-right:25px}.\32xl\:px-dynamic-26{padding-left:26px;padding-right:26px}.\32xl\:px-dynamic-27{padding-left:27px;padding-right:27px}.\32xl\:px-dynamic-28{padding-left:28px;padding-right:28px}.\32xl\:px-dynamic-29{padding-left:29px;padding-right:29px}.\32xl\:px-dynamic-3{padding-left:3px;padding-right:3px}.\32xl\:px-dynamic-30{padding-left:30px;padding-right:30px}.\32xl\:px-dynamic-31{padding-left:31px;padding-right:31px}.\32xl\:px-dynamic-32{padding-left:32px;padding-right:32px}.\32xl\:px-dynamic-33{padding-left:33px;padding-right:33px}.\32xl\:px-dynamic-34{padding-left:34px;padding-right:34px}.\32xl\:px-dynamic-35{padding-left:35px;padding-right:35px}.\32xl\:px-dynamic-36{padding-left:36px;padding-right:36px}.\32xl\:px-dynamic-37{padding-left:37px;padding-right:37px}.\32xl\:px-dynamic-38{padding-left:38px;padding-right:38px}.\32xl\:px-dynamic-39{padding-left:39px;padding-right:39px}.\32xl\:px-dynamic-4{padding-left:4px;padding-right:4px}.\32xl\:px-dynamic-40{padding-left:40px;padding-right:40px}.\32xl\:px-dynamic-41{padding-left:41px;padding-right:41px}.\32xl\:px-dynamic-42{padding-left:42px;padding-right:42px}.\32xl\:px-dynamic-43{padding-left:43px;padding-right:43px}.\32xl\:px-dynamic-44{padding-left:44px;padding-right:44px}.\32xl\:px-dynamic-45{padding-left:45px;padding-right:45px}.\32xl\:px-dynamic-46{padding-left:46px;padding-right:46px}.\32xl\:px-dynamic-47{padding-left:47px;padding-right:47px}.\32xl\:px-dynamic-48{padding-left:48px;padding-right:48px}.\32xl\:px-dynamic-49{padding-left:49px;padding-right:49px}.\32xl\:px-dynamic-5{padding-left:5px;padding-right:5px}.\32xl\:px-dynamic-50{padding-left:50px;padding-right:50px}.\32xl\:px-dynamic-51{padding-left:51px;padding-right:51px}.\32xl\:px-dynamic-52{padding-left:52px;padding-right:52px}.\32xl\:px-dynamic-53{padding-left:53px;padding-right:53px}.\32xl\:px-dynamic-54{padding-left:54px;padding-right:54px}.\32xl\:px-dynamic-55{padding-left:55px;padding-right:55px}.\32xl\:px-dynamic-56{padding-left:56px;padding-right:56px}.\32xl\:px-dynamic-57{padding-left:57px;padding-right:57px}.\32xl\:px-dynamic-58{padding-left:58px;padding-right:58px}.\32xl\:px-dynamic-59{padding-left:59px;padding-right:59px}.\32xl\:px-dynamic-6{padding-left:6px;padding-right:6px}.\32xl\:px-dynamic-60{padding-left:60px;padding-right:60px}.\32xl\:px-dynamic-61{padding-left:61px;padding-right:61px}.\32xl\:px-dynamic-62{padding-left:62px;padding-right:62px}.\32xl\:px-dynamic-63{padding-left:63px;padding-right:63px}.\32xl\:px-dynamic-64{padding-left:64px;padding-right:64px}.\32xl\:px-dynamic-65{padding-left:65px;padding-right:65px}.\32xl\:px-dynamic-66{padding-left:66px;padding-right:66px}.\32xl\:px-dynamic-67{padding-left:67px;padding-right:67px}.\32xl\:px-dynamic-68{padding-left:68px;padding-right:68px}.\32xl\:px-dynamic-69{padding-left:69px;padding-right:69px}.\32xl\:px-dynamic-7{padding-left:7px;padding-right:7px}.\32xl\:px-dynamic-70{padding-left:70px;padding-right:70px}.\32xl\:px-dynamic-71{padding-left:71px;padding-right:71px}.\32xl\:px-dynamic-72{padding-left:72px;padding-right:72px}.\32xl\:px-dynamic-73{padding-left:73px;padding-right:73px}.\32xl\:px-dynamic-74{padding-left:74px;padding-right:74px}.\32xl\:px-dynamic-75{padding-left:75px;padding-right:75px}.\32xl\:px-dynamic-76{padding-left:76px;padding-right:76px}.\32xl\:px-dynamic-77{padding-left:77px;padding-right:77px}.\32xl\:px-dynamic-78{padding-left:78px;padding-right:78px}.\32xl\:px-dynamic-79{padding-left:79px;padding-right:79px}.\32xl\:px-dynamic-8{padding-left:8px;padding-right:8px}.\32xl\:px-dynamic-80{padding-left:80px;padding-right:80px}.\32xl\:px-dynamic-81{padding-left:81px;padding-right:81px}.\32xl\:px-dynamic-82{padding-left:82px;padding-right:82px}.\32xl\:px-dynamic-83{padding-left:83px;padding-right:83px}.\32xl\:px-dynamic-84{padding-left:84px;padding-right:84px}.\32xl\:px-dynamic-85{padding-left:85px;padding-right:85px}.\32xl\:px-dynamic-86{padding-left:86px;padding-right:86px}.\32xl\:px-dynamic-87{padding-left:87px;padding-right:87px}.\32xl\:px-dynamic-88{padding-left:88px;padding-right:88px}.\32xl\:px-dynamic-89{padding-left:89px;padding-right:89px}.\32xl\:px-dynamic-9{padding-left:9px;padding-right:9px}.\32xl\:px-dynamic-90{padding-left:90px;padding-right:90px}.\32xl\:px-dynamic-91{padding-left:91px;padding-right:91px}.\32xl\:px-dynamic-92{padding-left:92px;padding-right:92px}.\32xl\:px-dynamic-93{padding-left:93px;padding-right:93px}.\32xl\:px-dynamic-94{padding-left:94px;padding-right:94px}.\32xl\:px-dynamic-95{padding-left:95px;padding-right:95px}.\32xl\:px-dynamic-96{padding-left:96px;padding-right:96px}.\32xl\:px-dynamic-97{padding-left:97px;padding-right:97px}.\32xl\:px-dynamic-98{padding-left:98px;padding-right:98px}.\32xl\:px-dynamic-99{padding-left:99px;padding-right:99px}}
@media (max-width:1770px){.max-\[1770px\]\:hidden{display:none}}
@media only screen and (max-width:1200px){
  #slider3 .swiper-wrapper,#slider4 .swiper-wrapper{
    height:300px !important;
  }}
@media only screen and (max-width:1024px){
  .calendar-wrapper table td .fc-daygrid-day-number{
    padding:2px 8px;
  }}
@media (max-width:992px){
    .mb-footer{
        padding-bottom:100px;
    }}
@media (max-width:939px){.md\:max-\[939px\]\:min-w-\[304px\]{min-width:304px}}
@media (max-width:894px){.md\:max-\[894px\]\:min-w-\[48px\]{min-width:48px}.md\:max-\[894px\]\:gap-\[16px\]{gap:16px}.md\:max-\[894px\]\:text-\[40px\]{font-size:40px}}
@media (max-width:768px){
  .gaming-table-container{
    padding:12px;
  }

  .enhanced-gaming-table td,.enhanced-gaming-table th{
    font-size:12px;
    padding:10px 8px;
  }

  .enhanced-gaming-table th{
    font-size:11px;
  }

  .gaming-status-badge{
    font-size:10px;
    padding:3px 6px;
  }

  .gaming-status-badge svg{
    height:10px;
    width:10px;
  }

  .datatable-pagination .datatable-pagination-list .datatable-pagination-list-item .datatable-pagination-list-item-link{
    font-size:12px;
    height:36px;
    min-width:36px;
    padding:6px 10px;
    padding:6px 8px;
  }

  .datatable-search input{
    font-size:13px;
    min-width:200px;
    padding:10px 14px 10px 40px;
  }

  .datatable-selector select{
    font-size:13px;
    padding:10px 36px 10px 14px;
  }

  .datatable-top{
    align-items:stretch;
    flex-direction:column;
    gap:12px;
  }

  .datatable-info{
    font-size:13px;
    padding:10px 14px;
  }
  .gaming-history-panel{
    padding:16px;
  }

  .gaming-table td,.gaming-table th{
    font-size:12px;
    padding:8px 6px;
  }
  .gaming-bank-card{
    min-height:80px;
    padding:12px;
  }

  .gaming-bank-card .w-12.h-12{
    height:2.5rem;
    width:2.5rem;
  }

  .gaming-bank-card .w-6.h-6{
    height:1.25rem;
    width:1.25rem;
  }
    .gaming-account-container{
      padding:1rem 0.5rem;
    }

    .gaming-title{
      font-size:2rem;
    }
    .hero-carousel-next,.hero-carousel-prev{
        display:none;
    }
    .clean-account-card:hover{
      transform:scale(1.02) translateY(-4px);
    }
    .gaming-pagination-container{
      border-radius:0.875rem;
      gap:1rem;
      gap:0.75rem;
      margin:0 0.5rem;
      margin:0 0.25rem;
      padding:1.25rem 1rem;
      padding:1rem 0.75rem;
    }

    .gaming-pagination-list{
      align-items:center;
      flex-wrap:wrap;
      gap:0.5rem;
      gap:0.375rem;
      justify-content:center;
      min-height:2.75rem;
      min-height:2.25rem;
    }

    .gaming-pagination-btn{
      border-radius:0.75rem;
      font-size:0.875rem;
      font-size:0.8125rem;
      height:2.75rem;
      height:2rem;
      min-width:2.75rem;
      min-width:2rem;
      padding:0.5rem 0.75rem;
      padding:0.375rem 0.5rem;
    }

    .gaming-pagination-btn--nav{
      min-width:3.25rem;
      min-width:2.25rem;
      padding:0.5rem 0.875rem;
    }

    .gaming-pagination-text{
      font-size:0.8125rem;
      font-size:0.75rem;
      font-weight:600;
    }

    .gaming-pagination-ellipsis{
      height:2.75rem;
      height:2rem;
      margin:0 0.25rem;
      min-width:2.75rem;
      min-width:2rem;
    }

    .gaming-pagination-info{
      margin-top:0.75rem;
    }

    .gaming-pagination-info-text{
      font-size:0.9375rem;
      font-size:0.875rem;
    }
    .gaming-pagination-list li{
      align-items:center;
      display:flex;
      justify-content:center;
    }
    .select2-container .select2-selection--multiple,.select2-container .select2-selection--single,.select2-container--default .select2-selection--multiple,.select2-container--default .select2-selection--single,.select2-container--gaming .select2-selection--multiple,.select2-container--gaming .select2-selection--single{
      font-size:0.875rem !important;
      min-height:44px !important;
      padding:0.625rem 0.875rem !important;
    }

    .select2-container .select2-selection--multiple,.select2-container--default .select2-selection--multiple,.select2-container--gaming .select2-selection--multiple{
      gap:0.125rem !important;
      max-height:100px !important;
    }

    .select2-container .select2-selection__choice,.select2-container--default .select2-selection__choice,.select2-container--gaming .select2-selection__choice{
      font-size:0.8125rem !important;
      max-width:150px !important;
      padding:0.25rem 0.5rem !important;
    }

    .select2-container .select2-selection__choice__remove,.select2-container--default .select2-selection__choice__remove,.select2-container--gaming .select2-selection__choice__remove{
      font-size:0.875rem !important;
      height:16px !important;
      width:16px !important;
    }

    .select2-container .select2-selection__arrow,.select2-container--default .select2-selection__arrow,.select2-container--gaming .select2-selection__arrow{
      height:42px !important;
      right:0.875rem !important;
    }

    .gaming-select2-dropdown{
      border-radius:0.5rem !important;
      margin-top:0.25rem !important;
    }

    .gaming-select2-dropdown .select2-results__options{
      max-height:200px !important;
    }

    .gaming-select2-dropdown .select2-results__option{
      font-size:0.875rem !important;
      padding:0.75rem 1rem !important;
    }

    .gaming-select2-dropdown .select2-search__field{
      font-size:0.875rem !important;
      padding:0.625rem 0.875rem !important;
    }

    .gaming-pagination-icon{
      height:16px;
      width:16px;
    }
    .gaming-pagination-controls{
      flex-direction:column;
      gap:0.75rem;
    }

    .gaming-auto-advance-btn{
      padding:0.375rem 0.625rem;
    }

    .gaming-auto-advance-btn,.gaming-auto-advance-text{
      font-size:0.6875rem;
    }

    .gaming-auto-advance-countdown{
      padding:0.375rem;
    }

    .gaming-countdown-text{
      font-size:0.75rem;
    }

    .gaming-countdown-label{
      font-size:0.625rem;
    }}
@media only screen and (max-width:768px){
  .calendar-wrapper .fc-toolbar{
    flex-direction:column !important;
  }
  
  .calendar-wrapper .fc-toolbar .fc-toolbar-chunk{
    margin-top:16px;
  }
  
  .calendar-wrapper .fc-toolbar .fc-toolbar-chunk:first-child{
    margin-top:0 !important;
  }}
@media (max-width:767px){
    .hero-carousel .swiper-slide{
        aspect-ratio:375/156;
    }}
@media (max-width:640px){
    .gaming-attributes-container{
      border-radius:1rem;
      margin:0 -0.5rem;
    }

    .gaming-attributes-header{
      padding:1.5rem 1.5rem 1rem 1.5rem;
    }

    .gaming-attributes-title{
      font-size:1.5rem;
    }

    .gaming-attribute-section{
      margin-bottom:1.5rem;
      margin-bottom:1.25rem !important;
      padding:1rem 1.5rem;
      padding:1rem 1.25rem !important;
    }

    .gaming-attribute-icon{
      height:2rem;
      width:2rem;
    }

    .gaming-attribute-title{
      font-size:1.1rem;
    }

    .gaming-attribute-count{
      font-size:0.8rem;
      padding:0.375rem 0.75rem;
    }

    .gaming-attribute-grid{
      gap:0.5rem;
      grid-template-columns:repeat(auto-fill, minmax(50px, 1fr));
    }

    .gaming-empty-state{
      padding:2rem 1.5rem;
    }

    .gaming-empty-state-icon{
      height:3rem;
      margin-bottom:1rem;
      width:3rem;
    }

    .gaming-empty-state-text{
      font-size:0.875rem;
    }

    .gaming-thumbnail{
      box-sizing:border-box !important;
      display:block !important;
      margin:0 !important;
      min-height:48px !important;
      min-width:64px !important;
      padding:0 !important;
      width:64px !important;
    }
    .gaming-attribute-grid[style*="minmax(50px"]{
      gap:0.375rem !important;
      grid-template-columns:repeat(auto-fill, minmax(45px, 1fr)) !important;
    }

    .gaming-attribute-header{
      margin-bottom:1rem !important;
      padding-bottom:0.75rem !important;
    }
    .clean-account-card{
      min-height:320px;
      padding:0.75rem;
    }

    .clean-card-header{
      margin-bottom:0.5rem;
    }

    .clean-image-section{
      margin-bottom:0.75rem;
    }

    .clean-account-title{
      margin-bottom:0.5rem;
      margin-bottom:0.625rem;
      min-height:3rem;
      padding:0 0.25rem;
    }

    .clean-account-title h3{
      font-size:0.9375rem;
      font-size:0.95rem;
      font-weight:600;
      line-height:1.4;
    }

    .account-title-truncated{
      background:hsla(0,0%,100%,.05);
      border:1px solid rgba(80,129,255,.2);
      font-size:0.95rem;
      margin:0 auto;
      max-width:calc(100% - 1rem);
      padding:0.375rem 0.5rem;
    }

    .account-title-truncated:hover{
      background:rgba(80,129,255,.15);
      border-color:rgba(80,129,255,.4);
      transform:scale(1.01);
    }



    .feature-header h4{
      font-size:0.75rem;
    }

    .feature-toggle-btn{
      font-size:0.625rem;
      padding:0.25rem 0.375rem;
    }

    .feature-grid{
      gap:0.375rem;
      max-height:120px;
      padding:0.5rem;
    }

    .feature-item{
      height:32px;
      width:32px;
    }

    .marquee-item{
      height:24px;
      width:24px;
    }

    .clean-purchase-btn{
      padding:0.5rem 0.625rem;
    }

    .purchase-icon{
      font-size:0.75rem;
    }

    .original-price{
      font-size:0.625rem;
    }

    .discounted-price,.regular-price{
      font-size:0.75rem;
    }
    .clean-account-card:hover{
      transform:scale(1.01) translateY(-2px);
    }
    .gaming-pagination-container{
      gap:0.875rem;
      gap:0.625rem;
      margin:0 0.25rem;
      padding:1rem 0.75rem;
      padding:0.875rem 0.5rem;
    }

    .gaming-pagination-list{
      gap:0.375rem;
      gap:0.25rem;
      max-width:100%;
      overflow-x:auto;
      padding:0.25rem 0;
      padding:0.125rem 0;
      scrollbar-width:none;
      -ms-overflow-style:none;
    }

    .gaming-pagination-list::-webkit-scrollbar{
      display:none;
    }

    .gaming-pagination-btn{
      flex-shrink:0;
      font-size:0.8125rem;
      font-size:0.75rem;
      height:2.5rem;
      height:1.875rem;
      min-width:2.5rem;
      min-width:1.875rem;
      padding:0.375rem 0.625rem;
      padding:0.25rem 0.375rem;
    }

    .gaming-pagination-btn--nav{
      min-width:2.875rem;
      min-width:2rem;
      padding:0.375rem 0.75rem;
    }

    .gaming-pagination-text{
      font-size:0.75rem;
      font-size:0.6875rem;
    }

    .gaming-pagination-ellipsis{
      height:2.5rem;
      height:1.875rem;
      min-width:2.5rem;
      min-width:1.875rem;
    }

    .gaming-pagination-icon{
      height:18px;
      height:14px;
      width:18px;
      width:14px;
    }
    .gaming-pagination-btn:active{
      transform:translateY(0) scale(0.98);
      transition:all 0.1s ease;
    }}
@media only screen and (max-width:600px){
  #slider3 .swiper-wrapper,#slider4 .swiper-wrapper{
    height:180px !important;
  }}
@media (max-width:480px){
  .enhanced-gaming-table{
    font-size:11px;
  }

  .enhanced-gaming-table td,.enhanced-gaming-table th{
    font-size:11px;
    padding:8px 6px;
  }

  .enhanced-gaming-table th{
    font-size:10px;
  }

  .gaming-status-badge{
    font-size:9px;
    padding:2px 4px;
  }

  .datatable-search input{
    min-width:160px;
  }
    .nav-section-left,.nav-section-right{
        width:calc(50% - 35px);
    }

    .floating-home-button{
        height:70px;
        top:-23px;
        width:70px;
    }

    .nav-icon{
        font-size:24px;
        height:24px;
        margin:0 auto 5px;
        width:24px;
    }

    .nav-label{
        font-size:11px;
        height:15px;
        line-height:15px;
    }

    .nav-item{
        padding-top:6px;
    }
    .clean-account-card{
      min-height:300px;
      padding:0.625rem;
    }

    .clean-stats-grid,.feature-grid{
      padding:0.375rem;
    }
    .clean-account-title{
      min-height:2.75rem;
      padding:0 0.125rem;
    }

    .clean-account-title h3{
      font-size:0.9rem;
      line-height:1.35;
    }

    .account-title-truncated{
      font-size:0.9rem;
      max-width:calc(100% - 0.5rem);
      padding:0.3rem 0.4rem;
    }
    .gaming-pagination-container{
      border-radius:0.75rem;
      gap:0.75rem;
      gap:0.5rem;
      margin:0;
      padding:0.875rem 0.5rem;
      padding:0.75rem 0.375rem;
    }

    .gaming-pagination-list{
      gap:0.25rem;
      gap:0.125rem;
      justify-content:flex-start;
      min-height:2.25rem;
      min-height:1.75rem;
      padding:0.125rem 0;
      padding:0;
    }

    .gaming-pagination-text{
      display:none !important;
    }

    .gaming-pagination-btn{
      border-radius:0.5rem;
      border-radius:0.25rem;
      flex-shrink:0;
      font-size:0.75rem;
      font-size:0.6875rem;
      height:2.25rem;
      height:1.75rem;
      min-width:2.25rem;
      min-width:1.75rem;
      padding:0.25rem;
      padding:0.125rem 0.25rem;
    }

    .gaming-pagination-btn--nav{
      min-width:2.5rem;
      min-width:1.875rem;
      padding:0.25rem 0.375rem;
      padding:0.125rem 0.25rem;
    }

    .gaming-pagination-ellipsis{
      font-size:0.875rem;
      font-size:0.75rem;
      height:2.25rem;
      height:1.75rem;
      min-width:2.25rem;
      min-width:1.75rem;
    }

    .gaming-pagination-icon{
      height:16px;
      height:12px;
      width:16px;
      width:12px;
    }

    .gaming-pagination-info-text{
      font-size:0.8125rem;
      font-size:0.75rem;
    }
    .select2-container .select2-selection--multiple,.select2-container--default .select2-selection--multiple,.select2-container--gaming .select2-selection--multiple{
      max-height:80px !important;
    }

    .select2-container .select2-selection__choice,.select2-container--default .select2-selection__choice,.select2-container--gaming .select2-selection__choice{
      font-size:0.75rem !important;
      max-width:120px !important;
    }

    .gaming-select2-dropdown .select2-results__options{
      max-height:180px !important;
    }
    .gaming-pagination-controls{
      flex-direction:column;
      gap:0.5rem;
    }

    .gaming-auto-advance-btn{
      gap:0.25rem;
      padding:0.25rem 0.5rem;
    }

    .gaming-auto-advance-btn,.gaming-auto-advance-text{
      font-size:0.625rem;
    }

    .gaming-auto-advance-icon{
      height:12px;
      width:12px;
    }

    .gaming-auto-advance-countdown{
      gap:0.125rem;
      padding:0.25rem;
    }

    .gaming-countdown-svg{
      height:24px;
      width:24px;
    }

    .gaming-countdown-text{
      font-size:0.6875rem;
    }

    .gaming-countdown-label{
      font-size:0.5625rem;
    }}
@media (max-width:400px){.max-\[400px\]\:hidden{display:none}}
@media (max-width:360px){
    .gaming-pagination-container{
      gap:0.625rem;
      gap:0.375rem;
      padding:0.75rem 0.375rem;
      padding:0.625rem 0.25rem;
    }

    .gaming-pagination-list{
      gap:0.125rem;
      gap:0.0625rem;
      min-height:1.5rem;
      padding:0;
    }

    .gaming-pagination-btn{
      font-size:0.6875rem;
      font-size:0.625rem;
      height:2rem;
      height:1.5rem;
      min-width:2rem;
      min-width:1.5rem;
      padding:0.125rem;
      padding:0.0625rem 0.125rem;
    }

    .gaming-pagination-btn--nav{
      min-width:2.25rem;
      min-width:1.625rem;
      padding:0.125rem 0.25rem;
    }

    .gaming-pagination-ellipsis{
      font-size:0.75rem;
      font-size:0.6875rem;
      height:2rem;
      height:1.5rem;
      min-width:2rem;
      min-width:1.5rem;
    }

    .gaming-pagination-icon{
      height:14px;
      height:10px;
      width:14px;
      width:10px;
    }

    .gaming-pagination-info-text{
      font-size:0.75rem;
      font-size:0.6875rem;
    }}
@media (hover:none) and (pointer:coarse){
    .gaming-thumbnail{
      min-height:48px !important;
      min-width:64px !important;
    }

    .gaming-thumbnail img{
      touch-action:manipulation !important;
    }
    .clean-account-card:active{
      transform:scale(1.02) translateY(-2px);
      transition:all 0.15s ease;
    }}
@media (prefers-color-scheme:dark){.dark\:flex{display:flex;}.dark\:hidden{display:none;}.dark\:bg-\[\#060818\]{--tw-bg-opacity:1;background-color:rgb(6 8 24 / var(--tw-bg-opacity, 1));}.dark\:bg-\[\#121c2c\]{--tw-bg-opacity:1;background-color:rgb(18 28 44 / var(--tw-bg-opacity, 1));}.dark\:bg-black{--tw-bg-opacity:1;background-color:rgb(0 0 0 / var(--tw-bg-opacity, 1));}.dark\:bg-success-dark-light{--tw-bg-opacity:1;background-color:rgb(21 87 36 / var(--tw-bg-opacity, 1));}.dark\:text-white{--tw-text-opacity:1;color:rgb(255 255 255 / var(--tw-text-opacity, 1));}}
@media (prefers-reduced-motion:reduce){
    .clean-account-card{
      transition:transform 0.2s ease,border-color 0.2s ease;
    }

    .clean-account-card:hover{
      animation:none;
      transform:scale(1.02) translateY(-2px);
    }
    .gaming-pagination-btn{
      transition:background-color 0.2s ease,color 0.2s ease;
    }

    .gaming-pagination-btn:hover{
      animation:none;
      transform:none;
    }

    .gaming-pagination-btn--active,.gaming-pagination-container{
      animation:none;
    }

    .gaming-pagination-btn:before{
      display:none;
    }}
@media print{.print\:hidden{display:none;}}