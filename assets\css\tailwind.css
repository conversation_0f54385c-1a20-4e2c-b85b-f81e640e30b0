/* Tailwind CSS Base Styles */

*, ::before, ::after {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}

::backdrop {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}

/* ! tailwindcss v3.4.17 | MIT License | https://tailwindcss.com */

/*
1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)
2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)
*/

*,
::before,
::after {
  box-sizing: border-box;
  /* 1 */
  border-width: 0;
  /* 2 */
  border-style: solid;
  /* 2 */
  border-color: #e5e7eb;
  /* 2 */
}

::before,
::after {
  --tw-content: '';
}

/*
1. Use a consistent sensible line-height in all browsers.
2. Prevent adjustments of font size after orientation changes in iOS.
3. Use a more readable tab size.
4. Use the user's configured `sans` font-family by default.
5. Use the user's configured `sans` font-feature-settings by default.
6. Use the user's configured `sans` font-variation-settings by default.
7. Disable tap highlights on iOS
*/

html,
:host {
  line-height: 1.5;
  /* 1 */
  -webkit-text-size-adjust: 100%;
  /* 2 */
  -moz-tab-size: 4;
  /* 3 */
  -o-tab-size: 4;
     tab-size: 4;
  /* 3 */
  font-family: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  /* 4 */
  font-feature-settings: normal;
  /* 5 */
  font-variation-settings: normal;
  /* 6 */
  -webkit-tap-highlight-color: transparent;
  /* 7 */
}

/*
1. Remove the margin in all browsers.
2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.
*/

body {
  margin: 0;
  /* 1 */
  line-height: inherit;
  /* 2 */
}

/*
1. Add the correct height in Firefox.
2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)
3. Ensure horizontal rules are visible by default.
*/

hr {
  height: 0;
  /* 1 */
  color: inherit;
  /* 2 */
  border-top-width: 1px;
  /* 3 */
}

/*
Add the correct text decoration in Chrome, Edge, and Safari.
*/

abbr:where([title]) {
  -webkit-text-decoration: underline dotted;
          text-decoration: underline dotted;
}

/*
Remove the default font size and weight for headings.
*/

h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: inherit;
  font-weight: inherit;
}

/*
Reset links to optimize for opt-in styling instead of opt-out.
*/

a {
  color: inherit;
  text-decoration: inherit;
}

/*
Add the correct font weight in Edge and Safari.
*/

b,
strong {
  font-weight: bolder;
}

/*
1. Use the user's configured `mono` font-family by default.
2. Use the user's configured `mono` font-feature-settings by default.
3. Use the user's configured `mono` font-variation-settings by default.
4. Correct the odd `em` font sizing in all browsers.
*/

code,
kbd,
samp,
pre {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  /* 1 */
  font-feature-settings: normal;
  /* 2 */
  font-variation-settings: normal;
  /* 3 */
  font-size: 1em;
  /* 4 */
}

/*
Add the correct font size in all browsers.
*/

small {
  font-size: 80%;
}

/*
Prevent `sub` and `sup` elements from affecting the line height in all browsers.
*/

sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

/*
1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)
2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)
3. Remove gaps between table borders by default.
*/

table {
  text-indent: 0;
  /* 1 */
  border-color: inherit;
  /* 2 */
  border-collapse: collapse;
  /* 3 */
}

/*
1. Change the font styles in all browsers.
2. Remove the margin in Firefox and Safari.
3. Remove default padding in all browsers.
*/

button,
input,
optgroup,
select,
textarea {
  font-family: inherit;
  /* 1 */
  font-feature-settings: inherit;
  /* 1 */
  font-variation-settings: inherit;
  /* 1 */
  font-size: 100%;
  /* 1 */
  font-weight: inherit;
  /* 1 */
  line-height: inherit;
  /* 1 */
  letter-spacing: inherit;
  /* 1 */
  color: inherit;
  /* 1 */
  margin: 0;
  /* 2 */
  padding: 0;
  /* 3 */
}

/*
Remove the inheritance of text transform in Edge and Firefox.
*/

button,
select {
  text-transform: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Remove default button styles.
*/

button,
input:where([type='button']),
input:where([type='reset']),
input:where([type='submit']) {
  -webkit-appearance: button;
  /* 1 */
  background-color: transparent;
  /* 2 */
  background-image: none;
  /* 2 */
}

/*
Use the modern Firefox focus style for all focusable elements.
*/

:-moz-focusring {
  outline: auto;
}

/*
Remove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)
*/

:-moz-ui-invalid {
  box-shadow: none;
}

/*
Add the correct vertical alignment in Chrome and Firefox.
*/

progress {
  vertical-align: baseline;
}

/*
Correct the cursor style of increment and decrement buttons in Safari.
*/

::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
  height: auto;
}

/*
1. Correct the odd appearance in Chrome and Safari.
2. Correct the outline style in Safari.
*/

[type='search'] {
  -webkit-appearance: textfield;
  /* 1 */
  outline-offset: -2px;
  /* 2 */
}

/*
Remove the inner padding in Chrome and Safari on macOS.
*/

::-webkit-search-decoration {
  -webkit-appearance: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Change font properties to `inherit` in Safari.
*/

::-webkit-file-upload-button {
  -webkit-appearance: button;
  /* 1 */
  font: inherit;
  /* 2 */
}

/*
Add the correct display in Chrome and Safari.
*/

summary {
  display: list-item;
}

/*
Removes the default spacing and border for appropriate elements.
*/

blockquote,
dl,
dd,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
figure,
p,
pre {
  margin: 0;
}

fieldset {
  margin: 0;
  padding: 0;
}

legend {
  padding: 0;
}

ol,
ul,
menu {
  list-style: none;
  margin: 0;
  padding: 0;
}

/*
Reset default styling for dialogs.
*/

dialog {
  padding: 0;
}

/*
Prevent resizing textareas horizontally by default.
*/

textarea {
  resize: vertical;
}

/*
1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)
2. Set the default placeholder color to the user's configured gray 400 color.
*/

input::-moz-placeholder, textarea::-moz-placeholder {
  opacity: 1;
  /* 1 */
  color: #9ca3af;
  /* 2 */
}

input::placeholder,
textarea::placeholder {
  opacity: 1;
  /* 1 */
  color: #9ca3af;
  /* 2 */
}

/*
Set the default cursor for buttons.
*/

button,
[role="button"] {
  cursor: pointer;
}

/*
Make sure disabled buttons don't get the pointer cursor.
*/

:disabled {
  cursor: default;
}

/*
1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)
2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)
   This can trigger a poorly considered lint error in some tools but is included by design.
*/

img,
svg,
video,
canvas,
audio,
iframe,
embed,
object {
  display: block;
  /* 1 */
  vertical-align: middle;
  /* 2 */
}

/*
Constrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)
*/

img,
video {
  max-width: 100%;
  height: auto;
}

/* Make elements with the HTML hidden attribute stay hidden by default */

[hidden]:where(:not([hidden="until-found"])) {
  display: none;
}

/* Signika Font Declaration */

@font-face {
  font-family: 'Signika';

  src: url('/assets/webfonts/Signika-SemiBold.ttf') format('truetype');

  font-display: swap;

  font-weight: 600;

  font-style: normal;
}

/* League Spartan Font Declaration */

@font-face {
  font-family: 'League Spartan';

  src: url('/assets/webfonts/LeagueSpartan-Regular.ttf') format('truetype');

  font-display: swap;

  font-weight: 400;

  font-style: normal;
}

/* Global font application */

* {
  font-family: 'Signika', ui-sans-serif, system-ui, sans-serif;
}

/* HTML and body base styles */

html {
  scroll-behavior: smooth;
}

body {
  font-family: 'Signika', ui-sans-serif, system-ui, sans-serif;
  background-color: #0F172A;
  color: #FFFFFF;
}

.\!container {
  width: 100% !important;
  margin-right: auto !important;
  margin-left: auto !important;
  padding-right: 16px !important;
  padding-left: 16px !important;
}

.container {
  width: 100%;
  margin-right: auto;
  margin-left: auto;
  padding-right: 16px;
  padding-left: 16px;
}

@media (min-width: 640px) {
  .\!container {
    max-width: 640px !important;
  }

  .container {
    max-width: 640px;
  }
}

@media (min-width: 768px) {
  .\!container {
    max-width: 768px !important;
  }

  .container {
    max-width: 768px;
  }
}

@media (min-width: 1024px) {
  .\!container {
    max-width: 1024px !important;
  }

  .container {
    max-width: 1024px;
  }
}

@media (min-width: 1280px) {
  .\!container {
    max-width: 1280px !important;
  }

  .container {
    max-width: 1280px;
  }
}

@media (min-width: 1536px) {
  .\!container {
    max-width: 1536px !important;
  }

  .container {
    max-width: 1536px;
  }
}

/* Gaming Button Components */

.gaming-btn-primary {
  display: inline-flex;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  align-items: center;
  justify-content: center;
  border-radius: 0.5rem;
  padding-left: 1rem;
  padding-right: 1rem;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  font-weight: 600;
  transition-property: all;
  transition-duration: 300ms;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

.gaming-btn-primary:hover {
  --tw-scale-x: 1.05;
  --tw-scale-y: 1.05;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.gaming-btn-primary:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-offset-width: 2px;
}

.gaming-btn-primary {
  background-image: linear-gradient(to right, var(--tw-gradient-stops));
  --tw-gradient-from: #5081FF var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(80 129 255 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
  --tw-gradient-to: #3463DB var(--tw-gradient-to-position);
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.gaming-btn-primary:hover {
  --tw-gradient-from: #3463DB var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(52 99 219 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
  --tw-gradient-to: #5081FF var(--tw-gradient-to-position);
  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.gaming-btn-secondary {
  display: inline-flex;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  align-items: center;
  justify-content: center;
  border-radius: 0.5rem;
  padding-left: 1rem;
  padding-right: 1rem;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  font-weight: 600;
  transition-property: all;
  transition-duration: 300ms;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

.gaming-btn-secondary:hover {
  --tw-scale-x: 1.05;
  --tw-scale-y: 1.05;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.gaming-btn-secondary:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-offset-width: 2px;
}

.gaming-btn-secondary {
  background-image: linear-gradient(to right, var(--tw-gradient-stops));
  --tw-gradient-from: #272450 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(39 36 80 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
  --tw-gradient-to: #9C27B0 var(--tw-gradient-to-position);
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.gaming-btn-secondary:hover {
  --tw-gradient-from: #9C27B0 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(156 39 176 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
  --tw-gradient-to: #272450 var(--tw-gradient-to-position);
  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

/* Gaming Card Components */

.gaming-card {
  position: relative;
  overflow: hidden;
  border-radius: 0.75rem;
  border-width: 1px;
  border-color: rgb(80 129 255 / 0.3);
  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));
  --tw-gradient-from: rgb(30 41 59 / 0.9) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(30 41 59 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
  --tw-gradient-to: rgb(15 23 42 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(15 23 42 / 0.8) var(--tw-gradient-via-position), var(--tw-gradient-to);
  --tw-gradient-to: rgb(30 41 59 / 0.9) var(--tw-gradient-to-position);
  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  --tw-backdrop-blur: blur(4px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}

.gaming-image-gallery .gaming-card {
  /* ENHANCED: Improved height constraints for better visibility */
  max-height: 85vh;
  height: auto;
  display: flex;
  align-items: center;
  justify-content: center;
  visibility: visible;
  /* ENHANCED: Ensure proper positioning */
  position: relative;
  z-index: 1;
}

.gaming-card-hover {
  position: relative;
  overflow: hidden;
  border-radius: 0.75rem;
  border-width: 1px;
  border-color: rgb(80 129 255 / 0.3);
  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));
  --tw-gradient-from: rgb(30 41 59 / 0.9) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(30 41 59 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
  --tw-gradient-to: rgb(15 23 42 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(15 23 42 / 0.8) var(--tw-gradient-via-position), var(--tw-gradient-to);
  --tw-gradient-to: rgb(30 41 59 / 0.9) var(--tw-gradient-to-position);
  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  --tw-backdrop-blur: blur(4px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}

.gaming-image-gallery .gaming-card-hover {
  /* ENHANCED: Improved height constraints for better visibility */
  max-height: 85vh;
  height: auto;
  display: flex;
  align-items: center;
  justify-content: center;
  visibility: visible;
  /* ENHANCED: Ensure proper positioning */
  position: relative;
  z-index: 1;
}

.gaming-card-hover {
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  transition-property: all;
  transition-duration: 300ms;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

.gaming-card-hover:hover {
  --tw-translate-y: -0.25rem;
  --tw-scale-x: 1.05;
  --tw-scale-y: 1.05;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  --tw-border-opacity: 1;
  border-color: rgb(80 129 255 / var(--tw-border-opacity, 1));
  --tw-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);
  --tw-shadow-colored: 0 25px 50px -12px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  --tw-shadow-color: rgb(80 129 255 / 0.5);
  --tw-shadow: var(--tw-shadow-colored);
}

/* Gaming Input Components */

.gaming-input {
  width: 100%;
  border-radius: 0.5rem;
  border-width: 1px;
  border-color: rgb(80 129 255 / 0.3);
  background-color: rgb(30 41 59 / 0.5);
  padding-left: 1rem;
  padding-right: 1rem;
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.gaming-input::-moz-placeholder {
  --tw-placeholder-opacity: 1;
  color: rgb(156 163 175 / var(--tw-placeholder-opacity, 1));
}

.gaming-input::placeholder {
  --tw-placeholder-opacity: 1;
  color: rgb(156 163 175 / var(--tw-placeholder-opacity, 1));
}

.gaming-input {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
}

.gaming-input:focus {
  --tw-border-opacity: 1;
  border-color: rgb(80 129 255 / var(--tw-border-opacity, 1));
  outline: 2px solid transparent;
  outline-offset: 2px;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-color: rgb(80 129 255 / 0.2);
}

.gaming-select {
  width: 100%;
  border-radius: 0.5rem;
  border-width: 1px;
  border-color: rgb(80 129 255 / 0.3);
  background-color: rgb(30 41 59 / 0.5);
  padding-left: 1rem;
  padding-right: 1rem;
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.gaming-select::-moz-placeholder {
  --tw-placeholder-opacity: 1;
  color: rgb(156 163 175 / var(--tw-placeholder-opacity, 1));
}

.gaming-select::placeholder {
  --tw-placeholder-opacity: 1;
  color: rgb(156 163 175 / var(--tw-placeholder-opacity, 1));
}

.gaming-select {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
}

.gaming-select:focus {
  --tw-border-opacity: 1;
  border-color: rgb(80 129 255 / var(--tw-border-opacity, 1));
  outline: 2px solid transparent;
  outline-offset: 2px;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-color: rgb(80 129 255 / 0.2);
}

.gaming-select {
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  background-size: 16px 16px;
  background-position: right;
  background-repeat: no-repeat;
  padding-right: 2.5rem;
}

.gaming-select:focus {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
}

.gaming-select {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
}

/* Gaming Navigation Components */

.gaming-nav-item {
  position: relative;
  border-radius: 0.5rem;
  padding-left: 1rem;
  padding-right: 1rem;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  --tw-text-opacity: 1;
  color: rgb(209 213 219 / var(--tw-text-opacity, 1));
  transition-property: all;
  transition-duration: 300ms;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

.gaming-image-gallery .gaming-nav-item {
  /* ENHANCED: Improved height constraints for better visibility */
  max-height: 85vh;
  height: auto;
  display: flex;
  align-items: center;
  justify-content: center;
  visibility: visible;
  /* ENHANCED: Ensure proper positioning */
  position: relative;
  z-index: 1;
}

.gaming-nav-item:hover {
  background-color: rgb(80 129 255 / 0.2);
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.gaming-nav-item.active {
  background-image: linear-gradient(to right, var(--tw-gradient-stops));
  --tw-gradient-from: #5081FF var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(80 129 255 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
  --tw-gradient-to: #3463DB var(--tw-gradient-to-position);
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

/* Gaming Modal Components */

/* Gaming Table Components */

.gaming-table {
  width: 100%;
  overflow: hidden;
  border-radius: 0.5rem;
  border-width: 1px;
  border-color: rgb(80 129 255 / 0.3);
  background-color: rgb(30 41 59 / 0.5);
}

.gaming-table th {
  border-bottom-width: 1px;
  border-color: rgb(80 129 255 / 0.3);
  --tw-bg-opacity: 1;
  background-color: rgb(15 23 42 / var(--tw-bg-opacity, 1));
  padding-left: 1.5rem;
  padding-right: 1.5rem;
  padding-top: 1rem;
  padding-bottom: 1rem;
  text-align: left;
  font-size: 0.75rem;
  line-height: 1rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  --tw-text-opacity: 1;
  color: rgb(209 213 219 / var(--tw-text-opacity, 1));
  background-color: #0F172A;
}

.gaming-table td {
  border-bottom-width: 1px;
  border-color: rgb(80 129 255 / 0.1);
  padding-left: 1.5rem;
  padding-right: 1.5rem;
  padding-top: 1rem;
  padding-bottom: 1rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  --tw-text-opacity: 1;
  color: rgb(209 213 219 / var(--tw-text-opacity, 1));
}

.gaming-table tr:hover {
  background-color: rgb(80 129 255 / 0.1);
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}

.pointer-events-none {
  pointer-events: none;
}

.pointer-events-auto {
  pointer-events: auto;
}

.\!visible {
  visibility: visible !important;
}

.visible {
  visibility: visible;
}

.invisible {
  visibility: hidden;
}

.collapse {
  visibility: collapse;
}

.static {
  position: static;
}

.fixed {
  position: fixed;
}

.absolute {
  position: absolute;
}

.relative {
  position: relative;
}

.sticky {
  position: sticky;
}

.inset-0 {
  inset: 0px;
}

.inset-1 {
  inset: 0.25rem;
}

.inset-x-0 {
  left: 0px;
  right: 0px;
}

.inset-y-0 {
  top: 0px;
  bottom: 0px;
}

.-bottom-1 {
  bottom: -0.25rem;
}

.-bottom-12 {
  bottom: -3rem;
}

.-bottom-2 {
  bottom: -0.5rem;
}

.-bottom-5 {
  bottom: -1.25rem;
}

.-end-6 {
  inset-inline-end: -1.5rem;
}

.-left-2 {
  left: -0.5rem;
}

.-right-1 {
  right: -0.25rem;
}

.-right-2 {
  right: -0.5rem;
}

.-top-0 {
  top: -0px;
}

.-top-1 {
  top: -0.25rem;
}

.-top-2 {
  top: -0.5rem;
}

.-top-3 {
  top: -0.75rem;
}

.-top-8 {
  top: -2rem;
}

.bottom-0 {
  bottom: 0px;
}

.bottom-1 {
  bottom: 0.25rem;
}

.bottom-10 {
  bottom: 2.5rem;
}

.bottom-12 {
  bottom: 3rem;
}

.bottom-14 {
  bottom: 3.5rem;
}

.bottom-2 {
  bottom: 0.5rem;
}

.bottom-20 {
  bottom: 5rem;
}

.bottom-4 {
  bottom: 1rem;
}

.bottom-5 {
  bottom: 1.25rem;
}

.bottom-6 {
  bottom: 1.5rem;
}

.bottom-8 {
  bottom: 2rem;
}

.bottom-\[12px\] {
  bottom: 12px;
}

.bottom-full {
  bottom: 100%;
}

.end-0 {
  inset-inline-end: 0px;
}

.end-1 {
  inset-inline-end: 0.25rem;
}

.end-10 {
  inset-inline-end: 2.5rem;
}

.end-5 {
  inset-inline-end: 1.25rem;
}

.end-6 {
  inset-inline-end: 1.5rem;
}

.end-auto {
  inset-inline-end: auto;
}

.left-0 {
  left: 0px;
}

.left-1 {
  left: 0.25rem;
}

.left-1\/2 {
  left: 50%;
}

.left-1\/4 {
  left: 25%;
}

.left-10 {
  left: 2.5rem;
}

.left-2 {
  left: 0.5rem;
}

.left-20 {
  left: 5rem;
}

.left-24 {
  left: 6rem;
}

.left-3 {
  left: 0.75rem;
}

.left-4 {
  left: 1rem;
}

.left-5 {
  left: 1.25rem;
}

.left-6 {
  left: 1.5rem;
}

.left-8 {
  left: 2rem;
}

.left-\[2px\] {
  left: 2px;
}

.left-\[50\%\] {
  left: 50%;
}

.right-0 {
  right: 0px;
}

.right-1 {
  right: 0.25rem;
}

.right-1\/3 {
  right: 33.333333%;
}

.right-1\/4 {
  right: 25%;
}

.right-10 {
  right: 2.5rem;
}

.right-14 {
  right: 3.5rem;
}

.right-2 {
  right: 0.5rem;
}

.right-20 {
  right: 5rem;
}

.right-3 {
  right: 0.75rem;
}

.right-4 {
  right: 1rem;
}

.right-6 {
  right: 1.5rem;
}

.right-8 {
  right: 2rem;
}

.start-0 {
  inset-inline-start: 0px;
}

.start-1 {
  inset-inline-start: 0.25rem;
}

.start-4 {
  inset-inline-start: 1rem;
}

.top-0 {
  top: 0px;
}

.top-1 {
  top: 0.25rem;
}

.top-1\/2 {
  top: 50%;
}

.top-1\/3 {
  top: 33.333333%;
}

.top-1\/4 {
  top: 25%;
}

.top-10 {
  top: 2.5rem;
}

.top-11 {
  top: 2.75rem;
}

.top-2 {
  top: 0.5rem;
}

.top-20 {
  top: 5rem;
}

.top-3 {
  top: 0.75rem;
}

.top-3\/4 {
  top: 75%;
}

.top-4 {
  top: 1rem;
}

.top-5 {
  top: 1.25rem;
}

.top-6 {
  top: 1.5rem;
}

.top-\[50\%\] {
  top: 50%;
}

.top-full {
  top: 100%;
}

.isolate {
  isolation: isolate;
}

.-z-10 {
  z-index: -10;
}

.-z-20 {
  z-index: -20;
}

.z-0 {
  z-index: 0;
}

.z-10 {
  z-index: 10;
}

.z-20 {
  z-index: 20;
}

.z-30 {
  z-index: 30;
}

.z-40 {
  z-index: 40;
}

.z-50 {
  z-index: 50;
}

.z-\[1000000\] {
  z-index: 1000000;
}

.z-\[1000001\] {
  z-index: 1000001;
}

.z-\[10000\] {
  z-index: 10000;
}

.z-\[1\] {
  z-index: 1;
}

.z-\[2\] {
  z-index: 2;
}

.z-\[3\] {
  z-index: 3;
}

.z-\[5\] {
  z-index: 5;
}

.z-\[60\] {
  z-index: 60;
}

.z-\[999997\] {
  z-index: 999997;
}

.z-\[999998\] {
  z-index: 999998;
}

.z-\[999999\] {
  z-index: 999999;
}

.z-\[9999\] {
  z-index: 9999;
}

.order-1 {
  order: 1;
}

.order-2 {
  order: 2;
}

.order-last {
  order: 9999;
}

.col-span-1 {
  grid-column: span 1 / span 1;
}

.col-span-2 {
  grid-column: span 2 / span 2;
}

.col-span-3 {
  grid-column: span 3 / span 3;
}

.col-span-4 {
  grid-column: span 4 / span 4;
}

.col-span-5 {
  grid-column: span 5 / span 5;
}

.col-span-6 {
  grid-column: span 6 / span 6;
}

.col-span-9 {
  grid-column: span 9 / span 9;
}

.col-span-full {
  grid-column: 1 / -1;
}

.col-start-2 {
  grid-column-start: 2;
}

.row-span-2 {
  grid-row: span 2 / span 2;
}

.row-span-3 {
  grid-row: span 3 / span 3;
}

.row-start-1 {
  grid-row-start: 1;
}

.row-start-3 {
  grid-row-start: 3;
}

.-m-2 {
  margin: -0.5rem;
}

.-m-5 {
  margin: -1.25rem;
}

.m-0 {
  margin: 0px;
}

.m-1 {
  margin: 0.25rem;
}

.m-6 {
  margin: 1.5rem;
}

.m-8 {
  margin: 2rem;
}

.m-auto {
  margin: auto;
}

.-mx-1 {
  margin-left: -0.25rem;
  margin-right: -0.25rem;
}

.-mx-2 {
  margin-left: -0.5rem;
  margin-right: -0.5rem;
}

.-mx-4 {
  margin-left: -1rem;
  margin-right: -1rem;
}

.-mx-5 {
  margin-left: -1.25rem;
  margin-right: -1.25rem;
}

.-mx-6 {
  margin-left: -1.5rem;
  margin-right: -1.5rem;
}

.mx-0 {
  margin-left: 0px;
  margin-right: 0px;
}

.mx-1 {
  margin-left: 0.25rem;
  margin-right: 0.25rem;
}

.mx-10 {
  margin-left: 2.5rem;
  margin-right: 2.5rem;
}

.mx-2 {
  margin-left: 0.5rem;
  margin-right: 0.5rem;
}

.mx-3 {
  margin-left: 0.75rem;
  margin-right: 0.75rem;
}

.mx-4 {
  margin-left: 1rem;
  margin-right: 1rem;
}

.mx-\[24px\] {
  margin-left: 24px;
  margin-right: 24px;
}

.mx-auto {
  margin-left: auto;
  margin-right: auto;
}

.my-0 {
  margin-top: 0px;
  margin-bottom: 0px;
}

.my-1 {
  margin-top: 0.25rem;
  margin-bottom: 0.25rem;
}

.my-10 {
  margin-top: 2.5rem;
  margin-bottom: 2.5rem;
}

.my-2 {
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
}

.my-3 {
  margin-top: 0.75rem;
  margin-bottom: 0.75rem;
}

.my-32 {
  margin-top: 8rem;
  margin-bottom: 8rem;
}

.my-4 {
  margin-top: 1rem;
  margin-bottom: 1rem;
}

.my-5 {
  margin-top: 1.25rem;
  margin-bottom: 1.25rem;
}

.my-6 {
  margin-top: 1.5rem;
  margin-bottom: 1.5rem;
}

.my-7 {
  margin-top: 1.75rem;
  margin-bottom: 1.75rem;
}

.my-8 {
  margin-top: 2rem;
  margin-bottom: 2rem;
}

.my-\[36px\] {
  margin-top: 36px;
  margin-bottom: 36px;
}

.my-auto {
  margin-top: auto;
  margin-bottom: auto;
}

.\!mb-0 {
  margin-bottom: 0px !important;
}

.\!mb-3 {
  margin-bottom: 0.75rem !important;
}

.\!mt-0 {
  margin-top: 0px !important;
}

.\!mt-2 {
  margin-top: 0.5rem !important;
}

.\!mt-6 {
  margin-top: 1.5rem !important;
}

.\!mt-7 {
  margin-top: 1.75rem !important;
}

.-ml-2 {
  margin-left: -0.5rem;
}

.-ml-\[16px\] {
  margin-left: -16px;
}

.-mr-3 {
  margin-right: -0.75rem;
}

.-mr-\[16px\] {
  margin-right: -16px;
}

.-ms-28 {
  margin-inline-start: -7rem;
}

.-ms-32 {
  margin-inline-start: -8rem;
}

.-ms-60 {
  margin-inline-start: -15rem;
}

.-mt-10 {
  margin-top: -2.5rem;
}

.-mt-12 {
  margin-top: -3rem;
}

.-mt-14 {
  margin-top: -3.5rem;
}

.-mt-2 {
  margin-top: -0.5rem;
}

.-mt-20 {
  margin-top: -5rem;
}

.-mt-3 {
  margin-top: -0.75rem;
}

.-mt-7 {
  margin-top: -1.75rem;
}

.-mt-8 {
  margin-top: -2rem;
}

.mb-0 {
  margin-bottom: 0px;
}

.mb-1 {
  margin-bottom: 0.25rem;
}

.mb-10 {
  margin-bottom: 2.5rem;
}

.mb-12 {
  margin-bottom: 3rem;
}

.mb-16 {
  margin-bottom: 4rem;
}

.mb-2 {
  margin-bottom: 0.5rem;
}

.mb-20 {
  margin-bottom: 5rem;
}

.mb-24 {
  margin-bottom: 6rem;
}

.mb-3 {
  margin-bottom: 0.75rem;
}

.mb-32 {
  margin-bottom: 8rem;
}

.mb-4 {
  margin-bottom: 1rem;
}

.mb-5 {
  margin-bottom: 1.25rem;
}

.mb-6 {
  margin-bottom: 1.5rem;
}

.mb-7 {
  margin-bottom: 1.75rem;
}

.mb-8 {
  margin-bottom: 2rem;
}

.mb-9 {
  margin-bottom: 2.25rem;
}

.mb-\[14px\] {
  margin-bottom: 14px;
}

.mb-px {
  margin-bottom: 1px;
}

.me-1 {
  margin-inline-end: 0.25rem;
}

.me-2 {
  margin-inline-end: 0.5rem;
}

.me-3 {
  margin-inline-end: 0.75rem;
}

.me-4 {
  margin-inline-end: 1rem;
}

.me-auto {
  margin-inline-end: auto;
}

.ml-0 {
  margin-left: 0px;
}

.ml-0\.5 {
  margin-left: 0.125rem;
}

.ml-1 {
  margin-left: 0.25rem;
}

.ml-2 {
  margin-left: 0.5rem;
}

.ml-3 {
  margin-left: 0.75rem;
}

.ml-4 {
  margin-left: 1rem;
}

.ml-5 {
  margin-left: 1.25rem;
}

.ml-6 {
  margin-left: 1.5rem;
}

.ml-auto {
  margin-left: auto;
}

.mr-0 {
  margin-right: 0px;
}

.mr-1 {
  margin-right: 0.25rem;
}

.mr-1\.5 {
  margin-right: 0.375rem;
}

.mr-2 {
  margin-right: 0.5rem;
}

.mr-3 {
  margin-right: 0.75rem;
}

.mr-4 {
  margin-right: 1rem;
}

.mr-5 {
  margin-right: 1.25rem;
}

.ms-1 {
  margin-inline-start: 0.25rem;
}

.ms-10 {
  margin-inline-start: 2.5rem;
}

.ms-2 {
  margin-inline-start: 0.5rem;
}

.ms-3 {
  margin-inline-start: 0.75rem;
}

.ms-5 {
  margin-inline-start: 1.25rem;
}

.ms-auto {
  margin-inline-start: auto;
}

.mt-0 {
  margin-top: 0px;
}

.mt-1 {
  margin-top: 0.25rem;
}

.mt-1\.5 {
  margin-top: 0.375rem;
}

.mt-10 {
  margin-top: 2.5rem;
}

.mt-16 {
  margin-top: 4rem;
}

.mt-2 {
  margin-top: 0.5rem;
}

.mt-20 {
  margin-top: 5rem;
}

.mt-24 {
  margin-top: 6rem;
}

.mt-3 {
  margin-top: 0.75rem;
}

.mt-4 {
  margin-top: 1rem;
}

.mt-5 {
  margin-top: 1.25rem;
}

.mt-6 {
  margin-top: 1.5rem;
}

.mt-7 {
  margin-top: 1.75rem;
}

.mt-8 {
  margin-top: 2rem;
}

.mt-\[16px\] {
  margin-top: 16px;
}

.mt-\[24px\] {
  margin-top: 24px;
}

.mt-\[4px\] {
  margin-top: 4px;
}

.mt-\[8px\] {
  margin-top: 8px;
}

.mt-auto {
  margin-top: auto;
}

.box-border {
  box-sizing: border-box;
}

.line-clamp-1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}

.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.line-clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}

.\!block {
  display: block !important;
}

.block {
  display: block;
}

.inline-block {
  display: inline-block;
}

.inline {
  display: inline;
}

.flex {
  display: flex;
}

.inline-flex {
  display: inline-flex;
}

.table {
  display: table;
}

.table-caption {
  display: table-caption;
}

.table-cell {
  display: table-cell;
}

.\!grid {
  display: grid !important;
}

.grid {
  display: grid;
}

.contents {
  display: contents;
}

.list-item {
  display: list-item;
}

.\!hidden {
  display: none !important;
}

.hidden {
  display: none;
}

.aspect-\[375\/156\] {
  aspect-ratio: 375/156;
}

.aspect-\[98\/131\] {
  aspect-ratio: 98/131;
}

.aspect-square {
  aspect-ratio: 1 / 1;
}

.size-1 {
  width: 0.25rem;
  height: 0.25rem;
}

.size-12 {
  width: 3rem;
  height: 3rem;
}

.size-3 {
  width: 0.75rem;
  height: 0.75rem;
}

.size-4 {
  width: 1rem;
  height: 1rem;
}

.size-5 {
  width: 1.25rem;
  height: 1.25rem;
}

.size-8 {
  width: 2rem;
  height: 2rem;
}

.size-9 {
  width: 2.25rem;
  height: 2.25rem;
}

.size-full {
  width: 100%;
  height: 100%;
}

.\!h-3 {
  height: 0.75rem !important;
}

.\!h-6 {
  height: 1.5rem !important;
}

.\!h-9 {
  height: 2.25rem !important;
}

.h-0 {
  height: 0px;
}

.h-0\.5 {
  height: 0.125rem;
}

.h-1 {
  height: 0.25rem;
}

.h-10 {
  height: 2.5rem;
}

.h-11 {
  height: 2.75rem;
}

.h-12 {
  height: 3rem;
}

.h-14 {
  height: 3.5rem;
}

.h-16 {
  height: 4rem;
}

.h-18 {
  height: 4.5rem;
}

.h-2 {
  height: 0.5rem;
}

.h-20 {
  height: 5rem;
}

.h-24 {
  height: 6rem;
}

.h-3 {
  height: 0.75rem;
}

.h-3\.5 {
  height: 0.875rem;
}

.h-32 {
  height: 8rem;
}

.h-36 {
  height: 9rem;
}

.h-4 {
  height: 1rem;
}

.h-40 {
  height: 10rem;
}

.h-44 {
  height: 11rem;
}

.h-48 {
  height: 12rem;
}

.h-5 {
  height: 1.25rem;
}

.h-6 {
  height: 1.5rem;
}

.h-7 {
  height: 1.75rem;
}

.h-8 {
  height: 2rem;
}

.h-80 {
  height: 20rem;
}

.h-9 {
  height: 2.25rem;
}

.h-96 {
  height: 24rem;
}

.h-\[12px\] {
  height: 12px;
}

.h-\[16px\] {
  height: 16px;
}

.h-\[200px\] {
  height: 200px;
}

.h-\[24px\] {
  height: 24px;
}

.h-\[32px\] {
  height: 32px;
}

.h-\[40px\] {
  height: 40px;
}

.h-\[4px\] {
  height: 4px;
}

.h-\[56px\] {
  height: 56px;
}

.h-\[64px\] {
  height: 64px;
}

.h-\[calc\(100\%-72px\)\] {
  height: calc(100% - 72px);
}

.h-auto {
  height: auto;
}

.h-fit {
  height: -moz-fit-content;
  height: fit-content;
}

.h-full {
  height: 100%;
}

.h-px {
  height: 1px;
}

.h-screen {
  height: 100vh;
}

.max-h-20 {
  max-height: 5rem;
}

.max-h-40 {
  max-height: 10rem;
}

.max-h-52 {
  max-height: 13rem;
}

.max-h-56 {
  max-height: 14rem;
}

.max-h-6 {
  max-height: 1.5rem;
}

.max-h-60 {
  max-height: 15rem;
}

.max-h-80 {
  max-height: 20rem;
}

.max-h-96 {
  max-height: 24rem;
}

.max-h-\[44px\] {
  max-height: 44px;
}

.max-h-\[95px\] {
  max-height: 95px;
}

.min-h-0 {
  min-height: 0px;
}

.min-h-12 {
  min-height: 3rem;
}

.min-h-16 {
  min-height: 4rem;
}

.min-h-28 {
  min-height: 7rem;
}

.min-h-52 {
  min-height: 13rem;
}

.min-h-6 {
  min-height: 1.5rem;
}

.min-h-60 {
  min-height: 15rem;
}

.min-h-9 {
  min-height: 2.25rem;
}

.min-h-96 {
  min-height: 24rem;
}

.min-h-\[150px\] {
  min-height: 150px;
}

.min-h-\[2\.5rem\] {
  min-height: 2.5rem;
}

.min-h-\[320px\] {
  min-height: 320px;
}

.min-h-\[380px\] {
  min-height: 380px;
}

.min-h-\[65px\] {
  min-height: 65px;
}

.min-h-full {
  min-height: 100%;
}

.min-h-home {
  min-height: calc(100vh - 80px);
}

.min-h-screen {
  min-height: 100vh;
}

.\!w-1 {
  width: 0.25rem !important;
}

.\!w-2 {
  width: 0.5rem !important;
}

.\!w-3 {
  width: 0.75rem !important;
}

.w-0 {
  width: 0px;
}

.w-1 {
  width: 0.25rem;
}

.w-1\/2 {
  width: 50%;
}

.w-1\/3 {
  width: 33.333333%;
}

.w-1\/6 {
  width: 16.666667%;
}

.w-10 {
  width: 2.5rem;
}

.w-11 {
  width: 2.75rem;
}

.w-12 {
  width: 3rem;
}

.w-14 {
  width: 3.5rem;
}

.w-16 {
  width: 4rem;
}

.w-18 {
  width: 4.5rem;
}

.w-2 {
  width: 0.5rem;
}

.w-2\/3 {
  width: 66.666667%;
}

.w-2\/5 {
  width: 40%;
}

.w-20 {
  width: 5rem;
}

.w-24 {
  width: 6rem;
}

.w-28 {
  width: 7rem;
}

.w-3 {
  width: 0.75rem;
}

.w-3\.5 {
  width: 0.875rem;
}

.w-3\/4 {
  width: 75%;
}

.w-32 {
  width: 8rem;
}

.w-4 {
  width: 1rem;
}

.w-4\/5 {
  width: 80%;
}

.w-44 {
  width: 11rem;
}

.w-48 {
  width: 12rem;
}

.w-5 {
  width: 1.25rem;
}

.w-52 {
  width: 13rem;
}

.w-56 {
  width: 14rem;
}

.w-6 {
  width: 1.5rem;
}

.w-64 {
  width: 16rem;
}

.w-7 {
  width: 1.75rem;
}

.w-72 {
  width: 18rem;
}

.w-8 {
  width: 2rem;
}

.w-80 {
  width: 20rem;
}

.w-9 {
  width: 2.25rem;
}

.w-96 {
  width: 24rem;
}

.w-\[120px\] {
  width: 120px;
}

.w-\[12px\] {
  width: 12px;
}

.w-\[16px\] {
  width: 16px;
}

.w-\[18px\] {
  width: 18px;
}

.w-\[1px\] {
  width: 1px;
}

.w-\[200px\] {
  width: 200px;
}

.w-\[230px\] {
  width: 230px;
}

.w-\[24px\] {
  width: 24px;
}

.w-\[28px\] {
  width: 28px;
}

.w-\[32px\] {
  width: 32px;
}

.w-\[36px\] {
  width: 36px;
}

.w-\[371px\] {
  width: 371px;
}

.w-\[40px\] {
  width: 40px;
}

.w-\[80\%\] {
  width: 80%;
}

.w-\[85\%\] {
  width: 85%;
}

.w-\[93\.333333\%\] {
  width: 93.333333%;
}

.w-\[98px\] {
  width: 98px;
}

.w-auto {
  width: auto;
}

.w-fit {
  width: -moz-fit-content;
  width: fit-content;
}

.w-full {
  width: 100%;
}

.w-max {
  width: -moz-max-content;
  width: max-content;
}

.w-screen {
  width: 100vw;
}

.min-w-20 {
  min-width: 5rem;
}

.min-w-\[12rem\] {
  min-width: 12rem;
}

.min-w-\[20rem\] {
  min-width: 20rem;
}

.min-w-full {
  min-width: 100%;
}

.min-w-max {
  min-width: -moz-max-content;
  min-width: max-content;
}

.max-w-12 {
  max-width: 3rem;
}

.max-w-2xl {
  max-width: 42rem;
}

.max-w-3xl {
  max-width: 48rem;
}

.max-w-4xl {
  max-width: 56rem;
}

.max-w-52 {
  max-width: 13rem;
}

.max-w-5xl {
  max-width: 64rem;
}

.max-w-7xl {
  max-width: 80rem;
}

.max-w-fit {
  max-width: -moz-fit-content;
  max-width: fit-content;
}

.max-w-full {
  max-width: 100%;
}

.max-w-lg {
  max-width: 32rem;
}

.max-w-md {
  max-width: 28rem;
}

.max-w-none {
  max-width: none;
}

.max-w-sm {
  max-width: 24rem;
}

.max-w-xl {
  max-width: 36rem;
}

.max-w-xs {
  max-width: 20rem;
}

.flex-1 {
  flex: 1 1 0%;
}

.flex-auto {
  flex: 1 1 auto;
}

.flex-initial {
  flex: 0 1 auto;
}

.flex-none {
  flex: none;
}

.flex-shrink {
  flex-shrink: 1;
}

.flex-shrink-0 {
  flex-shrink: 0;
}

.shrink {
  flex-shrink: 1;
}

.shrink-0 {
  flex-shrink: 0;
}

.flex-grow {
  flex-grow: 1;
}

.flex-grow-0 {
  flex-grow: 0;
}

.grow {
  flex-grow: 1;
}

.table-auto {
  table-layout: auto;
}

.table-fixed {
  table-layout: fixed;
}

.caption-bottom {
  caption-side: bottom;
}

.border-collapse {
  border-collapse: collapse;
}

.origin-bottom-right {
  transform-origin: bottom right;
}

.origin-center {
  transform-origin: center;
}

.origin-top-right {
  transform-origin: top right;
}

.-translate-x-0 {
  --tw-translate-x: -0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-translate-x-1 {
  --tw-translate-x: -0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-translate-x-1\/2 {
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-translate-x-\[50\%\] {
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-translate-x-full {
  --tw-translate-x: -100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-translate-y-0 {
  --tw-translate-y: -0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-translate-y-1 {
  --tw-translate-y: -0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-translate-y-1\/2 {
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-translate-y-2 {
  --tw-translate-y: -0.5rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-translate-y-8 {
  --tw-translate-y: -2rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-translate-y-\[50\%\] {
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-translate-y-full {
  --tw-translate-y: -100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-x-0 {
  --tw-translate-x: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-x-1 {
  --tw-translate-x: 0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-x-2 {
  --tw-translate-x: 0.5rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-x-full {
  --tw-translate-x: 100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-y-0 {
  --tw-translate-y: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-y-40 {
  --tw-translate-y: 10rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-y-\[-10px\] {
  --tw-translate-y: -10px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-y-full {
  --tw-translate-y: 100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.\!rotate-90 {
  --tw-rotate: 90deg !important;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)) !important;
}

.-rotate-12 {
  --tw-rotate: -12deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-rotate-45 {
  --tw-rotate: -45deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.rotate-0 {
  --tw-rotate: 0deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.rotate-12 {
  --tw-rotate: 12deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.rotate-180 {
  --tw-rotate: 180deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.rotate-45 {
  --tw-rotate: 45deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.rotate-90 {
  --tw-rotate: 90deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-skew-y-1 {
  --tw-skew-y: -1deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.scale-0 {
  --tw-scale-x: 0;
  --tw-scale-y: 0;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.scale-100 {
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.scale-105 {
  --tw-scale-x: 1.05;
  --tw-scale-y: 1.05;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.scale-110 {
  --tw-scale-x: 1.1;
  --tw-scale-y: 1.1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.scale-125 {
  --tw-scale-x: 1.25;
  --tw-scale-y: 1.25;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.scale-90 {
  --tw-scale-x: .9;
  --tw-scale-y: .9;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.scale-95 {
  --tw-scale-x: .95;
  --tw-scale-y: .95;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.scale-x-0 {
  --tw-scale-x: 0;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.scale-x-100 {
  --tw-scale-x: 1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.transform {
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

@keyframes bounce {
  0%, 100% {
    transform: translateY(-25%);
    animation-timing-function: cubic-bezier(0.8,0,1,1);
  }

  50% {
    transform: none;
    animation-timing-function: cubic-bezier(0,0,0.2,1);
  }
}

.animate-bounce {
  animation: bounce 1s infinite;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }

  50% {
    transform: translateY(-10px);
  }
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

@keyframes glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(99, 102, 241, 0.3);
  }

  50% {
    box-shadow: 0 0 30px rgba(99, 102, 241, 0.6);
  }
}

.animate-glow {
  animation: glow 2s ease-in-out infinite;
}

@keyframes ping {
  75%, 100% {
    transform: scale(2);
    opacity: 0;
  }
}

.animate-ping {
  animation: ping 1s cubic-bezier(0, 0, 0.2, 1) infinite;
}

@keyframes pulse {
  50% {
    opacity: .5;
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(99, 102, 241, 0.4);
    border-color: rgba(99, 102, 241, 0.6);
  }

  50% {
    box-shadow: 0 0 40px rgba(99, 102, 241, 0.8);
    border-color: rgba(99, 102, 241, 1);
  }
}

.animate-pulse-glow {
  animation: pulse-glow 3s ease-in-out infinite;
}

@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.animate-rotate {
  animation: rotate 8s linear infinite;
}

@keyframes shimmer {
  0% {
    left: -100%;
    transform: translateX(-100%);
  }

  100% {
    left: 100%;
    transform: translateX(100%);
  }
}

.animate-shimmer {
  animation: shimmer 2.5s infinite linear;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

.\!cursor-not-allowed {
  cursor: not-allowed !important;
}

.cursor-auto {
  cursor: auto;
}

.cursor-default {
  cursor: default;
}

.cursor-grab {
  cursor: grab;
}

.cursor-move {
  cursor: move;
}

.cursor-not-allowed {
  cursor: not-allowed;
}

.cursor-pointer {
  cursor: pointer;
}

.cursor-text {
  cursor: text;
}

.select-none {
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}

.select-all {
  -webkit-user-select: all;
     -moz-user-select: all;
          user-select: all;
}

.resize-none {
  resize: none;
}

.resize {
  resize: both;
}

.snap-x {
  scroll-snap-type: x var(--tw-scroll-snap-strictness);
}

.snap-mandatory {
  --tw-scroll-snap-strictness: mandatory;
}

.snap-start {
  scroll-snap-align: start;
}

.snap-center {
  scroll-snap-align: center;
}

.list-inside {
  list-style-position: inside;
}

.list-decimal {
  list-style-type: decimal;
}

.list-disc {
  list-style-type: disc;
}

.appearance-none {
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
}

.appearance-auto {
  -webkit-appearance: auto;
     -moz-appearance: auto;
          appearance: auto;
}

.grid-flow-row {
  grid-auto-flow: row;
}

.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

.grid-cols-12 {
  grid-template-columns: repeat(12, minmax(0, 1fr));
}

.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}

.grid-cols-3 {
  grid-template-columns: repeat(3, minmax(0, 1fr));
}

.grid-cols-4 {
  grid-template-columns: repeat(4, minmax(0, 1fr));
}

.grid-cols-5 {
  grid-template-columns: repeat(5, minmax(0, 1fr));
}

.grid-cols-6 {
  grid-template-columns: repeat(6, minmax(0, 1fr));
}

.grid-cols-7 {
  grid-template-columns: repeat(7, minmax(0, 1fr));
}

.grid-cols-8 {
  grid-template-columns: repeat(8, minmax(0, 1fr));
}

.grid-rows-1 {
  grid-template-rows: repeat(1, minmax(0, 1fr));
}

.grid-rows-4 {
  grid-template-rows: repeat(4, minmax(0, 1fr));
}

.grid-rows-6 {
  grid-template-rows: repeat(6, minmax(0, 1fr));
}

.flex-row {
  flex-direction: row;
}

.flex-row-reverse {
  flex-direction: row-reverse;
}

.flex-col {
  flex-direction: column;
}

.flex-col-reverse {
  flex-direction: column-reverse;
}

.flex-wrap {
  flex-wrap: wrap;
}

.flex-wrap-reverse {
  flex-wrap: wrap-reverse;
}

.\!flex-nowrap {
  flex-wrap: nowrap !important;
}

.flex-nowrap {
  flex-wrap: nowrap;
}

.place-content-center {
  place-content: center;
}

.place-items-center {
  place-items: center;
}

.content-center {
  align-content: center;
}

.content-between {
  align-content: space-between;
}

.items-start {
  align-items: flex-start;
}

.items-end {
  align-items: flex-end;
}

.\!items-center {
  align-items: center !important;
}

.items-center {
  align-items: center;
}

.items-baseline {
  align-items: baseline;
}

.items-stretch {
  align-items: stretch;
}

.justify-start {
  justify-content: flex-start;
}

.justify-end {
  justify-content: flex-end;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.justify-around {
  justify-content: space-around;
}

.justify-evenly {
  justify-content: space-evenly;
}

.justify-items-center {
  justify-items: center;
}

.gap-0 {
  gap: 0px;
}

.gap-1 {
  gap: 0.25rem;
}

.gap-1\.5 {
  gap: 0.375rem;
}

.gap-10 {
  gap: 2.5rem;
}

.gap-12 {
  gap: 3rem;
}

.gap-14 {
  gap: 3.5rem;
}

.gap-2 {
  gap: 0.5rem;
}

.gap-20 {
  gap: 5rem;
}

.gap-3 {
  gap: 0.75rem;
}

.gap-4 {
  gap: 1rem;
}

.gap-5 {
  gap: 1.25rem;
}

.gap-6 {
  gap: 1.5rem;
}

.gap-7 {
  gap: 1.75rem;
}

.gap-8 {
  gap: 2rem;
}

.gap-\[12px\] {
  gap: 12px;
}

.gap-\[16px\] {
  gap: 16px;
}

.gap-\[24px\] {
  gap: 24px;
}

.gap-\[4px\] {
  gap: 4px;
}

.gap-\[8px\] {
  gap: 8px;
}

.gap-x-12 {
  -moz-column-gap: 3rem;
       column-gap: 3rem;
}

.gap-x-2 {
  -moz-column-gap: 0.5rem;
       column-gap: 0.5rem;
}

.gap-x-2\.5 {
  -moz-column-gap: 0.625rem;
       column-gap: 0.625rem;
}

.gap-x-3 {
  -moz-column-gap: 0.75rem;
       column-gap: 0.75rem;
}

.gap-x-3\.5 {
  -moz-column-gap: 0.875rem;
       column-gap: 0.875rem;
}

.gap-x-4 {
  -moz-column-gap: 1rem;
       column-gap: 1rem;
}

.gap-x-5 {
  -moz-column-gap: 1.25rem;
       column-gap: 1.25rem;
}

.gap-x-6 {
  -moz-column-gap: 1.5rem;
       column-gap: 1.5rem;
}

.gap-x-\[24px\] {
  -moz-column-gap: 24px;
       column-gap: 24px;
}

.gap-y-1 {
  row-gap: 0.25rem;
}

.gap-y-2 {
  row-gap: 0.5rem;
}

.gap-y-3 {
  row-gap: 0.75rem;
}

.gap-y-4 {
  row-gap: 1rem;
}

.-space-x-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(-0.5rem * var(--tw-space-x-reverse));
  margin-left: calc(-0.5rem * calc(1 - var(--tw-space-x-reverse)));
}

.-space-x-3 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(-0.75rem * var(--tw-space-x-reverse));
  margin-left: calc(-0.75rem * calc(1 - var(--tw-space-x-reverse)));
}

.-space-x-4 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(-1rem * var(--tw-space-x-reverse));
  margin-left: calc(-1rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-x-1 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.25rem * var(--tw-space-x-reverse));
  margin-left: calc(0.25rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-x-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.5rem * var(--tw-space-x-reverse));
  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-x-3 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.75rem * var(--tw-space-x-reverse));
  margin-left: calc(0.75rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-x-4 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(1rem * var(--tw-space-x-reverse));
  margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-y-0 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0px * var(--tw-space-y-reverse));
}

.space-y-1 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.25rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.25rem * var(--tw-space-y-reverse));
}

.space-y-14 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(3.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(3.5rem * var(--tw-space-y-reverse));
}

.space-y-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));
}

.space-y-3 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));
}

.space-y-4 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1rem * var(--tw-space-y-reverse));
}

.space-y-5 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1.25rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1.25rem * var(--tw-space-y-reverse));
}

.space-y-6 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));
}

.space-y-7 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1.75rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1.75rem * var(--tw-space-y-reverse));
}

.space-y-8 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(2rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(2rem * var(--tw-space-y-reverse));
}

.space-y-9 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(2.25rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(2.25rem * var(--tw-space-y-reverse));
}

.divide-y > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-y-reverse: 0;
  border-top-width: calc(1px * calc(1 - var(--tw-divide-y-reverse)));
  border-bottom-width: calc(1px * var(--tw-divide-y-reverse));
}

.divide-white > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-opacity: 1;
  border-color: rgb(255 255 255 / var(--tw-divide-opacity, 1));
}

.self-end {
  align-self: flex-end;
}

.self-center {
  align-self: center;
}

.overflow-auto {
  overflow: auto;
}

.overflow-hidden {
  overflow: hidden;
}

.overflow-visible {
  overflow: visible;
}

.overflow-scroll {
  overflow: scroll;
}

.overflow-x-auto {
  overflow-x: auto;
}

.overflow-y-auto {
  overflow-y: auto;
}

.overflow-x-hidden {
  overflow-x: hidden;
}

.overflow-y-hidden {
  overflow-y: hidden;
}

.overflow-x-scroll {
  overflow-x: scroll;
}

.overflow-y-scroll {
  overflow-y: scroll;
}

.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.text-ellipsis {
  text-overflow: ellipsis;
}

.whitespace-normal {
  white-space: normal;
}

.whitespace-nowrap {
  white-space: nowrap;
}

.whitespace-pre {
  white-space: pre;
}

.whitespace-pre-wrap {
  white-space: pre-wrap;
}

.text-wrap {
  text-wrap: wrap;
}

.text-nowrap {
  text-wrap: nowrap;
}

.break-words {
  overflow-wrap: break-word;
}

.break-all {
  word-break: break-all;
}

.rounded {
  border-radius: 0.25rem;
}

.rounded-2xl {
  border-radius: 1rem;
}

.rounded-3xl {
  border-radius: 1.5rem;
}

.rounded-\[100px\] {
  border-radius: 100px;
}

.rounded-\[10px\] {
  border-radius: 10px;
}

.rounded-\[12px\] {
  border-radius: 12px;
}

.rounded-\[16px\] {
  border-radius: 16px;
}

.rounded-\[24px\] {
  border-radius: 24px;
}

.rounded-\[48px\] {
  border-radius: 48px;
}

.rounded-\[8px\] {
  border-radius: 8px;
}

.rounded-full {
  border-radius: 9999px;
}

.rounded-lg {
  border-radius: 0.5rem;
}

.rounded-md {
  border-radius: 0.375rem;
}

.rounded-none {
  border-radius: 0px;
}

.rounded-sm {
  border-radius: 0.125rem;
}

.rounded-xl {
  border-radius: 0.75rem;
}

.rounded-b-3xl {
  border-bottom-right-radius: 1.5rem;
  border-bottom-left-radius: 1.5rem;
}

.rounded-b-md {
  border-bottom-right-radius: 0.375rem;
  border-bottom-left-radius: 0.375rem;
}

.rounded-b-none {
  border-bottom-right-radius: 0px;
  border-bottom-left-radius: 0px;
}

.rounded-e-3xl {
  border-start-end-radius: 1.5rem;
  border-end-end-radius: 1.5rem;
}

.rounded-r-full {
  border-top-right-radius: 9999px;
  border-bottom-right-radius: 9999px;
}

.rounded-s-3xl {
  border-start-start-radius: 1.5rem;
  border-end-start-radius: 1.5rem;
}

.rounded-t-3xl {
  border-top-left-radius: 1.5rem;
  border-top-right-radius: 1.5rem;
}

.rounded-t-md {
  border-top-left-radius: 0.375rem;
  border-top-right-radius: 0.375rem;
}

.rounded-t-none {
  border-top-left-radius: 0px;
  border-top-right-radius: 0px;
}

.rounded-bl-\[8px\] {
  border-bottom-left-radius: 8px;
}

.rounded-bl-full {
  border-bottom-left-radius: 9999px;
}

.rounded-bl-md {
  border-bottom-left-radius: 0.375rem;
}

.rounded-br-md {
  border-bottom-right-radius: 0.375rem;
}

.rounded-tl {
  border-top-left-radius: 0.25rem;
}

.rounded-tl-\[8px\] {
  border-top-left-radius: 8px;
}

.rounded-tl-md {
  border-top-left-radius: 0.375rem;
}

.rounded-tl-none {
  border-top-left-radius: 0px;
}

.rounded-tr {
  border-top-right-radius: 0.25rem;
}

.rounded-tr-md {
  border-top-right-radius: 0.375rem;
}

.\!border {
  border-width: 1px !important;
}

.border {
  border-width: 1px;
}

.border-0 {
  border-width: 0px;
}

.border-2 {
  border-width: 2px;
}

.border-4 {
  border-width: 4px;
}

.border-8 {
  border-width: 8px;
}

.border-x-4 {
  border-left-width: 4px;
  border-right-width: 4px;
}

.border-y {
  border-top-width: 1px;
  border-bottom-width: 1px;
}

.\!border-t-0 {
  border-top-width: 0px !important;
}

.border-b {
  border-bottom-width: 1px;
}

.border-b-0 {
  border-bottom-width: 0px;
}

.border-b-2 {
  border-bottom-width: 2px;
}

.border-b-4 {
  border-bottom-width: 4px;
}

.border-e {
  border-inline-end-width: 1px;
}

.border-l {
  border-left-width: 1px;
}

.border-l-0 {
  border-left-width: 0px;
}

.border-l-2 {
  border-left-width: 2px;
}

.border-l-4 {
  border-left-width: 4px;
}

.border-r {
  border-right-width: 1px;
}

.border-r-0 {
  border-right-width: 0px;
}

.border-r-2 {
  border-right-width: 2px;
}

.border-t {
  border-top-width: 1px;
}

.border-t-0 {
  border-top-width: 0px;
}

.border-t-2 {
  border-top-width: 2px;
}

.border-t-4 {
  border-top-width: 4px;
}

.border-dashed {
  border-style: dashed;
}

.\!border-none {
  border-style: none !important;
}

.border-none {
  border-style: none;
}

.border-\[\#00FF88\] {
  --tw-border-opacity: 1;
  border-color: rgb(0 255 136 / var(--tw-border-opacity, 1));
}

.border-\[\#00FF88\]\/30 {
  border-color: rgb(0 255 136 / 0.3);
}

.border-\[\#00FFFF\]\/60 {
  border-color: rgb(0 255 255 / 0.6);
}

.border-\[\#06B6D4\] {
  --tw-border-opacity: 1;
  border-color: rgb(6 182 212 / var(--tw-border-opacity, 1));
}

.border-\[\#06B6D4\]\/80 {
  border-color: rgb(6 182 212 / 0.8);
}

.border-\[\#10B981\] {
  --tw-border-opacity: 1;
  border-color: rgb(16 185 129 / var(--tw-border-opacity, 1));
}

.border-\[\#13112E\] {
  --tw-border-opacity: 1;
  border-color: rgb(19 17 46 / var(--tw-border-opacity, 1));
}

.border-\[\#3463DB\] {
  --tw-border-opacity: 1;
  border-color: rgb(52 99 219 / var(--tw-border-opacity, 1));
}

.border-\[\#4B7DFF\]\/30 {
  border-color: rgb(75 125 255 / 0.3);
}

.border-\[\#4B7DFF\]\/60 {
  border-color: rgb(75 125 255 / 0.6);
}

.border-\[\#5081FF33\] {
  border-color: #5081FF33;
}

.border-\[\#5081FF\] {
  --tw-border-opacity: 1;
  border-color: rgb(80 129 255 / var(--tw-border-opacity, 1));
}

.border-\[\#5081FF\]\/20 {
  border-color: rgb(80 129 255 / 0.2);
}

.border-\[\#5081FF\]\/30 {
  border-color: rgb(80 129 255 / 0.3);
}

.border-\[\#5081FF\]\/50 {
  border-color: rgb(80 129 255 / 0.5);
}

.border-\[\#5081FF\]\/60 {
  border-color: rgb(80 129 255 / 0.6);
}

.border-\[\#5081ff33\] {
  border-color: #5081ff33;
}

.border-\[\#6366F1\] {
  --tw-border-opacity: 1;
  border-color: rgb(99 102 241 / var(--tw-border-opacity, 1));
}

.border-\[\#6366F1\]\/30 {
  border-color: rgb(99 102 241 / 0.3);
}

.border-\[\#6366F1\]\/40 {
  border-color: rgb(99 102 241 / 0.4);
}

.border-\[\#6366F1\]\/80 {
  border-color: rgb(99 102 241 / 0.8);
}

.border-\[\#8B5CF6\] {
  --tw-border-opacity: 1;
  border-color: rgb(139 92 246 / var(--tw-border-opacity, 1));
}

.border-\[\#9C27B0\]\/30 {
  border-color: rgb(156 39 176 / 0.3);
}

.border-\[\#EF4444\] {
  --tw-border-opacity: 1;
  border-color: rgb(239 68 68 / var(--tw-border-opacity, 1));
}

.border-\[\#F59E0B\] {
  --tw-border-opacity: 1;
  border-color: rgb(245 158 11 / var(--tw-border-opacity, 1));
}

.border-\[\#FF6B6B\] {
  --tw-border-opacity: 1;
  border-color: rgb(255 107 107 / var(--tw-border-opacity, 1));
}

.border-\[\#FF6B6B\]\/30 {
  border-color: rgb(255 107 107 / 0.3);
}

.border-\[\#FFD700\]\/30 {
  border-color: rgb(255 215 0 / 0.3);
}

.border-\[\#FFFFFF1F\] {
  border-color: #FFFFFF1F;
}

.border-\[\#ff062e\]\/30 {
  border-color: rgb(255 6 46 / 0.3);
}

.border-black {
  --tw-border-opacity: 1;
  border-color: rgb(0 0 0 / var(--tw-border-opacity, 1));
}

.border-blue-400 {
  --tw-border-opacity: 1;
  border-color: rgb(96 165 250 / var(--tw-border-opacity, 1));
}

.border-blue-500 {
  --tw-border-opacity: 1;
  border-color: rgb(59 130 246 / var(--tw-border-opacity, 1));
}

.border-blue-500\/50 {
  border-color: rgb(59 130 246 / 0.5);
}

.border-emerald-500 {
  --tw-border-opacity: 1;
  border-color: rgb(16 185 129 / var(--tw-border-opacity, 1));
}

.border-emerald-500\/50 {
  border-color: rgb(16 185 129 / 0.5);
}

.border-gaming-blue {
  --tw-border-opacity: 1;
  border-color: rgb(80 129 255 / var(--tw-border-opacity, 1));
}

.border-gaming-blue\/20 {
  border-color: rgb(80 129 255 / 0.2);
}

.border-gaming-blue\/30 {
  border-color: rgb(80 129 255 / 0.3);
}

.border-gaming-dark {
  --tw-border-opacity: 1;
  border-color: rgb(15 23 42 / var(--tw-border-opacity, 1));
}

.border-gaming-deep-blue {
  --tw-border-opacity: 1;
  border-color: rgb(52 99 219 / var(--tw-border-opacity, 1));
}

.border-gaming-gold {
  --tw-border-opacity: 1;
  border-color: rgb(255 215 0 / var(--tw-border-opacity, 1));
}

.border-gaming-green {
  --tw-border-opacity: 1;
  border-color: rgb(0 255 136 / var(--tw-border-opacity, 1));
}

.border-gaming-light-blue {
  --tw-border-opacity: 1;
  border-color: rgb(75 125 255 / var(--tw-border-opacity, 1));
}

.border-gaming-navy {
  --tw-border-opacity: 1;
  border-color: rgb(19 17 46 / var(--tw-border-opacity, 1));
}

.border-gaming-orange {
  --tw-border-opacity: 1;
  border-color: rgb(255 140 0 / var(--tw-border-opacity, 1));
}

.border-gaming-purple {
  --tw-border-opacity: 1;
  border-color: rgb(39 36 80 / var(--tw-border-opacity, 1));
}

.border-gaming-red {
  --tw-border-opacity: 1;
  border-color: rgb(255 107 107 / var(--tw-border-opacity, 1));
}

.border-gaming-slate {
  --tw-border-opacity: 1;
  border-color: rgb(30 41 59 / var(--tw-border-opacity, 1));
}

.border-gaming-violet {
  --tw-border-opacity: 1;
  border-color: rgb(156 39 176 / var(--tw-border-opacity, 1));
}

.border-gray-200 {
  --tw-border-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));
}

.border-gray-300 {
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));
}

.border-gray-400 {
  --tw-border-opacity: 1;
  border-color: rgb(156 163 175 / var(--tw-border-opacity, 1));
}

.border-gray-500 {
  --tw-border-opacity: 1;
  border-color: rgb(107 114 128 / var(--tw-border-opacity, 1));
}

.border-gray-600 {
  --tw-border-opacity: 1;
  border-color: rgb(75 85 99 / var(--tw-border-opacity, 1));
}

.border-gray-600\/30 {
  border-color: rgb(75 85 99 / 0.3);
}

.border-gray-600\/50 {
  border-color: rgb(75 85 99 / 0.5);
}

.border-gray-700 {
  --tw-border-opacity: 1;
  border-color: rgb(55 65 81 / var(--tw-border-opacity, 1));
}

.border-green-500 {
  --tw-border-opacity: 1;
  border-color: rgb(34 197 94 / var(--tw-border-opacity, 1));
}

.border-green-500\/30 {
  border-color: rgb(34 197 94 / 0.3);
}

.border-mandy-500 {
  --tw-border-opacity: 1;
  border-color: rgb(229 62 62 / var(--tw-border-opacity, 1));
}

.border-mandy-500\/30 {
  border-color: rgb(229 62 62 / 0.3);
}

.border-orange-500 {
  --tw-border-opacity: 1;
  border-color: rgb(249 115 22 / var(--tw-border-opacity, 1));
}

.border-orange-500\/50 {
  border-color: rgb(249 115 22 / 0.5);
}

.border-purple-500 {
  --tw-border-opacity: 1;
  border-color: rgb(168 85 247 / var(--tw-border-opacity, 1));
}

.border-purple-500\/50 {
  border-color: rgb(168 85 247 / 0.5);
}

.border-red-500 {
  --tw-border-opacity: 1;
  border-color: rgb(239 68 68 / var(--tw-border-opacity, 1));
}

.border-red-500\/30 {
  border-color: rgb(239 68 68 / 0.3);
}

.border-teal-500 {
  --tw-border-opacity: 1;
  border-color: rgb(20 184 166 / var(--tw-border-opacity, 1));
}

.border-transparent {
  border-color: transparent;
}

.border-white {
  --tw-border-opacity: 1;
  border-color: rgb(255 255 255 / var(--tw-border-opacity, 1));
}

.border-white\/20 {
  border-color: rgb(255 255 255 / 0.2);
}

.border-white\/60 {
  border-color: rgb(255 255 255 / 0.6);
}

.border-yellow-500 {
  --tw-border-opacity: 1;
  border-color: rgb(234 179 8 / var(--tw-border-opacity, 1));
}

.border-yellow-500\/50 {
  border-color: rgb(234 179 8 / 0.5);
}

.border-y-\[\#5081ff33\] {
  border-top-color: #5081ff33;
  border-bottom-color: #5081ff33;
}

.\!border-b-white {
  --tw-border-opacity: 1 !important;
  border-bottom-color: rgb(255 255 255 / var(--tw-border-opacity, 1)) !important;
}

.\!border-l-transparent {
  border-left-color: transparent !important;
}

.border-l-black {
  --tw-border-opacity: 1;
  border-left-color: rgb(0 0 0 / var(--tw-border-opacity, 1));
}

.border-l-transparent {
  border-left-color: transparent;
}

.border-t-\[\#5081FF33\] {
  border-top-color: #5081FF33;
}

.border-t-\[\#5081ff33\] {
  border-top-color: #5081ff33;
}

.border-t-transparent {
  border-top-color: transparent;
}

.border-opacity-20 {
  --tw-border-opacity: 0.2;
}

.\!bg-transparent {
  background-color: transparent !important;
}

.bg-\[\#00000099\] {
  background-color: #00000099;
}

.bg-\[\#0068FF\] {
  --tw-bg-opacity: 1;
  background-color: rgb(0 104 255 / var(--tw-bg-opacity, 1));
}

.bg-\[\#00FFFF\] {
  --tw-bg-opacity: 1;
  background-color: rgb(0 255 255 / var(--tw-bg-opacity, 1));
}

.bg-\[\#06B6D4\] {
  --tw-bg-opacity: 1;
  background-color: rgb(6 182 212 / var(--tw-bg-opacity, 1));
}

.bg-\[\#0E0A2F\] {
  --tw-bg-opacity: 1;
  background-color: rgb(14 10 47 / var(--tw-bg-opacity, 1));
}

.bg-\[\#0E0A2F\]\/50 {
  background-color: rgb(14 10 47 / 0.5);
}

.bg-\[\#0F172A\] {
  --tw-bg-opacity: 1;
  background-color: rgb(15 23 42 / var(--tw-bg-opacity, 1));
}

.bg-\[\#10B981\] {
  --tw-bg-opacity: 1;
  background-color: rgb(16 185 129 / var(--tw-bg-opacity, 1));
}

.bg-\[\#13112E\] {
  --tw-bg-opacity: 1;
  background-color: rgb(19 17 46 / var(--tw-bg-opacity, 1));
}

.bg-\[\#1E293B\] {
  --tw-bg-opacity: 1;
  background-color: rgb(30 41 59 / var(--tw-bg-opacity, 1));
}

.bg-\[\#1E293B\]\/50 {
  background-color: rgb(30 41 59 / 0.5);
}

.bg-\[\#272450\] {
  --tw-bg-opacity: 1;
  background-color: rgb(39 36 80 / var(--tw-bg-opacity, 1));
}

.bg-\[\#2b22b3\] {
  --tw-bg-opacity: 1;
  background-color: rgb(43 34 179 / var(--tw-bg-opacity, 1));
}

.bg-\[\#3463DB\] {
  --tw-bg-opacity: 1;
  background-color: rgb(52 99 219 / var(--tw-bg-opacity, 1));
}

.bg-\[\#4B7DFF\] {
  --tw-bg-opacity: 1;
  background-color: rgb(75 125 255 / var(--tw-bg-opacity, 1));
}

.bg-\[\#4B7DFF\]\/20 {
  background-color: rgb(75 125 255 / 0.2);
}

.bg-\[\#5081FF\] {
  --tw-bg-opacity: 1;
  background-color: rgb(80 129 255 / var(--tw-bg-opacity, 1));
}

.bg-\[\#5081FF\]\/20 {
  background-color: rgb(80 129 255 / 0.2);
}

.bg-\[\#5081FF\]\/30 {
  background-color: rgb(80 129 255 / 0.3);
}

.bg-\[\#5865F2\] {
  --tw-bg-opacity: 1;
  background-color: rgb(88 101 242 / var(--tw-bg-opacity, 1));
}

.bg-\[\#6366F1\] {
  --tw-bg-opacity: 1;
  background-color: rgb(99 102 241 / var(--tw-bg-opacity, 1));
}

.bg-\[\#8B5CF6\] {
  --tw-bg-opacity: 1;
  background-color: rgb(139 92 246 / var(--tw-bg-opacity, 1));
}

.bg-\[\#EF4444\] {
  --tw-bg-opacity: 1;
  background-color: rgb(239 68 68 / var(--tw-bg-opacity, 1));
}

.bg-\[\#F59E0B\] {
  --tw-bg-opacity: 1;
  background-color: rgb(245 158 11 / var(--tw-bg-opacity, 1));
}

.bg-\[\#FFFFFF0A\] {
  background-color: #FFFFFF0A;
}

.bg-\[\#FFFFFF1F\] {
  background-color: #FFFFFF1F;
}

.bg-\[\#FFFFFF33\] {
  background-color: #FFFFFF33;
}

.bg-\[\#FFFFFF7A\] {
  background-color: #FFFFFF7A;
}

.bg-\[\#fafafa\] {
  --tw-bg-opacity: 1;
  background-color: rgb(250 250 250 / var(--tw-bg-opacity, 1));
}

.bg-\[\#fbfbfb\] {
  --tw-bg-opacity: 1;
  background-color: rgb(251 251 251 / var(--tw-bg-opacity, 1));
}

.bg-\[\#ff062e\]\/10 {
  background-color: rgb(255 6 46 / 0.1);
}

.bg-\[\#ff062e\]\/20 {
  background-color: rgb(255 6 46 / 0.2);
}

.bg-\[\#fff9\] {
  background-color: #fff9;
}

.bg-\[\#ffffff1f\] {
  background-color: #ffffff1f;
}

.bg-\[black\]\/60 {
  background-color: rgb(0 0 0 / 0.6);
}

.bg-\[rgba\(0\2c 0\2c 0\2c 0\.5\)\] {
  background-color: rgba(0,0,0,0.5);
}

.bg-black {
  --tw-bg-opacity: 1;
  background-color: rgb(0 0 0 / var(--tw-bg-opacity, 1));
}

.bg-black\/80 {
  background-color: rgb(0 0 0 / 0.8);
}

.bg-blue-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(96 165 250 / var(--tw-bg-opacity, 1));
}

.bg-blue-400\/10 {
  background-color: rgb(96 165 250 / 0.1);
}

.bg-blue-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(59 130 246 / var(--tw-bg-opacity, 1));
}

.bg-blue-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(37 99 235 / var(--tw-bg-opacity, 1));
}

.bg-blue-700 {
  --tw-bg-opacity: 1;
  background-color: rgb(29 78 216 / var(--tw-bg-opacity, 1));
}

.bg-blue-800 {
  --tw-bg-opacity: 1;
  background-color: rgb(30 64 175 / var(--tw-bg-opacity, 1));
}

.bg-emerald-300 {
  --tw-bg-opacity: 1;
  background-color: rgb(110 231 183 / var(--tw-bg-opacity, 1));
}

.bg-emerald-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(52 211 153 / var(--tw-bg-opacity, 1));
}

.bg-emerald-400\/10 {
  background-color: rgb(52 211 153 / 0.1);
}

.bg-emerald-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(236 253 245 / var(--tw-bg-opacity, 1));
}

.bg-gaming-blue {
  --tw-bg-opacity: 1;
  background-color: rgb(80 129 255 / var(--tw-bg-opacity, 1));
}

.bg-gaming-blue\/20 {
  background-color: rgb(80 129 255 / 0.2);
}

.bg-gaming-dark {
  --tw-bg-opacity: 1;
  background-color: rgb(15 23 42 / var(--tw-bg-opacity, 1));
}

.bg-gaming-deep-blue {
  --tw-bg-opacity: 1;
  background-color: rgb(52 99 219 / var(--tw-bg-opacity, 1));
}

.bg-gaming-gold {
  --tw-bg-opacity: 1;
  background-color: rgb(255 215 0 / var(--tw-bg-opacity, 1));
}

.bg-gaming-green {
  --tw-bg-opacity: 1;
  background-color: rgb(0 255 136 / var(--tw-bg-opacity, 1));
}

.bg-gaming-green\/20 {
  background-color: rgb(0 255 136 / 0.2);
}

.bg-gaming-light-blue {
  --tw-bg-opacity: 1;
  background-color: rgb(75 125 255 / var(--tw-bg-opacity, 1));
}

.bg-gaming-navy {
  --tw-bg-opacity: 1;
  background-color: rgb(19 17 46 / var(--tw-bg-opacity, 1));
}

.bg-gaming-orange {
  --tw-bg-opacity: 1;
  background-color: rgb(255 140 0 / var(--tw-bg-opacity, 1));
}

.bg-gaming-purple {
  --tw-bg-opacity: 1;
  background-color: rgb(39 36 80 / var(--tw-bg-opacity, 1));
}

.bg-gaming-purple\/20 {
  background-color: rgb(39 36 80 / 0.2);
}

.bg-gaming-red {
  --tw-bg-opacity: 1;
  background-color: rgb(255 107 107 / var(--tw-bg-opacity, 1));
}

.bg-gaming-slate {
  --tw-bg-opacity: 1;
  background-color: rgb(30 41 59 / var(--tw-bg-opacity, 1));
}

.bg-gaming-slate\/20 {
  background-color: rgb(30 41 59 / 0.2);
}

.bg-gaming-slate\/50 {
  background-color: rgb(30 41 59 / 0.5);
}

.bg-gaming-violet {
  --tw-bg-opacity: 1;
  background-color: rgb(156 39 176 / var(--tw-bg-opacity, 1));
}

.bg-gray-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));
}

.bg-gray-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1));
}

.bg-gray-300 {
  --tw-bg-opacity: 1;
  background-color: rgb(209 213 219 / var(--tw-bg-opacity, 1));
}

.bg-gray-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(156 163 175 / var(--tw-bg-opacity, 1));
}

.bg-gray-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));
}

.bg-gray-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(107 114 128 / var(--tw-bg-opacity, 1));
}

.bg-gray-500\/50 {
  background-color: rgb(107 114 128 / 0.5);
}

.bg-gray-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(75 85 99 / var(--tw-bg-opacity, 1));
}

.bg-gray-600\/50 {
  background-color: rgb(75 85 99 / 0.5);
}

.bg-gray-700 {
  --tw-bg-opacity: 1;
  background-color: rgb(55 65 81 / var(--tw-bg-opacity, 1));
}

.bg-gray-700\/20 {
  background-color: rgb(55 65 81 / 0.2);
}

.bg-gray-800 {
  --tw-bg-opacity: 1;
  background-color: rgb(31 41 55 / var(--tw-bg-opacity, 1));
}

.bg-gray-900 {
  --tw-bg-opacity: 1;
  background-color: rgb(17 24 39 / var(--tw-bg-opacity, 1));
}

.bg-gray-950 {
  --tw-bg-opacity: 1;
  background-color: rgb(3 7 18 / var(--tw-bg-opacity, 1));
}

.bg-green-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(34 197 94 / var(--tw-bg-opacity, 1));
}

.bg-green-500\/10 {
  background-color: rgb(34 197 94 / 0.1);
}

.bg-green-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(22 163 74 / var(--tw-bg-opacity, 1));
}

.bg-green-700 {
  --tw-bg-opacity: 1;
  background-color: rgb(21 128 61 / var(--tw-bg-opacity, 1));
}

.bg-mandy-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(229 62 62 / var(--tw-bg-opacity, 1));
}

.bg-mandy-500\/90 {
  background-color: rgb(229 62 62 / 0.9);
}

.bg-red-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(248 113 113 / var(--tw-bg-opacity, 1));
}

.bg-red-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(239 68 68 / var(--tw-bg-opacity, 1));
}

.bg-red-500\/10 {
  background-color: rgb(239 68 68 / 0.1);
}

.bg-stone-950 {
  --tw-bg-opacity: 1;
  background-color: rgb(12 10 9 / var(--tw-bg-opacity, 1));
}

.bg-success-dark-light {
  --tw-bg-opacity: 1;
  background-color: rgb(21 87 36 / var(--tw-bg-opacity, 1));
}

.bg-success-light {
  --tw-bg-opacity: 1;
  background-color: rgb(212 237 218 / var(--tw-bg-opacity, 1));
}

.bg-teal-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(20 184 166 / var(--tw-bg-opacity, 1));
}

.bg-transparent {
  background-color: transparent;
}

.bg-white {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}

.bg-white\/10 {
  background-color: rgb(255 255 255 / 0.1);
}

.bg-yellow-300 {
  --tw-bg-opacity: 1;
  background-color: rgb(253 224 71 / var(--tw-bg-opacity, 1));
}

.bg-yellow-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(234 179 8 / var(--tw-bg-opacity, 1));
}

.bg-yellow-500\/20 {
  background-color: rgb(234 179 8 / 0.2);
}

.bg-opacity-60 {
  --tw-bg-opacity: 0.6;
}

.bg-gradient-to-b {
  background-image: linear-gradient(to bottom, var(--tw-gradient-stops));
}

.bg-gradient-to-br {
  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));
}

.bg-gradient-to-l {
  background-image: linear-gradient(to left, var(--tw-gradient-stops));
}

.bg-gradient-to-r {
  background-image: linear-gradient(to right, var(--tw-gradient-stops));
}

.bg-gradient-to-t {
  background-image: linear-gradient(to top, var(--tw-gradient-stops));
}

.from-\[\#00FF88\] {
  --tw-gradient-from: #00FF88 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(0 255 136 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-\[\#00FF88\]\/20 {
  --tw-gradient-from: rgb(0 255 136 / 0.2) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(0 255 136 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-\[\#00FFFF\] {
  --tw-gradient-from: #00FFFF var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(0 255 255 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-\[\#06B6D4\] {
  --tw-gradient-from: #06B6D4 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(6 182 212 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-\[\#0F172A\] {
  --tw-gradient-from: #0F172A var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(15 23 42 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-\[\#0F172A\]\/80 {
  --tw-gradient-from: rgb(15 23 42 / 0.8) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(15 23 42 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-\[\#0F172A\]\/90 {
  --tw-gradient-from: rgb(15 23 42 / 0.9) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(15 23 42 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-\[\#0F172A\]\/95 {
  --tw-gradient-from: rgb(15 23 42 / 0.95) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(15 23 42 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-\[\#13112E\] {
  --tw-gradient-from: #13112E var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(19 17 46 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-\[\#1E293B\]\/50 {
  --tw-gradient-from: rgb(30 41 59 / 0.5) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(30 41 59 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-\[\#1E293B\]\/60 {
  --tw-gradient-from: rgb(30 41 59 / 0.6) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(30 41 59 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-\[\#1E293B\]\/80 {
  --tw-gradient-from: rgb(30 41 59 / 0.8) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(30 41 59 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-\[\#1E293B\]\/95 {
  --tw-gradient-from: rgb(30 41 59 / 0.95) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(30 41 59 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-\[\#1a1640\]\/80 {
  --tw-gradient-from: rgb(26 22 64 / 0.8) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(26 22 64 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-\[\#2A2D4F\] {
  --tw-gradient-from: #2A2D4F var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(42 45 79 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-\[\#4B7DFF\] {
  --tw-gradient-from: #4B7DFF var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(75 125 255 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-\[\#4B7DFF\]\/10 {
  --tw-gradient-from: rgb(75 125 255 / 0.1) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(75 125 255 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-\[\#4B7DFF\]\/20 {
  --tw-gradient-from: rgb(75 125 255 / 0.2) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(75 125 255 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-\[\#4B7DFF\]\/5 {
  --tw-gradient-from: rgb(75 125 255 / 0.05) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(75 125 255 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-\[\#5081FF\] {
  --tw-gradient-from: #5081FF var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(80 129 255 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-\[\#5081FF\]\/10 {
  --tw-gradient-from: rgb(80 129 255 / 0.1) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(80 129 255 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-\[\#5081FF\]\/20 {
  --tw-gradient-from: rgb(80 129 255 / 0.2) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(80 129 255 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-\[\#6366F1\] {
  --tw-gradient-from: #6366F1 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(99 102 241 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-\[\#6366F1\]\/10 {
  --tw-gradient-from: rgb(99 102 241 / 0.1) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(99 102 241 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-\[\#6366F1\]\/20 {
  --tw-gradient-from: rgb(99 102 241 / 0.2) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(99 102 241 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-\[\#8B5CF6\]\/10 {
  --tw-gradient-from: rgb(139 92 246 / 0.1) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(139 92 246 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-\[\#8B5CF6\]\/30 {
  --tw-gradient-from: rgb(139 92 246 / 0.3) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(139 92 246 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-\[\#9C27B0\] {
  --tw-gradient-from: #9C27B0 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(156 39 176 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-\[\#9C27B0\]\/20 {
  --tw-gradient-from: rgb(156 39 176 / 0.2) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(156 39 176 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-\[\#F59E0B\] {
  --tw-gradient-from: #F59E0B var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(245 158 11 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-\[\#FF6B6B\] {
  --tw-gradient-from: #FF6B6B var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(255 107 107 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-\[\#FF6B6B\]\/20 {
  --tw-gradient-from: rgb(255 107 107 / 0.2) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(255 107 107 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-\[\#FFD700\] {
  --tw-gradient-from: #FFD700 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(255 215 0 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-\[\#FFD700\]\/20 {
  --tw-gradient-from: rgb(255 215 0 / 0.2) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(255 215 0 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-amber-500 {
  --tw-gradient-from: #f59e0b var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(245 158 11 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-black {
  --tw-gradient-from: #000 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(0 0 0 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-black\/15 {
  --tw-gradient-from: rgb(0 0 0 / 0.15) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(0 0 0 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-black\/20 {
  --tw-gradient-from: rgb(0 0 0 / 0.2) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(0 0 0 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-black\/90 {
  --tw-gradient-from: rgb(0 0 0 / 0.9) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(0 0 0 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-blue-400 {
  --tw-gradient-from: #60a5fa var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(96 165 250 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-blue-500 {
  --tw-gradient-from: #3b82f6 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(59 130 246 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-blue-500\/10 {
  --tw-gradient-from: rgb(59 130 246 / 0.1) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(59 130 246 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-blue-500\/20 {
  --tw-gradient-from: rgb(59 130 246 / 0.2) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(59 130 246 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-blue-500\/30 {
  --tw-gradient-from: rgb(59 130 246 / 0.3) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(59 130 246 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-blue-500\/5 {
  --tw-gradient-from: rgb(59 130 246 / 0.05) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(59 130 246 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-blue-900 {
  --tw-gradient-from: #1e3a8a var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(30 58 138 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-blue-900\/95 {
  --tw-gradient-from: rgb(30 58 138 / 0.95) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(30 58 138 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-cyan-500 {
  --tw-gradient-from: #06b6d4 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(6 182 212 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-emerald-500 {
  --tw-gradient-from: #10b981 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(16 185 129 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-emerald-900 {
  --tw-gradient-from: #064e3b var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(6 78 59 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-emerald-900\/95 {
  --tw-gradient-from: rgb(6 78 59 / 0.95) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(6 78 59 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-fuchsia-500 {
  --tw-gradient-from: #d946ef var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(217 70 239 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-gaming-blue {
  --tw-gradient-from: #5081FF var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(80 129 255 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-gaming-gold {
  --tw-gradient-from: #FFD700 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(255 215 0 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-gaming-green {
  --tw-gradient-from: #00FF88 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(0 255 136 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-gaming-purple {
  --tw-gradient-from: #272450 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(39 36 80 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-gaming-slate {
  --tw-gradient-from: #1E293B var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(30 41 59 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-gaming-slate\/60 {
  --tw-gradient-from: rgb(30 41 59 / 0.6) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(30 41 59 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-gaming-violet {
  --tw-gradient-from: #9C27B0 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(156 39 176 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-gray-700 {
  --tw-gradient-from: #374151 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(55 65 81 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-gray-700\/80 {
  --tw-gradient-from: rgb(55 65 81 / 0.8) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(55 65 81 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-gray-800 {
  --tw-gradient-from: #1f2937 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(31 41 55 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-gray-800\/50 {
  --tw-gradient-from: rgb(31 41 55 / 0.5) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(31 41 55 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-gray-800\/60 {
  --tw-gradient-from: rgb(31 41 55 / 0.6) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(31 41 55 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-gray-800\/80 {
  --tw-gradient-from: rgb(31 41 55 / 0.8) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(31 41 55 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-green-500 {
  --tw-gradient-from: #22c55e var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(34 197 94 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-green-500\/20 {
  --tw-gradient-from: rgb(34 197 94 / 0.2) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(34 197 94 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-indigo-900 {
  --tw-gradient-from: #312e81 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(49 46 129 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-orange-900 {
  --tw-gradient-from: #7c2d12 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(124 45 18 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-orange-900\/95 {
  --tw-gradient-from: rgb(124 45 18 / 0.95) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(124 45 18 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-pink-500 {
  --tw-gradient-from: #ec4899 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(236 72 153 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-purple-900 {
  --tw-gradient-from: #581c87 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(88 28 135 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-purple-900\/95 {
  --tw-gradient-from: rgb(88 28 135 / 0.95) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(88 28 135 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-red-500 {
  --tw-gradient-from: #ef4444 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(239 68 68 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-red-600 {
  --tw-gradient-from: #dc2626 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(220 38 38 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-teal-500 {
  --tw-gradient-from: #14b8a6 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(20 184 166 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-transparent {
  --tw-gradient-from: transparent var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(0 0 0 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-violet-500 {
  --tw-gradient-from: #8b5cf6 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(139 92 246 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-yellow-400 {
  --tw-gradient-from: #facc15 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(250 204 21 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-yellow-500 {
  --tw-gradient-from: #eab308 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(234 179 8 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-yellow-500\/10 {
  --tw-gradient-from: rgb(234 179 8 / 0.1) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(234 179 8 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.via-\[\#00FF88\]\/60 {
  --tw-gradient-to: rgb(0 255 136 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(0 255 136 / 0.6) var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.via-\[\#00FFFF\] {
  --tw-gradient-to: rgb(0 255 255 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #00FFFF var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.via-\[\#1E293B\] {
  --tw-gradient-to: rgb(30 41 59 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #1E293B var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.via-\[\#1E293B\]\/80 {
  --tw-gradient-to: rgb(30 41 59 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(30 41 59 / 0.8) var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.via-\[\#1E293B\]\/90 {
  --tw-gradient-to: rgb(30 41 59 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(30 41 59 / 0.9) var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.via-\[\#1a1640\] {
  --tw-gradient-to: rgb(26 22 64 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #1a1640 var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.via-\[\#374151\]\/85 {
  --tw-gradient-to: rgb(55 65 81 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(55 65 81 / 0.85) var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.via-\[\#4B7DFF\]\/20 {
  --tw-gradient-to: rgb(75 125 255 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(75 125 255 / 0.2) var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.via-\[\#5081FF\] {
  --tw-gradient-to: rgb(80 129 255 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #5081FF var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.via-\[\#5081FF\]\/10 {
  --tw-gradient-to: rgb(80 129 255 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(80 129 255 / 0.1) var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.via-\[\#5081FF\]\/30 {
  --tw-gradient-to: rgb(80 129 255 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(80 129 255 / 0.3) var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.via-\[\#6366F1\]\/15 {
  --tw-gradient-to: rgb(99 102 241 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(99 102 241 / 0.15) var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.via-\[\#8B5CF6\] {
  --tw-gradient-to: rgb(139 92 246 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #8B5CF6 var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.via-\[\#8B5CF6\]\/10 {
  --tw-gradient-to: rgb(139 92 246 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(139 92 246 / 0.1) var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.via-\[\#9C27B0\]\/60 {
  --tw-gradient-to: rgb(156 39 176 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(156 39 176 / 0.6) var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.via-\[\#FF6B6B\]\/60 {
  --tw-gradient-to: rgb(255 107 107 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(255 107 107 / 0.6) var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.via-\[\#FFD700\]\/60 {
  --tw-gradient-to: rgb(255 215 0 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(255 215 0 / 0.6) var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.via-black {
  --tw-gradient-to: rgb(0 0 0 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #000 var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.via-gaming-deep-blue {
  --tw-gradient-to: rgb(52 99 219 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #3463DB var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.via-gray-900 {
  --tw-gradient-to: rgb(17 24 39 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #111827 var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.via-gray-900\/95 {
  --tw-gradient-to: rgb(17 24 39 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(17 24 39 / 0.95) var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.via-purple-400 {
  --tw-gradient-to: rgb(192 132 252 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #c084fc var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.via-purple-500 {
  --tw-gradient-to: rgb(168 85 247 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #a855f7 var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.via-purple-500\/20 {
  --tw-gradient-to: rgb(168 85 247 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(168 85 247 / 0.2) var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.via-purple-500\/30 {
  --tw-gradient-to: rgb(168 85 247 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(168 85 247 / 0.3) var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.via-purple-500\/5 {
  --tw-gradient-to: rgb(168 85 247 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(168 85 247 / 0.05) var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.via-transparent {
  --tw-gradient-to: rgb(0 0 0 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), transparent var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.via-white {
  --tw-gradient-to: rgb(255 255 255 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #fff var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.via-white\/10 {
  --tw-gradient-to: rgb(255 255 255 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(255 255 255 / 0.1) var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.via-white\/20 {
  --tw-gradient-to: rgb(255 255 255 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(255 255 255 / 0.2) var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.via-white\/30 {
  --tw-gradient-to: rgb(255 255 255 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(255 255 255 / 0.3) var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.to-\[\#0099CC\] {
  --tw-gradient-to: #0099CC var(--tw-gradient-to-position);
}

.to-\[\#00CC6A\] {
  --tw-gradient-to: #00CC6A var(--tw-gradient-to-position);
}

.to-\[\#00FF88\]\/20 {
  --tw-gradient-to: rgb(0 255 136 / 0.2) var(--tw-gradient-to-position);
}

.to-\[\#06B6D4\] {
  --tw-gradient-to: #06B6D4 var(--tw-gradient-to-position);
}

.to-\[\#06B6D4\]\/10 {
  --tw-gradient-to: rgb(6 182 212 / 0.1) var(--tw-gradient-to-position);
}

.to-\[\#0F172A\] {
  --tw-gradient-to: #0F172A var(--tw-gradient-to-position);
}

.to-\[\#0F172A\]\/50 {
  --tw-gradient-to: rgb(15 23 42 / 0.5) var(--tw-gradient-to-position);
}

.to-\[\#0F172A\]\/80 {
  --tw-gradient-to: rgb(15 23 42 / 0.8) var(--tw-gradient-to-position);
}

.to-\[\#0F172A\]\/90 {
  --tw-gradient-to: rgb(15 23 42 / 0.9) var(--tw-gradient-to-position);
}

.to-\[\#0F172A\]\/95 {
  --tw-gradient-to: rgb(15 23 42 / 0.95) var(--tw-gradient-to-position);
}

.to-\[\#10B981\] {
  --tw-gradient-to: #10B981 var(--tw-gradient-to-position);
}

.to-\[\#13112E\] {
  --tw-gradient-to: #13112E var(--tw-gradient-to-position);
}

.to-\[\#13112E\]\/90 {
  --tw-gradient-to: rgb(19 17 46 / 0.9) var(--tw-gradient-to-position);
}

.to-\[\#1E293B\]\/90 {
  --tw-gradient-to: rgb(30 41 59 / 0.9) var(--tw-gradient-to-position);
}

.to-\[\#1E293B\]\/95 {
  --tw-gradient-to: rgb(30 41 59 / 0.95) var(--tw-gradient-to-position);
}

.to-\[\#1a1640\] {
  --tw-gradient-to: #1a1640 var(--tw-gradient-to-position);
}

.to-\[\#3463DB\] {
  --tw-gradient-to: #3463DB var(--tw-gradient-to-position);
}

.to-\[\#3463DB\]\/10 {
  --tw-gradient-to: rgb(52 99 219 / 0.1) var(--tw-gradient-to-position);
}

.to-\[\#3463DB\]\/20 {
  --tw-gradient-to: rgb(52 99 219 / 0.2) var(--tw-gradient-to-position);
}

.to-\[\#4B7DFF\] {
  --tw-gradient-to: #4B7DFF var(--tw-gradient-to-position);
}

.to-\[\#4B7DFF\]\/20 {
  --tw-gradient-to: rgb(75 125 255 / 0.2) var(--tw-gradient-to-position);
}

.to-\[\#4B7DFF\]\/5 {
  --tw-gradient-to: rgb(75 125 255 / 0.05) var(--tw-gradient-to-position);
}

.to-\[\#5081FF\] {
  --tw-gradient-to: #5081FF var(--tw-gradient-to-position);
}

.to-\[\#5081FF\]\/10 {
  --tw-gradient-to: rgb(80 129 255 / 0.1) var(--tw-gradient-to-position);
}

.to-\[\#5081FF\]\/20 {
  --tw-gradient-to: rgb(80 129 255 / 0.2) var(--tw-gradient-to-position);
}

.to-\[\#6366F1\] {
  --tw-gradient-to: #6366F1 var(--tw-gradient-to-position);
}

.to-\[\#6B46C1\] {
  --tw-gradient-to: #6B46C1 var(--tw-gradient-to-position);
}

.to-\[\#7B1FA2\] {
  --tw-gradient-to: #7B1FA2 var(--tw-gradient-to-position);
}

.to-\[\#8B5CF6\] {
  --tw-gradient-to: #8B5CF6 var(--tw-gradient-to-position);
}

.to-\[\#9C27B0\]\/20 {
  --tw-gradient-to: rgb(156 39 176 / 0.2) var(--tw-gradient-to-position);
}

.to-\[\#EF4444\] {
  --tw-gradient-to: #EF4444 var(--tw-gradient-to-position);
}

.to-\[\#FF5252\] {
  --tw-gradient-to: #FF5252 var(--tw-gradient-to-position);
}

.to-\[\#FF6B6B\]\/20 {
  --tw-gradient-to: rgb(255 107 107 / 0.2) var(--tw-gradient-to-position);
}

.to-\[\#FF8C00\] {
  --tw-gradient-to: #FF8C00 var(--tw-gradient-to-position);
}

.to-\[\#FFA500\] {
  --tw-gradient-to: #FFA500 var(--tw-gradient-to-position);
}

.to-\[\#FFD700\]\/20 {
  --tw-gradient-to: rgb(255 215 0 / 0.2) var(--tw-gradient-to-position);
}

.to-black {
  --tw-gradient-to: #000 var(--tw-gradient-to-position);
}

.to-black\/90 {
  --tw-gradient-to: rgb(0 0 0 / 0.9) var(--tw-gradient-to-position);
}

.to-blue-400 {
  --tw-gradient-to: #60a5fa var(--tw-gradient-to-position);
}

.to-blue-500 {
  --tw-gradient-to: #3b82f6 var(--tw-gradient-to-position);
}

.to-blue-500\/20 {
  --tw-gradient-to: rgb(59 130 246 / 0.2) var(--tw-gradient-to-position);
}

.to-blue-500\/30 {
  --tw-gradient-to: rgb(59 130 246 / 0.3) var(--tw-gradient-to-position);
}

.to-blue-500\/5 {
  --tw-gradient-to: rgb(59 130 246 / 0.05) var(--tw-gradient-to-position);
}

.to-cyan-400 {
  --tw-gradient-to: #22d3ee var(--tw-gradient-to-position);
}

.to-emerald-500 {
  --tw-gradient-to: #10b981 var(--tw-gradient-to-position);
}

.to-emerald-500\/20 {
  --tw-gradient-to: rgb(16 185 129 / 0.2) var(--tw-gradient-to-position);
}

.to-emerald-600 {
  --tw-gradient-to: #059669 var(--tw-gradient-to-position);
}

.to-fuchsia-400 {
  --tw-gradient-to: #e879f9 var(--tw-gradient-to-position);
}

.to-gaming-blue {
  --tw-gradient-to: #5081FF var(--tw-gradient-to-position);
}

.to-gaming-dark {
  --tw-gradient-to: #0F172A var(--tw-gradient-to-position);
}

.to-gaming-dark\/80 {
  --tw-gradient-to: rgb(15 23 42 / 0.8) var(--tw-gradient-to-position);
}

.to-gaming-deep-blue {
  --tw-gradient-to: #3463DB var(--tw-gradient-to-position);
}

.to-gaming-orange {
  --tw-gradient-to: #FF8C00 var(--tw-gradient-to-position);
}

.to-gray-700 {
  --tw-gradient-to: #374151 var(--tw-gradient-to-position);
}

.to-gray-800 {
  --tw-gradient-to: #1f2937 var(--tw-gradient-to-position);
}

.to-gray-800\/90 {
  --tw-gradient-to: rgb(31 41 55 / 0.9) var(--tw-gradient-to-position);
}

.to-gray-900 {
  --tw-gradient-to: #111827 var(--tw-gradient-to-position);
}

.to-gray-900\/50 {
  --tw-gradient-to: rgb(17 24 39 / 0.5) var(--tw-gradient-to-position);
}

.to-gray-900\/60 {
  --tw-gradient-to: rgb(17 24 39 / 0.6) var(--tw-gradient-to-position);
}

.to-gray-900\/80 {
  --tw-gradient-to: rgb(17 24 39 / 0.8) var(--tw-gradient-to-position);
}

.to-gray-900\/95 {
  --tw-gradient-to: rgb(17 24 39 / 0.95) var(--tw-gradient-to-position);
}

.to-indigo-900 {
  --tw-gradient-to: #312e81 var(--tw-gradient-to-position);
}

.to-orange-400 {
  --tw-gradient-to: #fb923c var(--tw-gradient-to-position);
}

.to-orange-500 {
  --tw-gradient-to: #f97316 var(--tw-gradient-to-position);
}

.to-orange-500\/10 {
  --tw-gradient-to: rgb(249 115 22 / 0.1) var(--tw-gradient-to-position);
}

.to-purple-500 {
  --tw-gradient-to: #a855f7 var(--tw-gradient-to-position);
}

.to-purple-500\/10 {
  --tw-gradient-to: rgb(168 85 247 / 0.1) var(--tw-gradient-to-position);
}

.to-purple-600 {
  --tw-gradient-to: #9333ea var(--tw-gradient-to-position);
}

.to-red-600 {
  --tw-gradient-to: #dc2626 var(--tw-gradient-to-position);
}

.to-red-700 {
  --tw-gradient-to: #b91c1c var(--tw-gradient-to-position);
}

.to-teal-500 {
  --tw-gradient-to: #14b8a6 var(--tw-gradient-to-position);
}

.to-transparent {
  --tw-gradient-to: transparent var(--tw-gradient-to-position);
}

.to-violet-400 {
  --tw-gradient-to: #a78bfa var(--tw-gradient-to-position);
}

.bg-contain {
  background-size: contain;
}

.bg-cover {
  background-size: cover;
}

.bg-clip-text {
  -webkit-background-clip: text;
          background-clip: text;
}

.bg-center {
  background-position: center;
}

.bg-left-top {
  background-position: left top;
}

.bg-repeat {
  background-repeat: repeat;
}

.bg-no-repeat {
  background-repeat: no-repeat;
}

.bg-origin-border {
  background-origin: border-box;
}

.fill-white {
  fill: #fff;
}

.object-contain {
  -o-object-fit: contain;
     object-fit: contain;
}

.object-cover {
  -o-object-fit: cover;
     object-fit: cover;
}

.\!p-0 {
  padding: 0px !important;
}

.\!p-5 {
  padding: 1.25rem !important;
}

.\!p-6 {
  padding: 1.5rem !important;
}

.p-0 {
  padding: 0px;
}

.p-1 {
  padding: 0.25rem;
}

.p-1\.5 {
  padding: 0.375rem;
}

.p-10 {
  padding: 2.5rem;
}

.p-2 {
  padding: 0.5rem;
}

.p-3 {
  padding: 0.75rem;
}

.p-4 {
  padding: 1rem;
}

.p-5 {
  padding: 1.25rem;
}

.p-6 {
  padding: 1.5rem;
}

.p-7 {
  padding: 1.75rem;
}

.p-8 {
  padding: 2rem;
}

.p-9 {
  padding: 2.25rem;
}

.p-\[16px\] {
  padding: 16px;
}

.p-\[24px\] {
  padding: 24px;
}

.p-\[4px\] {
  padding: 4px;
}

.p-px {
  padding: 1px;
}

.\!px-2 {
  padding-left: 0.5rem !important;
  padding-right: 0.5rem !important;
}

.\!px-\[20px\] {
  padding-left: 20px !important;
  padding-right: 20px !important;
}

.\!py-0 {
  padding-top: 0px !important;
  padding-bottom: 0px !important;
}

.\!py-3 {
  padding-top: 0.75rem !important;
  padding-bottom: 0.75rem !important;
}

.px-0 {
  padding-left: 0px;
  padding-right: 0px;
}

.px-1 {
  padding-left: 0.25rem;
  padding-right: 0.25rem;
}

.px-10 {
  padding-left: 2.5rem;
  padding-right: 2.5rem;
}

.px-11 {
  padding-left: 2.75rem;
  padding-right: 2.75rem;
}

.px-12 {
  padding-left: 3rem;
  padding-right: 3rem;
}

.px-14 {
  padding-left: 3.5rem;
  padding-right: 3.5rem;
}

.px-16 {
  padding-left: 4rem;
  padding-right: 4rem;
}

.px-2 {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}

.px-20 {
  padding-left: 5rem;
  padding-right: 5rem;
}

.px-3 {
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}

.px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}

.px-40 {
  padding-left: 10rem;
  padding-right: 10rem;
}

.px-5 {
  padding-left: 1.25rem;
  padding-right: 1.25rem;
}

.px-6 {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}

.px-7 {
  padding-left: 1.75rem;
  padding-right: 1.75rem;
}

.px-8 {
  padding-left: 2rem;
  padding-right: 2rem;
}

.px-9 {
  padding-left: 2.25rem;
  padding-right: 2.25rem;
}

.px-\[12px\] {
  padding-left: 12px;
  padding-right: 12px;
}

.px-\[16px\] {
  padding-left: 16px;
  padding-right: 16px;
}

.px-\[24px\] {
  padding-left: 24px;
  padding-right: 24px;
}

.py-0 {
  padding-top: 0px;
  padding-bottom: 0px;
}

.py-0\.5 {
  padding-top: 0.125rem;
  padding-bottom: 0.125rem;
}

.py-1 {
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}

.py-1\.5 {
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
}

.py-10 {
  padding-top: 2.5rem;
  padding-bottom: 2.5rem;
}

.py-12 {
  padding-top: 3rem;
  padding-bottom: 3rem;
}

.py-16 {
  padding-top: 4rem;
  padding-bottom: 4rem;
}

.py-2 {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}

.py-2\.5 {
  padding-top: 0.625rem;
  padding-bottom: 0.625rem;
}

.py-20 {
  padding-top: 5rem;
  padding-bottom: 5rem;
}

.py-3 {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}

.py-4 {
  padding-top: 1rem;
  padding-bottom: 1rem;
}

.py-5 {
  padding-top: 1.25rem;
  padding-bottom: 1.25rem;
}

.py-6 {
  padding-top: 1.5rem;
  padding-bottom: 1.5rem;
}

.py-7 {
  padding-top: 1.75rem;
  padding-bottom: 1.75rem;
}

.py-8 {
  padding-top: 2rem;
  padding-bottom: 2rem;
}

.py-9 {
  padding-top: 2.25rem;
  padding-bottom: 2.25rem;
}

.py-\[11px\] {
  padding-top: 11px;
  padding-bottom: 11px;
}

.py-\[12px\] {
  padding-top: 12px;
  padding-bottom: 12px;
}

.py-\[14px\] {
  padding-top: 14px;
  padding-bottom: 14px;
}

.py-\[16px\] {
  padding-top: 16px;
  padding-bottom: 16px;
}

.py-\[9px\] {
  padding-top: 9px;
  padding-bottom: 9px;
}

.py-px {
  padding-top: 1px;
  padding-bottom: 1px;
}

.pb-0 {
  padding-bottom: 0px;
}

.pb-10 {
  padding-bottom: 2.5rem;
}

.pb-12 {
  padding-bottom: 3rem;
}

.pb-16 {
  padding-bottom: 4rem;
}

.pb-2 {
  padding-bottom: 0.5rem;
}

.pb-20 {
  padding-bottom: 5rem;
}

.pb-24 {
  padding-bottom: 6rem;
}

.pb-3 {
  padding-bottom: 0.75rem;
}

.pb-4 {
  padding-bottom: 1rem;
}

.pb-5 {
  padding-bottom: 1.25rem;
}

.pb-8 {
  padding-bottom: 2rem;
}

.pb-\[6px\] {
  padding-bottom: 6px;
}

.pb-\[7px\] {
  padding-bottom: 7px;
}

.pb-\[8px\] {
  padding-bottom: 8px;
}

.pe-1 {
  padding-inline-end: 0.25rem;
}

.pe-32 {
  padding-inline-end: 8rem;
}

.pl-10 {
  padding-left: 2.5rem;
}

.pl-2 {
  padding-left: 0.5rem;
}

.pl-3 {
  padding-left: 0.75rem;
}

.pl-4 {
  padding-left: 1rem;
}

.pl-8 {
  padding-left: 2rem;
}

.pr-2 {
  padding-right: 0.5rem;
}

.pr-3 {
  padding-right: 0.75rem;
}

.pr-4 {
  padding-right: 1rem;
}

.pr-9 {
  padding-right: 2.25rem;
}

.ps-10 {
  padding-inline-start: 2.5rem;
}

.ps-5 {
  padding-inline-start: 1.25rem;
}

.pt-0 {
  padding-top: 0px;
}

.pt-1 {
  padding-top: 0.25rem;
}

.pt-12 {
  padding-top: 3rem;
}

.pt-14 {
  padding-top: 3.5rem;
}

.pt-2 {
  padding-top: 0.5rem;
}

.pt-3 {
  padding-top: 0.75rem;
}

.pt-4 {
  padding-top: 1rem;
}

.pt-5 {
  padding-top: 1.25rem;
}

.pt-6 {
  padding-top: 1.5rem;
}

.pt-\[10px\] {
  padding-top: 10px;
}

.pt-\[8px\] {
  padding-top: 8px;
}

.text-left {
  text-align: left;
}

.\!text-center {
  text-align: center !important;
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.text-justify {
  text-align: justify;
}

.text-start {
  text-align: start;
}

.text-end {
  text-align: end;
}

.align-top {
  vertical-align: top;
}

.align-middle {
  vertical-align: middle;
}

.align-text-bottom {
  vertical-align: text-bottom;
}

.align-\[-\.125em\] {
  vertical-align: -.125em;
}

.font-league-spartan {
  font-family: League Spartan, Arial, sans-serif;
}

.font-mono {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
}

.font-sans {
  font-family: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
}

.font-signika {
  font-family: Signika, sans-serif;
}

.font-spartan {
  font-family: League Spartan, Arial, sans-serif;
}

.\!text-\[12px\] {
  font-size: 12px !important;
}

.text-2xl {
  font-size: 1.5rem;
  line-height: 2rem;
}

.text-3xl {
  font-size: 1.875rem;
  line-height: 2.25rem;
}

.text-4xl {
  font-size: 2.25rem;
  line-height: 2.5rem;
}

.text-5xl {
  font-size: 3rem;
  line-height: 1;
}

.text-6xl {
  font-size: 3.75rem;
  line-height: 1;
}

.text-\[0\.813rem\] {
  font-size: 0.813rem;
}

.text-\[10px\] {
  font-size: 10px;
}

.text-\[11px\] {
  font-size: 11px;
}

.text-\[12px\] {
  font-size: 12px;
}

.text-\[14px\] {
  font-size: 14px;
}

.text-\[16px\] {
  font-size: 16px;
}

.text-\[20px\] {
  font-size: 20px;
}

.text-\[24px\] {
  font-size: 24px;
}

.text-\[28px\] {
  font-size: 28px;
}

.text-base {
  font-size: 1rem;
  line-height: 1.5rem;
}

.text-lg {
  font-size: 1.125rem;
  line-height: 1.75rem;
}

.text-lg\/none {
  font-size: 1.125rem;
  line-height: 1;
}

.text-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.text-xl {
  font-size: 1.25rem;
  line-height: 1.75rem;
}

.text-xs {
  font-size: 0.75rem;
  line-height: 1rem;
}

.\!font-medium {
  font-weight: 500 !important;
}

.font-\[300\] {
  font-weight: 300;
}

.font-\[400\] {
  font-weight: 400;
}

.font-black {
  font-weight: 900;
}

.font-bold {
  font-weight: 700;
}

.font-extrabold {
  font-weight: 800;
}

.font-medium {
  font-weight: 500;
}

.font-normal {
  font-weight: 400;
}

.font-semibold {
  font-weight: 600;
}

.uppercase {
  text-transform: uppercase;
}

.lowercase {
  text-transform: lowercase;
}

.capitalize {
  text-transform: capitalize;
}

.\!italic {
  font-style: italic !important;
}

.italic {
  font-style: italic;
}

.not-italic {
  font-style: normal;
}

.ordinal {
  --tw-ordinal: ordinal;
  font-variant-numeric: var(--tw-ordinal) var(--tw-slashed-zero) var(--tw-numeric-figure) var(--tw-numeric-spacing) var(--tw-numeric-fraction);
}

.\!leading-snug {
  line-height: 1.375 !important;
}

.leading-3 {
  line-height: .75rem;
}

.leading-4 {
  line-height: 1rem;
}

.leading-5 {
  line-height: 1.25rem;
}

.leading-6 {
  line-height: 1.5rem;
}

.leading-\[12px\] {
  line-height: 12px;
}

.leading-\[13px\] {
  line-height: 13px;
}

.leading-\[14px\] {
  line-height: 14px;
}

.leading-\[16px\] {
  line-height: 16px;
}

.leading-\[17px\] {
  line-height: 17px;
}

.leading-\[28px\] {
  line-height: 28px;
}

.leading-none {
  line-height: 1;
}

.leading-normal {
  line-height: 1.5;
}

.leading-relaxed {
  line-height: 1.625;
}

.leading-snug {
  line-height: 1.375;
}

.leading-tight {
  line-height: 1.25;
}

.tracking-\[0\.2em\] {
  letter-spacing: 0.2em;
}

.tracking-tight {
  letter-spacing: -0.025em;
}

.tracking-wide {
  letter-spacing: 0.025em;
}

.tracking-wider {
  letter-spacing: 0.05em;
}

.text-\[\#00FF88\] {
  --tw-text-opacity: 1;
  color: rgb(0 255 136 / var(--tw-text-opacity, 1));
}

.text-\[\#1a1640\] {
  --tw-text-opacity: 1;
  color: rgb(26 22 64 / var(--tw-text-opacity, 1));
}

.text-\[\#45ff02\] {
  --tw-text-opacity: 1;
  color: rgb(69 255 2 / var(--tw-text-opacity, 1));
}

.text-\[\#4B7DFF\] {
  --tw-text-opacity: 1;
  color: rgb(75 125 255 / var(--tw-text-opacity, 1));
}

.text-\[\#5081FF\] {
  --tw-text-opacity: 1;
  color: rgb(80 129 255 / var(--tw-text-opacity, 1));
}

.text-\[\#69B1FF\] {
  --tw-text-opacity: 1;
  color: rgb(105 177 255 / var(--tw-text-opacity, 1));
}

.text-\[\#9C27B0\] {
  --tw-text-opacity: 1;
  color: rgb(156 39 176 / var(--tw-text-opacity, 1));
}

.text-\[\#9F9BAB\] {
  --tw-text-opacity: 1;
  color: rgb(159 155 171 / var(--tw-text-opacity, 1));
}

.text-\[\#C7D2FE\] {
  --tw-text-opacity: 1;
  color: rgb(199 210 254 / var(--tw-text-opacity, 1));
}

.text-\[\#FF6B6B\] {
  --tw-text-opacity: 1;
  color: rgb(255 107 107 / var(--tw-text-opacity, 1));
}

.text-\[\#FFCA06\] {
  --tw-text-opacity: 1;
  color: rgb(255 202 6 / var(--tw-text-opacity, 1));
}

.text-\[\#FFD25F\] {
  --tw-text-opacity: 1;
  color: rgb(255 210 95 / var(--tw-text-opacity, 1));
}

.text-\[\#FFD700\] {
  --tw-text-opacity: 1;
  color: rgb(255 215 0 / var(--tw-text-opacity, 1));
}

.text-\[\#FFFFFF99\] {
  color: #FFFFFF99;
}

.text-\[\#FFFFFFCC\] {
  color: #FFFFFFCC;
}

.text-\[\#FFFFFF\] {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.text-\[\#f8f8f8\] {
  --tw-text-opacity: 1;
  color: rgb(248 248 248 / var(--tw-text-opacity, 1));
}

.text-\[\#ff062e\] {
  --tw-text-opacity: 1;
  color: rgb(255 6 46 / var(--tw-text-opacity, 1));
}

.text-\[\#ff062e\]\/70 {
  color: rgb(255 6 46 / 0.7);
}

.text-\[\#fff9\] {
  color: #fff9;
}

.text-\[\#fff\] {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.text-amber-400 {
  --tw-text-opacity: 1;
  color: rgb(251 191 36 / var(--tw-text-opacity, 1));
}

.text-amber-500 {
  --tw-text-opacity: 1;
  color: rgb(245 158 11 / var(--tw-text-opacity, 1));
}

.text-amber-600 {
  --tw-text-opacity: 1;
  color: rgb(217 119 6 / var(--tw-text-opacity, 1));
}

.text-black {
  --tw-text-opacity: 1;
  color: rgb(0 0 0 / var(--tw-text-opacity, 1));
}

.text-blue-400 {
  --tw-text-opacity: 1;
  color: rgb(96 165 250 / var(--tw-text-opacity, 1));
}

.text-blue-600 {
  --tw-text-opacity: 1;
  color: rgb(37 99 235 / var(--tw-text-opacity, 1));
}

.text-cyan-500 {
  --tw-text-opacity: 1;
  color: rgb(6 182 212 / var(--tw-text-opacity, 1));
}

.text-emerald-400 {
  --tw-text-opacity: 1;
  color: rgb(52 211 153 / var(--tw-text-opacity, 1));
}

.text-emerald-500 {
  --tw-text-opacity: 1;
  color: rgb(16 185 129 / var(--tw-text-opacity, 1));
}

.text-gaming-blue {
  --tw-text-opacity: 1;
  color: rgb(80 129 255 / var(--tw-text-opacity, 1));
}

.text-gaming-dark {
  --tw-text-opacity: 1;
  color: rgb(15 23 42 / var(--tw-text-opacity, 1));
}

.text-gaming-deep-blue {
  --tw-text-opacity: 1;
  color: rgb(52 99 219 / var(--tw-text-opacity, 1));
}

.text-gaming-gold {
  --tw-text-opacity: 1;
  color: rgb(255 215 0 / var(--tw-text-opacity, 1));
}

.text-gaming-green {
  --tw-text-opacity: 1;
  color: rgb(0 255 136 / var(--tw-text-opacity, 1));
}

.text-gaming-light-blue {
  --tw-text-opacity: 1;
  color: rgb(75 125 255 / var(--tw-text-opacity, 1));
}

.text-gaming-navy {
  --tw-text-opacity: 1;
  color: rgb(19 17 46 / var(--tw-text-opacity, 1));
}

.text-gaming-orange {
  --tw-text-opacity: 1;
  color: rgb(255 140 0 / var(--tw-text-opacity, 1));
}

.text-gaming-purple {
  --tw-text-opacity: 1;
  color: rgb(39 36 80 / var(--tw-text-opacity, 1));
}

.text-gaming-red {
  --tw-text-opacity: 1;
  color: rgb(255 107 107 / var(--tw-text-opacity, 1));
}

.text-gaming-slate {
  --tw-text-opacity: 1;
  color: rgb(30 41 59 / var(--tw-text-opacity, 1));
}

.text-gaming-violet {
  --tw-text-opacity: 1;
  color: rgb(156 39 176 / var(--tw-text-opacity, 1));
}

.text-gray-200 {
  --tw-text-opacity: 1;
  color: rgb(229 231 235 / var(--tw-text-opacity, 1));
}

.text-gray-300 {
  --tw-text-opacity: 1;
  color: rgb(209 213 219 / var(--tw-text-opacity, 1));
}

.text-gray-400 {
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity, 1));
}

.text-gray-500 {
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity, 1));
}

.text-gray-600 {
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity, 1));
}

.text-gray-700 {
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity, 1));
}

.text-gray-800 {
  --tw-text-opacity: 1;
  color: rgb(31 41 55 / var(--tw-text-opacity, 1));
}

.text-gray-900 {
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity, 1));
}

.text-gray-950 {
  --tw-text-opacity: 1;
  color: rgb(3 7 18 / var(--tw-text-opacity, 1));
}

.text-green-400 {
  --tw-text-opacity: 1;
  color: rgb(74 222 128 / var(--tw-text-opacity, 1));
}

.text-indigo-600 {
  --tw-text-opacity: 1;
  color: rgb(79 70 229 / var(--tw-text-opacity, 1));
}

.text-mandy-500 {
  --tw-text-opacity: 1;
  color: rgb(229 62 62 / var(--tw-text-opacity, 1));
}

.text-neutral-300 {
  --tw-text-opacity: 1;
  color: rgb(212 212 212 / var(--tw-text-opacity, 1));
}

.text-orange-400 {
  --tw-text-opacity: 1;
  color: rgb(251 146 60 / var(--tw-text-opacity, 1));
}

.text-purple-400 {
  --tw-text-opacity: 1;
  color: rgb(192 132 252 / var(--tw-text-opacity, 1));
}

.text-red-400 {
  --tw-text-opacity: 1;
  color: rgb(248 113 113 / var(--tw-text-opacity, 1));
}

.text-red-500 {
  --tw-text-opacity: 1;
  color: rgb(239 68 68 / var(--tw-text-opacity, 1));
}

.text-sky-500 {
  --tw-text-opacity: 1;
  color: rgb(14 165 233 / var(--tw-text-opacity, 1));
}

.text-slate-400 {
  --tw-text-opacity: 1;
  color: rgb(148 163 184 / var(--tw-text-opacity, 1));
}

.text-slate-500 {
  --tw-text-opacity: 1;
  color: rgb(100 116 139 / var(--tw-text-opacity, 1));
}

.text-slate-900 {
  --tw-text-opacity: 1;
  color: rgb(15 23 42 / var(--tw-text-opacity, 1));
}

.text-teal-500 {
  --tw-text-opacity: 1;
  color: rgb(20 184 166 / var(--tw-text-opacity, 1));
}

.text-transparent {
  color: transparent;
}

.text-white {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.text-white\/70 {
  color: rgb(255 255 255 / 0.7);
}

.text-yellow-300 {
  --tw-text-opacity: 1;
  color: rgb(253 224 71 / var(--tw-text-opacity, 1));
}

.text-yellow-400 {
  --tw-text-opacity: 1;
  color: rgb(250 204 21 / var(--tw-text-opacity, 1));
}

.text-yellow-500 {
  --tw-text-opacity: 1;
  color: rgb(234 179 8 / var(--tw-text-opacity, 1));
}

.underline {
  text-decoration-line: underline;
}

.overline {
  text-decoration-line: overline;
}

.line-through {
  text-decoration-line: line-through;
}

.underline-offset-4 {
  text-underline-offset: 4px;
}

.antialiased {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.placeholder-\[\#FFFFFFCC\]\/50::-moz-placeholder {
  color: rgb(255 255 255 / 0.5);
}

.placeholder-\[\#FFFFFFCC\]\/50::placeholder {
  color: rgb(255 255 255 / 0.5);
}

.opacity-0 {
  opacity: 0;
}

.opacity-10 {
  opacity: 0.1;
}

.opacity-100 {
  opacity: 1;
}

.opacity-20 {
  opacity: 0.2;
}

.opacity-30 {
  opacity: 0.3;
}

.opacity-5 {
  opacity: 0.05;
}

.opacity-50 {
  opacity: 0.5;
}

.opacity-55 {
  opacity: 0.55;
}

.opacity-60 {
  opacity: 0.6;
}

.opacity-65 {
  opacity: 0.65;
}

.opacity-70 {
  opacity: 0.7;
}

.opacity-75 {
  opacity: 0.75;
}

.opacity-80 {
  opacity: 0.8;
}

.opacity-90 {
  opacity: 0.9;
}

.opacity-\[0\.7\] {
  opacity: 0.7;
}

.mix-blend-luminosity {
  mix-blend-mode: luminosity;
}

.\!shadow-none {
  --tw-shadow: 0 0 #0000 !important;
  --tw-shadow-colored: 0 0 #0000 !important;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow) !important;
}

.shadow {
  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-2xl {
  --tw-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);
  --tw-shadow-colored: 0 25px 50px -12px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-lg {
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-md {
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-none {
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-sm {
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-xl {
  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-\[\#06B6D4\]\/30 {
  --tw-shadow-color: rgb(6 182 212 / 0.3);
  --tw-shadow: var(--tw-shadow-colored);
}

.shadow-\[\#06B6D4\]\/50 {
  --tw-shadow-color: rgb(6 182 212 / 0.5);
  --tw-shadow: var(--tw-shadow-colored);
}

.shadow-\[\#10B981\]\/30 {
  --tw-shadow-color: rgb(16 185 129 / 0.3);
  --tw-shadow: var(--tw-shadow-colored);
}

.shadow-\[\#5081FF\]\/20 {
  --tw-shadow-color: rgb(80 129 255 / 0.2);
  --tw-shadow: var(--tw-shadow-colored);
}

.shadow-\[\#5081FF\]\/30 {
  --tw-shadow-color: rgb(80 129 255 / 0.3);
  --tw-shadow: var(--tw-shadow-colored);
}

.shadow-\[\#5081FF\]\/40 {
  --tw-shadow-color: rgb(80 129 255 / 0.4);
  --tw-shadow: var(--tw-shadow-colored);
}

.shadow-\[\#6366F1\]\/25 {
  --tw-shadow-color: rgb(99 102 241 / 0.25);
  --tw-shadow: var(--tw-shadow-colored);
}

.shadow-\[\#6366F1\]\/30 {
  --tw-shadow-color: rgb(99 102 241 / 0.3);
  --tw-shadow: var(--tw-shadow-colored);
}

.shadow-\[\#6366F1\]\/40 {
  --tw-shadow-color: rgb(99 102 241 / 0.4);
  --tw-shadow: var(--tw-shadow-colored);
}

.shadow-\[\#6366F1\]\/50 {
  --tw-shadow-color: rgb(99 102 241 / 0.5);
  --tw-shadow: var(--tw-shadow-colored);
}

.shadow-\[\#6366F1\]\/60 {
  --tw-shadow-color: rgb(99 102 241 / 0.6);
  --tw-shadow: var(--tw-shadow-colored);
}

.shadow-\[\#8B5CF6\]\/30 {
  --tw-shadow-color: rgb(139 92 246 / 0.3);
  --tw-shadow: var(--tw-shadow-colored);
}

.shadow-\[\#8B5CF6\]\/50 {
  --tw-shadow-color: rgb(139 92 246 / 0.5);
  --tw-shadow: var(--tw-shadow-colored);
}

.shadow-\[\#EF4444\]\/25 {
  --tw-shadow-color: rgb(239 68 68 / 0.25);
  --tw-shadow: var(--tw-shadow-colored);
}

.shadow-\[\#F59E0B\]\/25 {
  --tw-shadow-color: rgb(245 158 11 / 0.25);
  --tw-shadow: var(--tw-shadow-colored);
}

.shadow-blue-500 {
  --tw-shadow-color: #3b82f6;
  --tw-shadow: var(--tw-shadow-colored);
}

.shadow-blue-500\/25 {
  --tw-shadow-color: rgb(59 130 246 / 0.25);
  --tw-shadow: var(--tw-shadow-colored);
}

.shadow-emerald-500 {
  --tw-shadow-color: #10b981;
  --tw-shadow: var(--tw-shadow-colored);
}

.shadow-emerald-500\/25 {
  --tw-shadow-color: rgb(16 185 129 / 0.25);
  --tw-shadow: var(--tw-shadow-colored);
}

.shadow-orange-500 {
  --tw-shadow-color: #f97316;
  --tw-shadow: var(--tw-shadow-colored);
}

.shadow-orange-500\/25 {
  --tw-shadow-color: rgb(249 115 22 / 0.25);
  --tw-shadow: var(--tw-shadow-colored);
}

.shadow-purple-500 {
  --tw-shadow-color: #a855f7;
  --tw-shadow: var(--tw-shadow-colored);
}

.shadow-purple-500\/25 {
  --tw-shadow-color: rgb(168 85 247 / 0.25);
  --tw-shadow: var(--tw-shadow-colored);
}

.shadow-red-500 {
  --tw-shadow-color: #ef4444;
  --tw-shadow: var(--tw-shadow-colored);
}

.shadow-red-500\/25 {
  --tw-shadow-color: rgb(239 68 68 / 0.25);
  --tw-shadow: var(--tw-shadow-colored);
}

.shadow-transparent {
  --tw-shadow-color: transparent;
  --tw-shadow: var(--tw-shadow-colored);
}

.outline-none {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.outline {
  outline-style: solid;
}

.ring {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.ring-2 {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.ring-4 {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.ring-white {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(255 255 255 / var(--tw-ring-opacity, 1));
}

.blur {
  --tw-blur: blur(8px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.blur-2xl {
  --tw-blur: blur(40px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.blur-3xl {
  --tw-blur: blur(64px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.blur-sm {
  --tw-blur: blur(4px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.blur-xl {
  --tw-blur: blur(24px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.brightness-110 {
  --tw-brightness: brightness(1.1);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.drop-shadow {
  --tw-drop-shadow: drop-shadow(0 1px 2px rgb(0 0 0 / 0.1)) drop-shadow(0 1px 1px rgb(0 0 0 / 0.06));
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.drop-shadow-lg {
  --tw-drop-shadow: drop-shadow(0 10px 8px rgb(0 0 0 / 0.04)) drop-shadow(0 4px 3px rgb(0 0 0 / 0.1));
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.grayscale {
  --tw-grayscale: grayscale(100%);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.\!invert {
  --tw-invert: invert(100%) !important;
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow) !important;
}

.invert {
  --tw-invert: invert(100%);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.saturate-100 {
  --tw-saturate: saturate(1);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.saturate-50 {
  --tw-saturate: saturate(.5);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.\!filter {
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow) !important;
}

.filter {
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.backdrop-blur-\[2px\] {
  --tw-backdrop-blur: blur(2px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}

.backdrop-blur-lg {
  --tw-backdrop-blur: blur(16px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}

.backdrop-blur-md {
  --tw-backdrop-blur: blur(12px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}

.backdrop-blur-sm {
  --tw-backdrop-blur: blur(4px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}

.backdrop-filter {
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}

.transition {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-\[height\] {
  transition-property: height;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-\[opacity\2c margin\] {
  transition-property: opacity,margin;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-colors {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-opacity {
  transition-property: opacity;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-transform {
  transition-property: transform;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.delay-500 {
  transition-delay: 500ms;
}

.duration-200 {
  transition-duration: 200ms;
}

.duration-300 {
  transition-duration: 300ms;
}

.duration-500 {
  transition-duration: 500ms;
}

.duration-700 {
  transition-duration: 700ms;
}

.ease-in {
  transition-timing-function: cubic-bezier(0.4, 0, 1, 1);
}

.ease-in-out {
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

.ease-out {
  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
}

/* Gaming Shadow Utilities */

.shadow-gaming {
  box-shadow: 0 4px 12px rgba(80, 129, 255, 0.3);
}

.shadow-gaming-lg {
  box-shadow: 0 8px 24px rgba(80, 129, 255, 0.4);
}

.shadow-gaming-xl {
  box-shadow: 0 12px 36px rgba(80, 129, 255, 0.5);
}

/* Gaming Glow Effects */

/* Gaming Text Effects */

/* Gaming Border Effects */

.border-glow {
  border-color: currentColor;
  box-shadow: 0 0 10px currentColor;
}

/* Gaming Background Effects */

/* Gaming Animation Utilities */

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-glow {
  animation: glow 2s ease-in-out infinite;
}

.animate-pulse-glow {
  animation: pulse-glow 3s ease-in-out infinite;
}

.animate-shimmer {
  animation: shimmer 2.5s infinite linear;
}

/* Gaming Clip Path Utilities */

/* Gaming Backdrop Utilities */

/* Gaming Scrollbar Utilities */

/* Gaming Responsive Utilities */

.min-h-home {
  min-height: calc(100vh - 80px);
}

/* Gaming Typography Utilities */

.font-gaming-display {
  font-family: 'League Spartan', 'Signika', ui-sans-serif, system-ui, sans-serif;
}

/* Custom Gaming Account Card Components */

._box-number-account_1eg83_36 {
  display: inline-block;
  border-radius: 0.375rem;
  font-size: 0.75rem;
  line-height: 1rem;
  font-weight: 700;
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

._short-account-border_1eg83_17 {
  position: relative;
  display: block;
  overflow: hidden;
  border-radius: 0.5rem;
}

.gaming-image-gallery ._short-account-border_1eg83_17 {
  /* ENHANCED: Improved height constraints for better visibility */
  max-height: 85vh;
  height: auto;
  display: flex;
  align-items: center;
  justify-content: center;
  visibility: visible;
  /* ENHANCED: Ensure proper positioning */
  position: relative;
  z-index: 1;
}

._image-container_1eg83_26 {
  position: relative;
  display: block;
  height: 12rem;
  width: 100%;
  overflow: hidden;
  border-radius: 0.5rem;
}

.gaming-image-gallery ._image-container_1eg83_26 {
  /* ENHANCED: Improved height constraints for better visibility */
  max-height: 85vh;
  height: auto;
  display: flex;
  align-items: center;
  justify-content: center;
  visibility: visible;
  /* ENHANCED: Ensure proper positioning */
  position: relative;
  z-index: 1;
}

._image-container_1eg83_26 img {
  height: 100%;
  width: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  transition-property: transform;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
}

._image-container_1eg83_26 img:hover {
  --tw-scale-x: 1.05;
  --tw-scale-y: 1.05;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

._button-icon_1eg83_47 {
  display: inline-flex;
  width: 100%;
  align-items: center;
  justify-content: center;
  border-radius: 0.5rem;
  --tw-bg-opacity: 1;
  background-color: rgb(80 129 255 / var(--tw-bg-opacity, 1));
  padding-left: 1rem;
  padding-right: 1rem;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  font-weight: 600;
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
  background-color: #5081FF;
}

._button-icon_1eg83_47:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(52 99 219 / var(--tw-bg-opacity, 1));
}

._button-icon_1eg83_47 svg {
  margin-right: 0.5rem;
}

.\[--placement\:bottom-left\] {
  --placement: bottom-left;
}

.\[--placement\:bottom-right\] {
  --placement: bottom-right;
}

/* Custom Base Styles */

/* Custom Component Styles */

/* Custom Utility Styles */

/* Gaming Keyframe Animations */

@keyframes shimmer {
  0% {
    left: -100%;
    transform: translateX(-100%);
  }

  100% {
    left: 100%;
    transform: translateX(100%);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }

  50% {
    transform: translateY(-10px);
  }
}

@keyframes glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(99, 102, 241, 0.3);
  }

  50% {
    box-shadow: 0 0 30px rgba(99, 102, 241, 0.6);
  }
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(99, 102, 241, 0.4);
    border-color: rgba(99, 102, 241, 0.6);
  }

  50% {
    box-shadow: 0 0 40px rgba(99, 102, 241, 0.8);
    border-color: rgba(99, 102, 241, 1);
  }
}

@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

@media (min-width: 1024px) {
  .recharge-payment-section {
    width: 33.333333% !important;
  }

  .recharge-history-section {
    width: 66.666667% !important;
  }
}

/* Enhanced Gaming Table Core Styles */

.enhanced-gaming-table {
  font-family: 'Signika', sans-serif;
  background: transparent;
  border-collapse: separate;
  border-spacing: 0;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(80, 129, 255, 0.15);
}

/* Gaming Status Badges */

.gaming-status-badge {
  display: inline-flex;
  align-items: center;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border: 1px solid;
  transition: all 0.3s ease;
  font-family: 'Signika', sans-serif;
}

.gaming-status-success {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.2) 0%, rgba(5, 150, 105, 0.2) 100%);
  color: #10B981;
  border-color: rgba(16, 185, 129, 0.3);
  box-shadow: 0 2px 8px rgba(16, 185, 129, 0.2);
}

.gaming-status-warning {
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.2) 0%, rgba(217, 119, 6, 0.2) 100%);
  color: #F59E0B;
  border-color: rgba(245, 158, 11, 0.3);
  box-shadow: 0 2px 8px rgba(245, 158, 11, 0.2);
}

.gaming-status-danger {
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.2) 0%, rgba(220, 38, 38, 0.2) 100%);
  color: #EF4444;
  border-color: rgba(239, 68, 68, 0.3);
  box-shadow: 0 2px 8px rgba(239, 68, 68, 0.2);
}

.gaming-status-info {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.2) 0%, rgba(37, 99, 235, 0.2) 100%);
  color: #3B82F6;
  border-color: rgba(59, 130, 246, 0.3);
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.2);
}

/* Enhanced Gaming Table Headers */

.enhanced-gaming-table thead tr {
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.95) 0%, rgba(30, 41, 59, 0.95) 50%, rgba(15, 23, 42, 0.95) 100%);
  border-bottom: 2px solid rgba(80, 129, 255, 0.4);
  position: relative;
}

/* .enhanced-gaming-table thead tr::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, transparent, rgba(80, 129, 255, 0.6), transparent);
} */

.enhanced-gaming-table th {
  background: transparent;
  color: #FFFFFF;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 1px;
  padding: 16px 12px;
  font-size: 12px;
  text-align: left;
  border: none;
  position: relative;
  white-space: nowrap;
}

.enhanced-gaming-table th::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60%;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(80, 129, 255, 0.5), transparent);
}

/* Enhanced Gaming Table Body */

.enhanced-gaming-table tbody tr {
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.4) 0%, rgba(30, 41, 59, 0.4) 100%);
  border-bottom: 1px solid rgba(80, 129, 255, 0.1);
  transition: all 0.3s ease;
  position: relative;
}

.enhanced-gaming-table tbody tr:nth-child(even) {
  background: linear-gradient(135deg, rgba(30, 41, 59, 0.3) 0%, rgba(15, 23, 42, 0.3) 100%);
}

.enhanced-gaming-table tbody tr:hover {
  background: linear-gradient(135deg, rgba(80, 129, 255, 0.15) 0%, rgba(52, 99, 219, 0.15) 100%);
  border-color: rgba(80, 129, 255, 0.3);
  transform: translateY(-1px);
  box-shadow: 0 4px 16px rgba(80, 129, 255, 0.2);
}

/* .enhanced-gaming-table tbody tr:hover::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background: linear-gradient(180deg, #5081FF, #3463DB);
  border-radius: 0 2px 2px 0;
} */

.enhanced-gaming-table td {
  padding: 14px 12px;
  color: #FFFFFFCC;
  font-size: 13px;
  font-weight: 500;
  border: none;
  transition: all 0.3s ease;
  vertical-align: middle;
}

.enhanced-gaming-table tbody tr:hover td {
  color: #FFFFFF;
}

/* Gaming Table Triangular Accents */

.enhanced-gaming-table tbody tr::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 0 8px 8px 0;
  border-color: transparent rgba(80, 129, 255, 0.2) transparent transparent;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.enhanced-gaming-table tbody tr:hover::after {
  opacity: 1;
}

/* Enhanced Gaming DataTable Pagination */

.datatable-pagination {
  margin-top: 24px;
  padding: 16px;
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.6) 0%, rgba(30, 41, 59, 0.6) 100%);
  border-radius: 12px;
  border: 1px solid rgba(80, 129, 255, 0.2);
}

.datatable-pagination .datatable-pagination-list {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.datatable-pagination .datatable-pagination-list .datatable-pagination-list-item .datatable-pagination-list-item-link {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 40px;
  height: 40px;
  padding: 8px 12px;
  background: linear-gradient(135deg, rgba(30, 41, 59, 0.8) 0%, rgba(15, 23, 42, 0.8) 100%);
  border: 2px solid rgba(80, 129, 255, 0.3);
  border-radius: 8px;
  color: #FFFFFFCC;
  text-decoration: none;
  font-weight: 600;
  font-size: 14px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  font-family: 'Signika', sans-serif;
}

.datatable-pagination .datatable-pagination-list .datatable-pagination-list-item .datatable-pagination-list-item-link::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(80, 129, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.datatable-pagination .datatable-pagination-list .datatable-pagination-list-item .datatable-pagination-list-item-link:hover::before {
  left: 100%;
}

.datatable-pagination .datatable-pagination-list .datatable-pagination-list-item .datatable-pagination-list-item-link:hover,
.datatable-pagination .datatable-pagination-list .datatable-pagination-list-item.datatable-pagination-active .datatable-pagination-list-item-link {
  background: linear-gradient(135deg, #5081FF 0%, #3463DB 100%);
  border-color: #5081FF;
  color: #FFFFFF;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(80, 129, 255, 0.4);
}

.datatable-pagination .datatable-pagination-list .datatable-pagination-list-item.datatable-pagination-active .datatable-pagination-list-item-link {
  box-shadow: 0 6px 20px rgba(80, 129, 255, 0.6);
}

/* Enhanced Gaming Search and Controls */

.datatable-top {
  margin-bottom: 20px;
  padding: 16px;
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.6) 0%, rgba(30, 41, 59, 0.6) 100%);
  border-radius: 12px;
  border: 1px solid rgba(80, 129, 255, 0.2);
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 16px;
}

.datatable-search {
  position: relative;
}

.datatable-search input {
  background: linear-gradient(135deg, rgba(30, 41, 59, 0.8) 0%, rgba(15, 23, 42, 0.8) 100%);
  border: 2px solid rgba(80, 129, 255, 0.3);
  border-radius: 10px;
  color: #FFFFFF;
  padding: 12px 16px 12px 44px;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
  min-width: 280px;
  font-family: 'Signika', sans-serif;
}

.datatable-search input::-moz-placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.datatable-search input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.datatable-search input:focus {
  border-color: #5081FF;
  outline: none;
  box-shadow: 0 0 0 4px rgba(80, 129, 255, 0.2);
  background: linear-gradient(135deg, rgba(30, 41, 59, 0.9) 0%, rgba(15, 23, 42, 0.9) 100%);
}

.datatable-search::before {
  content: '';
  position: absolute;
  left: 16px;
  top: 50%;
  transform: translateY(-50%);
  width: 16px;
  height: 16px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='%23FFFFFFCC'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z'/%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  pointer-events: none;
}

.datatable-selector {
  position: relative;
}

.datatable-selector select {
  background: linear-gradient(135deg, rgba(30, 41, 59, 0.8) 0%, rgba(15, 23, 42, 0.8) 100%);
  border: 2px solid rgba(80, 129, 255, 0.3);
  border-radius: 10px;
  color: #FFFFFF;
  padding: 12px 40px 12px 16px;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  cursor: pointer;
  font-family: 'Signika', sans-serif;
}

.datatable-selector select:focus {
  border-color: #5081FF;
  outline: none;
  box-shadow: 0 0 0 4px rgba(80, 129, 255, 0.2);
}

.datatable-selector::after {
  content: '';
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
  width: 12px;
  height: 12px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='%23FFFFFFCC'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M19 9l-7 7-7-7'/%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  pointer-events: none;
}

/* Enhanced Gaming Info Text */

.datatable-info {
  color: #FFFFFFCC;
  font-size: 14px;
  font-weight: 500;
  padding: 12px 16px;
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.6) 0%, rgba(30, 41, 59, 0.6) 100%);
  border-radius: 8px;
  border: 1px solid rgba(80, 129, 255, 0.2);
  font-family: 'Signika', sans-serif;
}

/* Enhanced Gaming Loading Animation */

.gaming-loading-item {
  position: relative;
  overflow: hidden;
  border-radius: 8px;
}

.gaming-loading-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(80, 129, 255, 0.3), transparent);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% {
    left: -100%;
    transform: translateX(-100%);
  }

  100% {
    left: 100%;
    transform: translateX(100%);
  }
}

/* Enhanced Gaming Table Responsive Design */

@media (max-width: 768px) {
  .gaming-table-container {
    padding: 12px;
  }

  .enhanced-gaming-table th,
  .enhanced-gaming-table td {
    padding: 10px 8px;
    font-size: 12px;
  }

  .enhanced-gaming-table th {
    font-size: 11px;
  }

  .gaming-status-badge {
    padding: 3px 6px;
    font-size: 10px;
  }

  .gaming-status-badge svg {
    width: 10px;
    height: 10px;
  }

  .datatable-pagination .datatable-pagination-list .datatable-pagination-list-item .datatable-pagination-list-item-link {
    min-width: 36px;
    height: 36px;
    padding: 6px 10px;
    font-size: 12px;
  }

  .datatable-search input {
    min-width: 200px;
    padding: 10px 14px 10px 40px;
    font-size: 13px;
  }

  .datatable-selector select {
    padding: 10px 36px 10px 14px;
    font-size: 13px;
  }

  .datatable-top {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .datatable-info {
    font-size: 13px;
    padding: 10px 14px;
  }
}

@media (max-width: 480px) {
  .enhanced-gaming-table {
    font-size: 11px;
  }

  .enhanced-gaming-table th,
  .enhanced-gaming-table td {
    padding: 8px 6px;
    font-size: 11px;
  }

  .enhanced-gaming-table th {
    font-size: 10px;
  }

  .gaming-status-badge {
    padding: 2px 4px;
    font-size: 9px;
  }

  .datatable-search input {
    min-width: 160px;
  }
}

.gaming-table th {
  background: linear-gradient(135deg, rgba(80, 129, 255, 0.2) 0%, rgba(52, 99, 219, 0.2) 100%);
  color: #FFFFFFCC;
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  padding: 16px 12px;
  border-bottom: 2px solid rgba(80, 129, 255, 0.3);
  font-size: 12px;
}

.gaming-table td {
  padding: 12px;
  border-bottom: 1px solid rgba(80, 129, 255, 0.1);
  color: #FFFFFFCC;
  transition: all 0.3s ease;
}

.gaming-table tr:hover td {
  background: linear-gradient(135deg, rgba(80, 129, 255, 0.1) 0%, rgba(52, 99, 219, 0.1) 100%);
  color: #FFFFFF;
}

/* Gaming Pagination Styles */

.datatable-pagination {
  margin-top: 20px;
}

.datatable-pagination .datatable-pagination-list {
  display: flex;
  justify-content: center;
  gap: 8px;
}

.datatable-pagination .datatable-pagination-list .datatable-pagination-list-item {
  border-radius: 8px;
  overflow: hidden;
}

.datatable-pagination .datatable-pagination-list .datatable-pagination-list-item .datatable-pagination-list-item-link {
  background: rgba(30, 41, 59, 0.5);
  border: 1px solid rgba(80, 129, 255, 0.3);
  color: #FFFFFFCC;
  padding: 8px 12px;
  transition: all 0.3s ease;
  text-decoration: none;
}

.datatable-pagination .datatable-pagination-list .datatable-pagination-list-item .datatable-pagination-list-item-link:hover,
.datatable-pagination .datatable-pagination-list .datatable-pagination-list-item.datatable-pagination-active .datatable-pagination-list-item-link {
  background: linear-gradient(135deg, #5081FF 0%, #3463DB 100%);
  border-color: #5081FF;
  color: #FFFFFF;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(80, 129, 255, 0.3);
}

/* Gaming Search Input */

.datatable-search input {
  background: rgba(30, 41, 59, 0.5);
  border: 2px solid rgba(80, 129, 255, 0.3);
  border-radius: 8px;
  color: #FFFFFF;
  padding: 8px 12px;
  transition: all 0.3s ease;
}

.datatable-search input:focus {
  border-color: #5081FF;
  outline: none;
  box-shadow: 0 0 0 3px rgba(80, 129, 255, 0.2);
}

.datatable-search input::-moz-placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.datatable-search input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

/* Gaming Select Dropdown */

.datatable-selector select {
  background: rgba(30, 41, 59, 0.5);
  border: 2px solid rgba(80, 129, 255, 0.3);
  border-radius: 8px;
  color: #FFFFFF;
  padding: 6px 10px;
  transition: all 0.3s ease;
}

.datatable-selector select:focus {
  border-color: #5081FF;
  outline: none;
  box-shadow: 0 0 0 3px rgba(80, 129, 255, 0.2);
}

/* Gaming Info Text */

.datatable-info {
  color: #FFFFFFCC;
  font-size: 14px;
}

/* Gaming Loading Animation Enhancement */

.gaming-loading-item {
  position: relative;
  overflow: hidden;
}

.gaming-loading-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(80, 129, 255, 0.2), transparent);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% {
    left: -100%;
    transform: translateX(-100%);
  }

  100% {
    left: 100%;
    transform: translateX(100%);
  }
}

/* Enhanced Gaming Form Interactions */

.gaming-select:focus,
.gaming-input:focus {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
}

.gaming-submit-btn:active {
  transform: scale(0.98) translateY(0);
}

/* Advanced Gaming Visual Effects */

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }

  50% {
    transform: translateY(-10px);
  }
}

@keyframes glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(99, 102, 241, 0.3);
  }

  50% {
    box-shadow: 0 0 30px rgba(99, 102, 241, 0.6);
  }
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(99, 102, 241, 0.4);
    border-color: rgba(99, 102, 241, 0.6);
  }

  50% {
    box-shadow: 0 0 40px rgba(99, 102, 241, 0.8);
    border-color: rgba(99, 102, 241, 1);
  }
}

/* Sophisticated Gaming Card Enhancements */

.gaming-bank-card:hover {
  animation: glow 2s ease-in-out infinite;
}

.gaming-bank-card.active {
  animation: pulse-glow 3s ease-in-out infinite;
}

/* Enhanced Shimmer Animation */

.animate-shimmer {
  animation: shimmer 2.5s infinite linear;
}

/* Gaming Panel Background Effects */

.gaming-recharge-panel::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, #6366F1, #8B5CF6, #06B6D4, #10B981, #F59E0B, #EF4444, #6366F1);
  border-radius: 18px;
  opacity: 0.1;
  z-index: -1;
  animation: rotate 8s linear infinite;
}

@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

/* Gaming Panel Responsive */

@media (max-width: 768px) {
  .gaming-history-panel {
    padding: 16px;
  }

  .gaming-table th,
  .gaming-table td {
    padding: 8px 6px;
    font-size: 12px;
  }

  .datatable-pagination .datatable-pagination-list .datatable-pagination-list-item .datatable-pagination-list-item-link {
    padding: 6px 8px;
    font-size: 12px;
  }

  /* Gaming Bank Cards Mobile */

  .gaming-bank-card {
    min-height: 80px;
    padding: 12px;
  }

  .gaming-bank-card .w-12.h-12 {
    width: 2.5rem;
    height: 2.5rem;
  }

  .gaming-bank-card .w-6.h-6 {
    width: 1.25rem;
    height: 1.25rem;
  }
}

/* Enhanced Gaming Bank Card Styles (Matching Game Cards) */

.gaming-bank-card {
  position: relative;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  transform-origin: center;
  will-change: transform, box-shadow, border-color;
  border-radius: 12px;
  overflow: hidden;
}

.gaming-bank-card::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, #5081FF, #3463DB, #4B7DFF, #5081FF);
  border-radius: 14px;
  opacity: 0;
  z-index: -1;
  transition: opacity 0.3s ease;
  animation: rotate 4s linear infinite;
}

.gaming-bank-card:hover::before {
  opacity: 0.3;
}

.gaming-bank-card.active::before {
  opacity: 0.6;
}

@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

/* Gaming Background Pattern Animation */

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }

  100% {
    transform: translateX(100%);
  }
}

.animate-shimmer {
  animation: shimmer 2s infinite;
}

/* Enhanced Gaming Card Hover Effects - Border Only (Matching Game Cards) */

.gaming-card-hover {
  position: relative;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  transform-origin: center;
  will-change: transform, box-shadow, border-color;
  border-radius: 12px;
  overflow: hidden;
}

/* Signika Font Declaration */

@font-face {
  font-family: Signika;

  src: url(/assets/webfonts/Signika-SemiBold.ttf);

  font-display: swap;
}

/* Signika Font Application */

* {
  font-family: 'Signika', sans-serif;
}

/* Signika Utility Classes */

.font-signika {
  font-family: 'Signika', sans-serif;
}

/* Override for specific elements */

h1, h2, h3, h4, h5, h6, p, span, div, a, button, input, textarea, select {
  font-family: 'Signika', sans-serif;
}

/* League Spartan Font Classes */

.__className_b661ba {
  font-family: 'League Spartan', Arial, sans-serif;
  font-style: normal
}

.__variable_b661ba {
  --font-league-spartan: "League Spartan", Arial, sans-serif

}

/* League Spartan Utility Classes */

.font-league-spartan {
  font-family: 'League Spartan', Arial, ui-sans-serif, system-ui, sans-serif;
}

.font-spartan {
  font-family: 'League Spartan', Arial, ui-sans-serif, system-ui, sans-serif;
}

/* Gaming Color Utility Classes */

/* Background Colors */

.bg-gaming-purple {
  background-color: #272450 !important;
}

.bg-gaming-blue {
  background-color: #5081FF !important;
}

.bg-gaming-deep-blue {
  background-color: #3463DB !important;
}

.bg-gaming-light-blue {
  background-color: #4B7DFF !important;
}

.bg-gaming-green {
  background-color: #00FF88 !important;
}

.bg-gaming-gold {
  background-color: #FFD700 !important;
}

.bg-gaming-red {
  background-color: #FF6B6B !important;
}

.bg-gaming-orange {
  background-color: #FF8C00 !important;
}

.bg-gaming-violet {
  background-color: #9C27B0 !important;
}

.bg-gaming-dark {
  background-color: #0F172A !important;
}

.bg-gaming-slate {
  background-color: #1E293B !important;
}

.bg-gaming-navy {
  background-color: #13112E !important;
}

/* Border Colors */

.border-gaming-purple {
  border-color: #272450 !important;
}

.border-gaming-blue {
  border-color: #5081FF !important;
}

.border-gaming-deep-blue {
  border-color: #3463DB !important;
}

.border-gaming-light-blue {
  border-color: #4B7DFF !important;
}

.border-gaming-green {
  border-color: #00FF88 !important;
}

.border-gaming-gold {
  border-color: #FFD700 !important;
}

.border-gaming-red {
  border-color: #FF6B6B !important;
}

.border-gaming-orange {
  border-color: #FF8C00 !important;
}

.border-gaming-violet {
  border-color: #9C27B0 !important;
}

.border-gaming-dark {
  border-color: #0F172A !important;
}

.border-gaming-slate {
  border-color: #1E293B !important;
}

.border-gaming-navy {
  border-color: #13112E !important;
}

/* Text Colors */

.text-gaming-purple {
  color: #272450 !important;
}

.text-gaming-blue {
  color: #5081FF !important;
}

.text-gaming-deep-blue {
  color: #3463DB !important;
}

.text-gaming-light-blue {
  color: #4B7DFF !important;
}

.text-gaming-green {
  color: #00FF88 !important;
}

.text-gaming-gold {
  color: #FFD700 !important;
}

.text-gaming-red {
  color: #FF6B6B !important;
}

.text-gaming-orange {
  color: #FF8C00 !important;
}

.text-gaming-violet {
  color: #9C27B0 !important;
}

.text-gaming-dark {
  color: #0F172A !important;
}

.text-gaming-slate {
  color: #1E293B !important;
}

.text-gaming-navy {
  color: #13112E !important;
}

/* Gaming Shadow Effects */

.shadow-gaming {
  box-shadow: 0 4px 12px rgba(80, 129, 255, 0.3) !important;
}

.shadow-gaming-lg {
  box-shadow: 0 8px 24px rgba(80, 129, 255, 0.4) !important;
}

.shadow-gaming-xl {
  box-shadow: 0 12px 36px rgba(80, 129, 255, 0.5) !important;
}

/* Arbitrary Value Support Classes */

.bg-\[#272450\] {
  background-color: #272450 !important;
}

.bg-\[#5081FF\] {
  background-color: #5081FF !important;
}

.bg-\[#3463DB\] {
  background-color: #3463DB !important;
}

.bg-\[#4B7DFF\] {
  background-color: #4B7DFF !important;
}

.bg-\[#00FF88\] {
  background-color: #00FF88 !important;
}

.bg-\[#FFD700\] {
  background-color: #FFD700 !important;
}

.bg-\[#FF6B6B\] {
  background-color: #FF6B6B !important;
}

.bg-\[#FF8C00\] {
  background-color: #FF8C00 !important;
}

.bg-\[#9C27B0\] {
  background-color: #9C27B0 !important;
}

.bg-\[#0F172A\] {
  background-color: #0F172A !important;
}

.bg-\[#1E293B\] {
  background-color: #1E293B !important;
}

.bg-\[#13112E\] {
  background-color: #13112E !important;
}

.border-\[#272450\] {
  border-color: #272450 !important;
}

.border-\[#5081FF\] {
  border-color: #5081FF !important;
}

.border-\[#3463DB\] {
  border-color: #3463DB !important;
}

.border-\[#4B7DFF\] {
  border-color: #4B7DFF !important;
}

.border-\[#00FF88\] {
  border-color: #00FF88 !important;
}

.border-\[#FFD700\] {
  border-color: #FFD700 !important;
}

.border-\[#FF6B6B\] {
  border-color: #FF6B6B !important;
}

.border-\[#FF8C00\] {
  border-color: #FF8C00 !important;
}

.border-\[#9C27B0\] {
  border-color: #9C27B0 !important;
}

.border-\[#0F172A\] {
  border-color: #0F172A !important;
}

.border-\[#1E293B\] {
  border-color: #1E293B !important;
}

.border-\[#13112E\] {
  border-color: #13112E !important;
}

.text-\[#FFFFFF\] {
  color: #FFFFFF !important;
}

.text-\[#FFFFFFCC\] {
  color: #FFFFFFCC !important;
}

.text-\[#FFFFFF99\] {
  color: #FFFFFF99 !important;
}

.text-\[#FFFFFF66\] {
  color: #FFFFFF66 !important;
}

.text-\[#272450\] {
  color: #272450 !important;
}

.text-\[#5081FF\] {
  color: #5081FF !important;
}

.text-\[#3463DB\] {
  color: #3463DB !important;
}

.text-\[#4B7DFF\] {
  color: #4B7DFF !important;
}

.text-\[#00FF88\] {
  color: #00FF88 !important;
}

.text-\[#FFD700\] {
  color: #FFD700 !important;
}

.text-\[#FF6B6B\] {
  color: #FF6B6B !important;
}

.text-\[#FF8C00\] {
  color: #FF8C00 !important;
}

.text-\[#9C27B0\] {
  color: #9C27B0 !important;
}

.text-\[#0F172A\] {
  color: #0F172A !important;
}

.text-\[#1E293B\] {
  color: #1E293B !important;
}

.text-\[#13112E\] {
  color: #13112E !important;
}

/* Custom Background Color Utilities */

/* Primary Background Colors */

.bg-dark {
  --tw-bg-opacity: 1;
  background-color: rgb(14 23 38 / var(--tw-bg-opacity));
}

.bg-white {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}

.bg-light {
  --tw-bg-opacity: 1;
  background-color: rgb(250 250 250 / var(--tw-bg-opacity));
}

.bg-primary {
  --tw-bg-opacity: 1;
  background-color: rgb(67 97 238 / var(--tw-bg-opacity));
}

.bg-secondary {
  --tw-bg-opacity: 1;
  background-color: rgb(128 93 202 / var(--tw-bg-opacity));
}

.bg-success {
  --tw-bg-opacity: 1;
  background-color: rgb(0 171 85 / var(--tw-bg-opacity));
}

.bg-info {
  --tw-bg-opacity: 1;
  background-color: rgb(33 150 243 / var(--tw-bg-opacity));
}

.bg-warning {
  --tw-bg-opacity: 1;
  background-color: rgb(226 160 63 / var(--tw-bg-opacity));
}

.bg-danger {
  --tw-bg-opacity: 1;
  background-color: rgb(231 81 90 / var(--tw-bg-opacity));
}

/* Gray Scale Background Colors */

.bg-gray-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity));
}

.bg-gray-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity));
}

.bg-gray-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(229 231 235 / var(--tw-bg-opacity));
}

.bg-gray-300 {
  --tw-bg-opacity: 1;
  background-color: rgb(209 213 219 / var(--tw-bg-opacity));
}

.bg-gray-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(156 163 175 / var(--tw-bg-opacity));
}

.bg-gray-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(107 114 128 / var(--tw-bg-opacity));
}

.bg-gray-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(75 85 99 / var(--tw-bg-opacity));
}

.bg-gray-700 {
  --tw-bg-opacity: 1;
  background-color: rgb(55 65 81 / var(--tw-bg-opacity));
}

.bg-gray-800 {
  --tw-bg-opacity: 1;
  background-color: rgb(31 41 55 / var(--tw-bg-opacity));
}

.bg-gray-900 {
  --tw-bg-opacity: 1;
  background-color: rgb(17 24 39 / var(--tw-bg-opacity));
}

/* Dark Mode Variants */

.dark .bg-dark {
  --tw-bg-opacity: 1;
  background-color: rgb(6 8 24 / var(--tw-bg-opacity));
}

.dark .bg-white {
  --tw-bg-opacity: 1;
  background-color: rgb(14 23 38 / var(--tw-bg-opacity));
}

.dark .bg-light {
  --tw-bg-opacity: 1;
  background-color: rgb(26 41 65 / var(--tw-bg-opacity));
}

.dark .bg-gray-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(26 41 65 / var(--tw-bg-opacity));
}

.dark .bg-gray-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(25 30 58 / var(--tw-bg-opacity));
}

.dark .bg-gray-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(59 63 92 / var(--tw-bg-opacity));
}

.dark .bg-gray-300 {
  --tw-bg-opacity: 1;
  background-color: rgb(136 142 168 / var(--tw-bg-opacity));
}

/* Transparent & Opacity Variants */

.bg-transparent {
  background-color: transparent;
}

.bg-black\/10 {
  background-color: rgb(0 0 0 / 0.1);
}

.bg-black\/20 {
  background-color: rgb(0 0 0 / 0.2);
}

.bg-black\/30 {
  background-color: rgb(0 0 0 / 0.3);
}

.bg-black\/50 {
  background-color: rgb(0 0 0 / 0.5);
}

.bg-white\/10 {
  background-color: rgb(255 255 255 / 0.1);
}

.bg-white\/20 {
  background-color: rgb(255 255 255 / 0.2);
}

.bg-white\/30 {
  background-color: rgb(255 255 255 / 0.3);
}

.bg-white\/50 {
  background-color: rgb(255 255 255 / 0.5);
}

/* Gradient Background Utilities */

.bg-gradient-primary {
  background: linear-gradient(135deg, rgb(67 97 238) 0%, rgb(128 93 202) 100%);
}

.bg-gradient-success {
  background: linear-gradient(135deg, rgb(0 171 85) 0%, rgb(33 150 243) 100%);
}

.bg-gradient-warning {
  background: linear-gradient(135deg, rgb(226 160 63) 0%, rgb(231 81 90) 100%);
}

.bg-gradient-dark {
  background: linear-gradient(135deg, rgb(14 23 38) 0%, rgb(26 41 65) 100%);
}

/* ===== CORE STYLES ===== */

.gaming-ripple {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transform: scale(0);
  animation: gaming-ripple-animation 0.6s linear;
  pointer-events: none;
}

@keyframes gaming-ripple-animation {
  to {
    transform: scale(4);
    opacity: 0;
  }
}

/* ===== PAGINATION SYSTEM ===== */

.gaming-pagination-bullet {
  width: 16px;
  height: 16px;
  background: rgba(80, 129, 255, 0.4);
  border: 3px solid rgba(80, 129, 255, 0.6);
  border-radius: 50%;
  margin: 0 10px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 
    0 4px 15px rgba(0, 0, 0, 0.2),
    0 0 10px rgba(80, 129, 255, 0.3);
  display: inline-block;
  visibility: visible;
  z-index: 10;
}

.gaming-pagination-bullet:hover {
  background: rgba(80, 129, 255, 0.7);
  border-color: rgba(80, 129, 255, 0.8);
  transform: scale(1.2);
  box-shadow: 
    0 6px 20px rgba(0, 0, 0, 0.3),
    0 0 15px rgba(80, 129, 255, 0.5);
}

.gaming-pagination-bullet.swiper-pagination-bullet-active {
  background: linear-gradient(135deg, #5081FF 0%, #3463DB 100%);
  border-color: #5081FF;
  box-shadow: 
    0 8px 25px rgba(0, 0, 0, 0.3),
    0 0 20px rgba(80, 129, 255, 0.7);
  transform: scale(1.3);
}

/* ===== TOOLTIP THEME ===== */

.tippy-box[data-theme~='gaming'] {
  background: linear-gradient(135deg, 
    rgba(30, 41, 59, 0.95) 0%, 
    rgba(15, 23, 42, 0.98) 100%);
  border: 1px solid rgba(80, 129, 255, 0.3);
  border-radius: 0.5rem;
  -webkit-backdrop-filter: blur(8px);
          backdrop-filter: blur(8px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
}

.tippy-box[data-theme~='gaming'] .tippy-content {
  color: #ffffff;
  font-weight: 500;
  padding: 0.5rem 0.75rem;
}

/* ===== GAMING ACCOUNT PAGE THEME ===== */

.gaming-account-container {
  background: linear-gradient(135deg,
      rgba(15, 23, 42, 0.95) 0%,
      rgba(30, 41, 59, 0.9) 50%,
      rgba(15, 23, 42, 0.95) 100%);
  min-height: 100vh;
  position: relative;
  overflow: hidden;
}

.gaming-account-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
      radial-gradient(circle at 20% 80%, rgba(80, 129, 255, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 80% 20%, rgba(52, 99, 219, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 40% 40%, rgba(139, 92, 246, 0.05) 0%, transparent 50%);
  pointer-events: none;
  z-index: 1;
}

/* Gaming Card Styles */

.gaming-account-card {
  background: linear-gradient(145deg,
      rgba(30, 41, 59, 0.8) 0%,
      rgba(15, 23, 42, 0.9) 50%,
      rgba(30, 41, 59, 0.8) 100%);
  border: 2px solid rgba(80, 129, 255, 0.3);
  border-radius: 1rem;
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  box-shadow:
      0 20px 25px -5px rgba(0, 0, 0, 0.3),
      0 10px 10px -5px rgba(0, 0, 0, 0.2),
      0 0 0 1px rgba(80, 129, 255, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  z-index: 2;
}

.gaming-account-card:hover {
  border-color: rgba(80, 129, 255, 0.5);
  box-shadow:
      0 25px 50px rgba(80, 129, 255, 0.2),
      0 0 30px rgba(80, 129, 255, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.15);
  transform: translateY(-2px);
}

/* ENHANCED: Gaming Image Gallery with Improved Visibility and Layout */

.gaming-image-gallery {
  background: linear-gradient(145deg,
      rgba(20, 25, 40, 0.95) 0%,
      rgba(10, 15, 30, 0.98) 100%);
  /* ENHANCED: Increased border size for better visibility */
  border: 3px solid rgba(80, 129, 255, 0.6);
  border-radius: 1rem;
  overflow: visible;
  /* ENHANCED: Allow glow effects to show */
  position: relative;
  /* ENHANCED: Improved height constraints for better visibility */
  max-height: 85vh;
  /* ENHANCED: Ensure full visibility */
  display: block !important;
  visibility: visible !important;
  /* FIXED: Better container layout with proper padding */
  padding: 0;
  margin: 0 auto;
  width: 100%;
  max-width: 100%;
  /* ENHANCED: Enhanced shadow for depth */
  box-shadow:
      0 8px 32px rgba(0, 0, 0, 0.3),
      0 0 0 1px rgba(80, 129, 255, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

/* ENHANCED: Force gallery container visibility with improved dimensions */

.gaming-image-gallery .relative {
  /* ENHANCED: Improved height constraints for better visibility */
  max-height: 85vh !important;
  height: auto !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  visibility: visible !important;
  /* ENHANCED: Ensure proper positioning */
  position: relative !important;
  z-index: 1 !important;
}

/* ENHANCED: Ensure Swiper container is always visible with improved dimensions */

.gaming-image-gallery .swiper {
  /* ENHANCED: Improved height constraints for better visibility */
  max-height: 85vh !important;
  height: auto !important;
  display: block !important;
  visibility: visible !important;
  /* ENHANCED: Ensure proper positioning */
  position: relative !important;
  z-index: 2 !important;
  /* ENHANCED: Enhanced border for Swiper container */
  border-radius: 0.75rem;
  overflow: hidden;
}

/* ENHANCED: Ensure slides are visible with improved dimensions */

.gaming-image-gallery .swiper-slide {
  /* ENHANCED: Improved height constraints for better visibility */
  max-height: 85vh !important;
  height: auto !important;
  display: flex !important;
  visibility: visible !important;
  align-items: center !important;
  justify-content: center !important;
  /* ENHANCED: Ensure proper positioning */
  position: relative !important;
  z-index: 3 !important;
}

/* ENHANCED: Improved glow effect with better visibility */

.gaming-image-gallery::before {
  content: '';
  position: absolute;
  top: -3px;
  left: -3px;
  right: -3px;
  bottom: -3px;
  background: linear-gradient(45deg,
      rgba(80, 129, 255, 0.6),
      rgba(52, 99, 219, 0.4),
      rgba(139, 92, 246, 0.5));
  border-radius: 1.125rem;
  z-index: -1;
  opacity: 0.3;
  /* ENHANCED: Always visible with subtle glow */
  transition: opacity 0.3s ease;
  /* ENHANCED: Enhanced glow effect */
  filter: blur(8px);
}

.gaming-image-gallery:hover::before {
  opacity: 0.8;
  /* ENHANCED: Stronger glow on hover */
  filter: blur(12px);
}

/* Gaming Buttons */

.gaming-btn-primary {
  background: linear-gradient(135deg, #5081FF 0%, #3463DB 100%);
  border: 2px solid rgba(80, 129, 255, 0.3);
  color: white;
  font-weight: 600;
  padding: 0.75rem 1.5rem;
  border-radius: 0.75rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.gaming-btn-primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.gaming-btn-primary:hover::before {
  left: 100%;
}

.gaming-btn-primary:hover {
  background: linear-gradient(135deg, #6366F1 0%, #4F46E5 100%);
  border-color: rgba(99, 102, 241, 0.5);
  box-shadow:
      0 10px 25px rgba(80, 129, 255, 0.3),
      0 0 20px rgba(80, 129, 255, 0.2);
  transform: translateY(-2px);
}

/* Gaming Info Cards */

.gaming-info-card {
  background: linear-gradient(135deg,
      rgba(80, 129, 255, 0.1) 0%,
      rgba(52, 99, 219, 0.05) 100%);
  border: 1px solid rgba(80, 129, 255, 0.2);
  border-radius: 0.75rem;
  padding: 1rem;
  -webkit-backdrop-filter: blur(8px);
          backdrop-filter: blur(8px);
  transition: all 0.3s ease;
}

.gaming-info-card:hover {
  border-color: rgba(80, 129, 255, 0.4);
  background: linear-gradient(135deg,
      rgba(80, 129, 255, 0.15) 0%,
      rgba(52, 99, 219, 0.1) 100%);
}

/* Gaming Typography */

.gaming-title {
  background: linear-gradient(135deg, #FFFFFF 0%, #C7D2FE 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.gaming-accent-text {
  color: #5081FF;
  font-weight: 600;
}

/* Gaming Status Indicators */

.gaming-status-available {
  background: linear-gradient(135deg, #10B981 0%, #059669 100%);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  font-weight: 600;
  text-align: center;
}

.gaming-status-sold {
  background: linear-gradient(135deg, #EF4444 0%, #DC2626 100%);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  font-weight: 600;
  text-align: center;
}

/* Gaming Attribute Icons */

.gaming-attribute-icon {
  position: relative;
  width: 3rem;
  height: 3rem;
  border-radius: 0.5rem;
  overflow: hidden;
  border: 2px solid rgba(80, 129, 255, 0.3);
  transition: all 0.3s ease;
  background: linear-gradient(145deg,
      rgba(30, 41, 59, 0.8) 0%,
      rgba(15, 23, 42, 0.9) 100%);
}

.gaming-attribute-icon::before {
  content: '';
  position: absolute;
  inset: 0;
  background-image: url('/assets/image/d7bb309392a99e4179abbf83cba73c3d.png');
  background-size: cover;
  background-position: center;
  opacity: 0.3;
}

.gaming-attribute-icon:hover {
  border-color: rgba(80, 129, 255, 0.6);
  transform: scale(1.05);
  box-shadow: 0 8px 25px rgba(80, 129, 255, 0.3);
}

.gaming-attribute-icon img {
  position: relative;
  z-index: 2;
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}

/* ===== ENHANCED GAMING ATTRIBUTES SECTION STYLES ===== */

/* Enhanced Attributes Container */

.gaming-attributes-container {
  background: linear-gradient(145deg,
      rgba(30, 41, 59, 0.95) 0%,
      rgba(15, 23, 42, 0.98) 50%,
      rgba(30, 41, 59, 0.95) 100%);
  border: 2px solid rgba(80, 129, 255, 0.4);
  border-radius: 1.25rem;
  backdrop-filter: blur(16px);
  -webkit-backdrop-filter: blur(16px);
  box-shadow:
      0 25px 50px rgba(0, 0, 0, 0.4),
      0 0 0 1px rgba(80, 129, 255, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);
  position: relative;
  overflow: hidden;
}

.gaming-attributes-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
      radial-gradient(circle at 20% 20%, rgba(80, 129, 255, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 80% 80%, rgba(139, 92, 246, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 40% 60%, rgba(52, 99, 219, 0.05) 0%, transparent 50%);
  pointer-events: none;
  z-index: 0;
}

/* Enhanced Section Header */

.gaming-attributes-header {
  position: relative;
  z-index: 1;
  padding: 2rem 2rem 1rem 2rem;
  border-bottom: 1px solid rgba(80, 129, 255, 0.2);
  background: linear-gradient(135deg,
      rgba(80, 129, 255, 0.05) 0%,
      rgba(52, 99, 219, 0.03) 100%);
}

.gaming-attributes-title {
  background: linear-gradient(135deg, #FFFFFF 0%, #C7D2FE 50%, #5081FF 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 700;
  font-size: 1.75rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-bottom: 0.5rem;
}

.gaming-attributes-subtitle {
  color: rgba(156, 163, 175, 0.9);
  font-size: 0.95rem;
  font-weight: 400;
  line-height: 1.5;
}

.gaming-attributes-icon {
  background: linear-gradient(135deg, #5081FF 0%, #8B5CF6 100%);
  border-radius: 0.75rem;
  padding: 0.75rem;
  box-shadow:
      0 8px 25px rgba(80, 129, 255, 0.3),
      0 0 20px rgba(80, 129, 255, 0.2);
  position: relative;
}

.gaming-attributes-icon::before {
  content: '';
  position: absolute;
  inset: -2px;
  background: linear-gradient(45deg, #5081FF, #8B5CF6, #3463DB);
  border-radius: 0.875rem;
  z-index: -1;
  opacity: 0.6;
  filter: blur(4px);
}

/* ===== ENHANCED GAMING SIDEBAR LAYOUT STYLES ===== */

.gaming-sidebar-thumbnails {
  background: linear-gradient(135deg,
      rgba(20, 25, 40, 0.95) 0%,
      rgba(10, 15, 30, 0.98) 100%);
  border: 2px solid rgba(80, 129, 255, 0.4);
  border-radius: 0.75rem;
  padding: 1rem;
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  box-shadow:
      0 8px 25px rgba(0, 0, 0, 0.3),
      0 0 0 1px rgba(80, 129, 255, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.gaming-sidebar-thumbnails:hover {
  border-color: rgba(80, 129, 255, 0.6);
  box-shadow:
      0 12px 35px rgba(0, 0, 0, 0.4),
      0 0 0 1px rgba(80, 129, 255, 0.3),
      0 0 20px rgba(80, 129, 255, 0.15),
      inset 0 1px 0 rgba(255, 255, 255, 0.15);
}

.gaming-sidebar-thumbnail-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(60px, 1fr));
  gap: 0.5rem;
  margin-top: 0.75rem;
  max-height: 300px;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: rgba(80, 129, 255, 0.5) rgba(30, 41, 59, 0.3);
}

.gaming-sidebar-thumbnail {
  aspect-ratio: 1;
  border-radius: 0.5rem;
  overflow: hidden;
  position: relative;
  cursor: pointer;
  background: linear-gradient(135deg,
      rgba(30, 41, 59, 0.8) 0%,
      rgba(15, 23, 42, 0.9) 100%);
  border: 2px solid rgba(80, 129, 255, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.gaming-sidebar-thumbnail:hover {
  border-color: rgba(80, 129, 255, 0.6);
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(80, 129, 255, 0.2);
}

.gaming-sidebar-thumbnail.active {
  border-color: rgba(80, 129, 255, 0.8);
  box-shadow: 0 0 15px rgba(80, 129, 255, 0.4);
}

.gaming-sidebar-thumbnail img {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  transition: all 0.3s ease;
}

/* ===== ENHANCED GAMING MAIN GALLERY STYLES ===== */

.gaming-main-gallery {
  background: linear-gradient(135deg,
      rgba(20, 25, 40, 0.95) 0%,
      rgba(10, 15, 30, 0.98) 100%);
  border: 2px solid rgba(80, 129, 255, 0.4);
  border-radius: 1rem;
  overflow: hidden;
  box-shadow:
      0 10px 30px rgba(0, 0, 0, 0.3),
      0 0 0 1px rgba(80, 129, 255, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.gaming-grid-header {
  padding: 1.5rem;
  border-bottom: 1px solid rgba(80, 129, 255, 0.2);
  background: linear-gradient(135deg,
      rgba(30, 41, 59, 0.8) 0%,
      rgba(15, 23, 42, 0.9) 100%);
}

.gaming-grid-stats {
  background: linear-gradient(135deg,
      rgba(80, 129, 255, 0.1) 0%,
      rgba(52, 99, 219, 0.1) 100%);
  border: 1px solid rgba(80, 129, 255, 0.3);
  border-radius: 0.5rem;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
}

.gaming-grid-container {
  position: relative;
}

.gaming-featured-display {
  padding: 1.5rem;
}

/* ===== GAMING GRID DISPLAY STYLES ===== */

.gaming-grid-display {
  padding: 1.5rem;
}

.gaming-grid-items {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 1rem;
  max-height: 600px;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: rgba(80, 129, 255, 0.5) rgba(30, 41, 59, 0.3);
}

.gaming-grid-item {
  aspect-ratio: 1;
  border-radius: 0.75rem;
  overflow: hidden;
  position: relative;
  cursor: pointer;
  background: linear-gradient(135deg,
      rgba(30, 41, 59, 0.8) 0%,
      rgba(15, 23, 42, 0.9) 100%);
  border: 2px solid rgba(80, 129, 255, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.gaming-grid-item:hover {
  border-color: rgba(80, 129, 255, 0.6);
  transform: translateY(-4px);
  box-shadow: 0 12px 25px rgba(80, 129, 255, 0.2);
}

.gaming-grid-item.active {
  border-color: rgba(80, 129, 255, 0.8);
  box-shadow: 0 0 20px rgba(80, 129, 255, 0.4);
}

.gaming-grid-item-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.gaming-grid-item-image {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  transition: all 0.3s ease;
}

.gaming-grid-item:hover .gaming-grid-item-image {
  transform: scale(1.05);
}

.gaming-grid-item-overlay {
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg,
      rgba(80, 129, 255, 0.1) 0%,
      rgba(52, 99, 219, 0.2) 100%);
  opacity: 0;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.gaming-grid-item:hover .gaming-grid-item-overlay {
  opacity: 1;
}

.gaming-grid-item-hover-icon {
  background: rgba(255, 255, 255, 0.1);
  -webkit-backdrop-filter: blur(8px);
          backdrop-filter: blur(8px);
  border-radius: 50%;
  padding: 0.75rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
  transform: scale(0.8);
  transition: all 0.3s ease;
}

.gaming-grid-item:hover .gaming-grid-item-hover-icon {
  transform: scale(1);
}

.gaming-grid-item-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: rgba(156, 163, 175, 0.5);
  background: linear-gradient(135deg,
      rgba(55, 65, 81, 0.5) 0%,
      rgba(31, 41, 55, 0.7) 100%);
}

.gaming-grid-item-placeholder svg {
  width: 3rem;
  height: 3rem;
}

/* ===== GAMING THUMBNAIL DISPLAY STYLES ===== */

.gaming-thumbnail-display {
  padding: 1.5rem;
}

.gaming-thumbnail-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid rgba(80, 129, 255, 0.2);
}

.gaming-thumbnail-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1.125rem;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
}

.gaming-thumbnail-count {
  background: linear-gradient(135deg,
      rgba(80, 129, 255, 0.1) 0%,
      rgba(52, 99, 219, 0.1) 100%);
  border: 1px solid rgba(80, 129, 255, 0.3);
  border-radius: 0.5rem;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  color: rgba(80, 129, 255, 0.9);
  font-weight: 600;
}

.gaming-main-thumbnail-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 1rem;
  max-height: 600px;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: rgba(80, 129, 255, 0.5) rgba(30, 41, 59, 0.3);
}

.gaming-main-thumbnail {
  aspect-ratio: 1;
  border-radius: 0.75rem;
  overflow: hidden;
  position: relative;
  cursor: pointer;
  background: linear-gradient(135deg,
      rgba(30, 41, 59, 0.8) 0%,
      rgba(15, 23, 42, 0.9) 100%);
  border: 2px solid rgba(80, 129, 255, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.gaming-main-thumbnail:hover {
  border-color: rgba(80, 129, 255, 0.6);
  transform: translateY(-4px);
  box-shadow: 0 12px 25px rgba(80, 129, 255, 0.2);
}

.gaming-main-thumbnail.active {
  border-color: rgba(80, 129, 255, 0.8);
  box-shadow: 0 0 20px rgba(80, 129, 255, 0.4);
}

.gaming-main-thumbnail-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.gaming-main-thumbnail-image {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  transition: all 0.3s ease;
}

.gaming-main-thumbnail:hover .gaming-main-thumbnail-image {
  transform: scale(1.05);
}

.gaming-main-thumbnail-overlay {
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg,
      rgba(80, 129, 255, 0.1) 0%,
      rgba(52, 99, 219, 0.2) 100%);
  opacity: 0;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.gaming-main-thumbnail:hover .gaming-main-thumbnail-overlay {
  opacity: 1;
}

.gaming-main-thumbnail-hover-icon {
  background: rgba(255, 255, 255, 0.1);
  -webkit-backdrop-filter: blur(8px);
          backdrop-filter: blur(8px);
  border-radius: 50%;
  padding: 0.75rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
  transform: scale(0.8);
  transition: all 0.3s ease;
}

.gaming-main-thumbnail:hover .gaming-main-thumbnail-hover-icon {
  transform: scale(1);
}

/* ===== GAMING VIEW CONTROLS ===== */

.gaming-view-controls {
  position: absolute;
  top: 1rem;
  right: 1rem;
  display: flex;
  gap: 0.5rem;
  z-index: 20;
}

.gaming-view-btn {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 0.5rem;
  background: linear-gradient(135deg,
      rgba(30, 41, 59, 0.9) 0%,
      rgba(15, 23, 42, 0.95) 100%);
  border: 1px solid rgba(80, 129, 255, 0.3);
  color: rgba(156, 163, 175, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  -webkit-backdrop-filter: blur(8px);
          backdrop-filter: blur(8px);
}

.gaming-view-btn:hover {
  border-color: rgba(80, 129, 255, 0.6);
  color: rgba(80, 129, 255, 0.9);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(80, 129, 255, 0.2);
}

.gaming-view-btn.active {
  background: linear-gradient(135deg,
      rgba(80, 129, 255, 0.2) 0%,
      rgba(52, 99, 219, 0.3) 100%);
  border-color: rgba(80, 129, 255, 0.6);
  color: rgba(80, 129, 255, 1);
  box-shadow: 0 0 15px rgba(80, 129, 255, 0.3);
}

/* ===== LEGACY THUMBNAIL GALLERY STYLES (Updated) ===== */

.gaming-thumbnail-gallery {
  /* HIDDEN: Legacy support - now handled by sidebar */
  display: none !important;
}

/* ENHANCED: Hover effect for floating thumbnail gallery */

.gaming-thumbnail-gallery:hover {
  border-color: rgba(80, 129, 255, 0.6);
  box-shadow:
      0 15px 40px rgba(0, 0, 0, 0.5),
      0 0 0 1px rgba(80, 129, 255, 0.3),
      0 0 20px rgba(80, 129, 255, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.15);
}

.gaming-thumbnail-container {
  display: flex !important;
  gap: 0.5rem;
  overflow-x: auto;
  overflow-y: hidden;
  padding: 0.5rem 0;
  scrollbar-width: thin;
  scrollbar-color: rgba(80, 129, 255, 0.5) rgba(30, 41, 59, 0.3);
  min-height: 60px;
  max-height: 120px;
  align-items: flex-start;
  flex-wrap: wrap;
  /* FIXED: Floating layout constraints */
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
  /* ENHANCED: Better scroll behavior */
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
  /* ENHANCED: Grid-like layout for floating panel */
  justify-content: flex-start;
}

.gaming-thumbnail-container::-webkit-scrollbar {
  height: 6px;
}

.gaming-thumbnail-container::-webkit-scrollbar-track {
  background: rgba(30, 41, 59, 0.3);
  border-radius: 3px;
}

.gaming-thumbnail-container::-webkit-scrollbar-thumb {
  background: linear-gradient(90deg, #5081FF, #3463DB);
  border-radius: 3px;
}

.gaming-thumbnail-container::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(90deg, #6366F1, #4F46E5);
}

.gaming-thumbnail {
  position: relative;
  width: 60px;
  /* ENHANCED: Smaller size for floating layout */
  min-width: 60px;
  min-height: 45px;
  border-radius: 0.375rem;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 2px solid rgba(80, 129, 255, 0.3);
  background: linear-gradient(145deg,
      rgba(30, 41, 59, 0.9) 0%,
      rgba(15, 23, 42, 0.95) 100%);
  flex-shrink: 0;
  /* ENHANCED: Remove flex centering to eliminate spacing */
  display: block;
  /* ENHANCED: Ensure border wraps content properly */
  box-sizing: border-box;
  /* ENHANCED: Remove any default padding/margin */
  padding: 0;
  margin: 0;
}

.gaming-thumbnail:hover {
  border-color: rgba(80, 129, 255, 0.6);
  transform: translateY(-2px) scale(1.05);
  box-shadow:
      0 8px 25px rgba(80, 129, 255, 0.3),
      0 0 20px rgba(80, 129, 255, 0.2);
}

.gaming-thumbnail.active {
  border-color: rgba(80, 129, 255, 0.8);
  box-shadow:
      0 0 0 2px rgba(80, 129, 255, 0.4),
      0 8px 25px rgba(80, 129, 255, 0.4),
      0 0 30px rgba(80, 129, 255, 0.3);
  transform: scale(1.1);
}

.gaming-thumbnail::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg,
      rgba(80, 129, 255, 0.6),
      rgba(52, 99, 219, 0.4),
      rgba(139, 92, 246, 0.5));
  border-radius: 0.5rem;
  z-index: -1;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.gaming-thumbnail.active::before {
  opacity: 1;
}

.gaming-thumbnail img {
  width: 100%;
  /* ENHANCED: Fill container completely to eliminate spacing */
  height: 100%;
  max-height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  -o-object-position: center;
     object-position: center;
  transition: all 0.3s ease;
  display: block;
  /* ENHANCED: Absolute positioning for tight border fit */
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
  /* ENHANCED: Ensure proper image rendering */
  image-rendering: -webkit-optimize-contrast;
  image-rendering: crisp-edges;
  /* ENHANCED: Fill container completely */
  max-width: 100%;
  border-radius: 0.375rem;
  /* ENHANCED: Remove any default spacing */
  padding: 0;
  margin: 0;
}

.gaming-thumbnail:hover img {
  transform: scale(1.1);
  filter: brightness(1.1);
}

.gaming-thumbnail-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background: linear-gradient(145deg,
      rgba(75, 85, 99, 0.8) 0%,
      rgba(55, 65, 81, 0.9) 100%);
  color: rgba(156, 163, 175, 0.8);
  /* ENHANCED: Absolute positioning to fill container completely */
  position: absolute;
  top: 0;
  left: 0;
  z-index: 0;
  border-radius: 0.375rem;
  /* ENHANCED: Remove any default spacing */
  padding: 0;
  margin: 0;
  box-sizing: border-box;
}

.gaming-thumbnail-placeholder svg {
  width: 24px;
  height: 24px;
  opacity: 0.6;
}

/* Gaming Thumbnail Gallery Header */

.gaming-thumbnail-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 0.5rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid rgba(80, 129, 255, 0.2);
}

/* ENHANCED: Compact header for floating layout */

@media (min-width: 769px) {
  .gaming-thumbnail-header {
    margin-bottom: 0.375rem;
    padding-bottom: 0.375rem;
  }

  .gaming-thumbnail-title {
    font-size: 0.75rem !important;
  }

  .gaming-thumbnail-count {
    font-size: 0.625rem !important;
    padding: 0.125rem 0.375rem !important;
  }
}

/* ===== ENHANCED ATTRIBUTE SECTION STYLES ===== */

/* Enhanced Attribute Section Container */

.gaming-attribute-section {
  position: relative;
  z-index: 1;
  padding: 1.5rem 2rem;
  margin-bottom: 2rem;
  border-radius: 1rem;
  background: linear-gradient(135deg,
      rgba(80, 129, 255, 0.03) 0%,
      rgba(52, 99, 219, 0.02) 100%);
  border: 1px solid rgba(80, 129, 255, 0.15);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.gaming-attribute-section:hover {
  border-color: rgba(80, 129, 255, 0.25);
  background: linear-gradient(135deg,
      rgba(80, 129, 255, 0.05) 0%,
      rgba(52, 99, 219, 0.03) 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(80, 129, 255, 0.1);
}

/* Enhanced Section Headers */

.gaming-attribute-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid rgba(80, 129, 255, 0.1);
}

.gaming-attribute-title-group {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.gaming-attribute-icon {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 0.75rem;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  box-shadow:
      0 8px 25px rgba(0, 0, 0, 0.2),
      0 0 20px rgba(80, 129, 255, 0.1);
  transition: all 0.3s ease;
}

.gaming-attribute-icon:hover {
  transform: translateY(-2px) scale(1.05);
  box-shadow:
      0 12px 35px rgba(0, 0, 0, 0.3),
      0 0 30px rgba(80, 129, 255, 0.2);
}

.gaming-attribute-icon.character {
  background: linear-gradient(135deg, #10B981 0%, #059669 100%);
}

.gaming-attribute-icon.weapon {
  background: linear-gradient(135deg, #F59E0B 0%, #D97706 100%);
}

.gaming-attribute-icon.skin {
  background: linear-gradient(135deg, #8B5CF6 0%, #7C3AED 100%);
}

.gaming-attribute-title {
  color: #FFFFFF;
  font-weight: 600;
  font-size: 1.25rem;
  letter-spacing: 0.025em;
}

.gaming-attribute-count {
  padding: 0.5rem 1rem;
  border-radius: 2rem;
  font-size: 0.875rem;
  font-weight: 600;
  -webkit-backdrop-filter: blur(8px);
          backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.gaming-attribute-count.character {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.2) 0%, rgba(5, 150, 105, 0.1) 100%);
  color: #34D399;
  border-color: rgba(16, 185, 129, 0.3);
}

.gaming-attribute-count.weapon {
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.2) 0%, rgba(217, 119, 6, 0.1) 100%);
  color: #FBBF24;
  border-color: rgba(245, 158, 11, 0.3);
}

.gaming-attribute-count.skin {
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.2) 0%, rgba(124, 58, 237, 0.1) 100%);
  color: #A78BFA;
  border-color: rgba(139, 92, 246, 0.3);
}

.gaming-attribute-count:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 15px rgba(80, 129, 255, 0.2);
}

.gaming-thumbnail-title {
  color: #FFFFFF;
  font-weight: 600;
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.gaming-thumbnail-count {
  background: linear-gradient(135deg, #5081FF 0%, #3463DB 100%);
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 0.375rem;
  font-size: 0.75rem;
  font-weight: 600;
}

/* ===== ENHANCED ATTRIBUTE CARD STYLES ===== */

/* Enhanced Attribute Grid - Reduced Size */

.gaming-attribute-grid {
  display: grid;
  gap: 0.75rem;
  grid-template-columns: repeat(auto-fill, minmax(60px, 1fr));
}

/* Enhanced Individual Attribute Cards */

.gaming-attribute-card {
  position: relative;
  aspect-ratio: 1;
  border-radius: 0.875rem;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  border: 2px solid rgba(107, 114, 128, 0.3);
  background: linear-gradient(145deg,
      rgba(31, 41, 55, 0.9) 0%,
      rgba(17, 24, 39, 0.95) 100%);
  box-shadow:
      0 4px 15px rgba(0, 0, 0, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.05);
}

.gaming-attribute-card::before {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(45deg,
      transparent 0%,
      rgba(80, 129, 255, 0.1) 50%,
      transparent 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 1;
}

.gaming-attribute-card:hover::before {
  opacity: 1;
}

.gaming-attribute-card:hover {
  transform: translateY(-4px) scale(1.05);
  border-color: rgba(80, 129, 255, 0.6);
  box-shadow:
      0 20px 40px rgba(0, 0, 0, 0.3),
      0 0 30px rgba(80, 129, 255, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

/* Character Cards */

.gaming-attribute-card.character:hover {
  border-color: rgba(16, 185, 129, 0.6);
  box-shadow:
      0 20px 40px rgba(0, 0, 0, 0.3),
      0 0 30px rgba(16, 185, 129, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

/* Weapon Cards */

.gaming-attribute-card.weapon:hover {
  border-color: rgba(245, 158, 11, 0.6);
  box-shadow:
      0 20px 40px rgba(0, 0, 0, 0.3),
      0 0 30px rgba(245, 158, 11, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

/* Skin Cards */

.gaming-attribute-card.skin:hover {
  border-color: rgba(139, 92, 246, 0.6);
  box-shadow:
      0 20px 40px rgba(0, 0, 0, 0.3),
      0 0 30px rgba(139, 92, 246, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

/* Enhanced Background Pattern */

.gaming-attribute-background {
  position: absolute;
  inset: 0;
  background-size: cover;
  background-position: center;
  opacity: 0.15;
  transition: all 0.3s ease;
  z-index: 0;
}

.gaming-attribute-card:hover .gaming-attribute-background {
  opacity: 0.25;
  transform: scale(1.1);
}

/* Enhanced Image Styling */

.gaming-attribute-image {
  position: relative;
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  z-index: 2;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  filter: brightness(1.1) contrast(1.1) saturate(1.05);
}

.gaming-attribute-card:hover .gaming-attribute-image {
  transform: scale(1.1);
  filter: brightness(1.2) contrast(1.2) saturate(1.15);
}

/* Enhanced Hover Overlays */

.gaming-attribute-overlay {
  position: absolute;
  inset: 0;
  background: linear-gradient(to top,
      rgba(0, 0, 0, 0.4) 0%,
      transparent 50%,
      transparent 100%);
  opacity: 0;
  transition: all 0.3s ease;
  z-index: 3;
}

.gaming-attribute-card:hover .gaming-attribute-overlay {
  opacity: 1;
}

.gaming-attribute-overlay.character {
  background: linear-gradient(to top,
      rgba(16, 185, 129, 0.3) 0%,
      transparent 60%);
}

.gaming-attribute-overlay.weapon {
  background: linear-gradient(to top,
      rgba(245, 158, 11, 0.3) 0%,
      transparent 60%);
}

.gaming-attribute-overlay.skin {
  background: linear-gradient(to top,
      rgba(139, 92, 246, 0.3) 0%,
      transparent 60%);
}

/* Enhanced Glow Effects */

.gaming-attribute-glow {
  position: absolute;
  inset: 0;
  border-radius: 0.875rem;
  box-shadow: inset 0 0 20px rgba(80, 129, 255, 0);
  transition: all 0.3s ease;
  z-index: 4;
}

.gaming-attribute-card:hover .gaming-attribute-glow {
  box-shadow: inset 0 0 20px rgba(80, 129, 255, 0.2);
}

.gaming-attribute-card.character:hover .gaming-attribute-glow {
  box-shadow: inset 0 0 20px rgba(16, 185, 129, 0.2);
}

.gaming-attribute-card.weapon:hover .gaming-attribute-glow {
  box-shadow: inset 0 0 20px rgba(245, 158, 11, 0.2);
}

.gaming-attribute-card.skin:hover .gaming-attribute-glow {
  box-shadow: inset 0 0 20px rgba(139, 92, 246, 0.2);
}

/* ENHANCED: Smooth Thumbnail Scrolling */

.gaming-thumbnail-container {
  scroll-behavior: smooth;
}

/* ENHANCED: Tight Border-Image Alignment */

.gaming-thumbnail {
  /* ENHANCED: Ensure borders align with content */
  box-sizing: border-box;
  /* ENHANCED: Remove any default spacing */
  padding: 0 !important;
  margin: 0 !important;
}

/* ENHANCED: Ensure images fill containers completely without spacing */

.gaming-thumbnail img {
  -o-object-fit: cover !important;
     object-fit: cover !important;
  -o-object-position: center !important;
     object-position: center !important;
  /* ENHANCED: Fill container completely to eliminate spacing */
  width: 100% !important;
  height: 100% !important;
  max-width: 100% !important;
  max-height: 100% !important;
  /* ENHANCED: Remove any default spacing */
  padding: 0 !important;
  margin: 0 !important;
  /* ENHANCED: Smooth image transitions */
  transition: transform 0.3s ease, filter 0.3s ease !important;
}

/* ENHANCED: Mobile touch optimization */

@media (hover: none) and (pointer: coarse) {
  .gaming-thumbnail {
    /* ENHANCED: Better touch targets on mobile */
    min-width: 64px !important;
    min-height: 48px !important;
  }

  .gaming-thumbnail img {
    /* ENHANCED: Prevent image zoom on mobile tap */
    touch-action: manipulation !important;
  }
}

/* ENHANCED: Thumbnail Focus States for Accessibility */

.gaming-thumbnail:focus {
  outline: 2px solid rgba(80, 129, 255, 0.8);
  outline-offset: 2px;
  border-color: rgba(80, 129, 255, 0.8);
}

/* ENHANCED: Thumbnail Loading Animation */

.gaming-thumbnail img {
  transition: opacity 0.3s ease, transform 0.3s ease;
}

.gaming-thumbnail img[loading="lazy"] {
  opacity: 0;
}

.gaming-thumbnail img.loaded,
  .gaming-thumbnail img:not([loading="lazy"]) {
  opacity: 1;
}

/* ENHANCED: Thumbnail Hover Animation */

@keyframes thumbnailPulse {
  0% {
    box-shadow: 0 0 0 0 rgba(80, 129, 255, 0.4);
  }

  70% {
    box-shadow: 0 0 0 10px rgba(80, 129, 255, 0);
  }

  100% {
    box-shadow: 0 0 0 0 rgba(80, 129, 255, 0);
  }
}

.gaming-thumbnail.active {
  animation: thumbnailPulse 2s infinite;
}

/* ===== ENHANCED EMPTY STATE STYLES ===== */

.gaming-empty-state {
  text-align: center;
  padding: 3rem 2rem;
  background: linear-gradient(135deg,
      rgba(31, 41, 55, 0.6) 0%,
      rgba(17, 24, 39, 0.8) 100%);
  border: 2px dashed rgba(107, 114, 128, 0.3);
  border-radius: 1rem;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.gaming-empty-state::before {
  content: '';
  position: absolute;
  inset: 0;
  background:
      radial-gradient(circle at 50% 50%, rgba(80, 129, 255, 0.05) 0%, transparent 70%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.gaming-empty-state:hover {
  border-color: rgba(80, 129, 255, 0.4);
  background: linear-gradient(135deg,
      rgba(31, 41, 55, 0.8) 0%,
      rgba(17, 24, 39, 0.9) 100%);
}

.gaming-empty-state:hover::before {
  opacity: 1;
}

.gaming-empty-state-icon {
  width: 4rem;
  height: 4rem;
  background: linear-gradient(135deg,
      rgba(107, 114, 128, 0.2) 0%,
      rgba(75, 85, 99, 0.3) 100%);
  border-radius: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.5rem auto;
  border: 1px solid rgba(107, 114, 128, 0.3);
  transition: all 0.3s ease;
}

.gaming-empty-state:hover .gaming-empty-state-icon {
  background: linear-gradient(135deg,
      rgba(80, 129, 255, 0.2) 0%,
      rgba(52, 99, 219, 0.3) 100%);
  border-color: rgba(80, 129, 255, 0.4);
  transform: scale(1.05);
}

.gaming-empty-state-text {
  color: rgba(156, 163, 175, 0.8);
  font-size: 0.95rem;
  font-weight: 500;
  position: relative;
  z-index: 1;
}

.gaming-empty-state:hover .gaming-empty-state-text {
  color: rgba(156, 163, 175, 1);
}

/* ===== ENHANCED RESPONSIVE DESIGN ===== */

/* Mobile Responsive Enhancements */

@media (max-width: 640px) {
  .gaming-attributes-container {
    border-radius: 1rem;
    margin: 0 -0.5rem;
  }

  .gaming-attributes-header {
    padding: 1.5rem 1.5rem 1rem 1.5rem;
  }

  .gaming-attributes-title {
    font-size: 1.5rem;
  }

  .gaming-attribute-section {
    padding: 1rem 1.5rem;
    margin-bottom: 1.5rem;
  }

  .gaming-attribute-icon {
    width: 2rem;
    height: 2rem;
  }

  .gaming-attribute-title {
    font-size: 1.1rem;
  }

  .gaming-attribute-count {
    padding: 0.375rem 0.75rem;
    font-size: 0.8rem;
  }

  .gaming-attribute-grid {
    grid-template-columns: repeat(auto-fill, minmax(50px, 1fr));
    gap: 0.5rem;
  }

  .gaming-empty-state {
    padding: 2rem 1.5rem;
  }

  .gaming-empty-state-icon {
    width: 3rem;
    height: 3rem;
    margin-bottom: 1rem;
  }

  .gaming-empty-state-text {
    font-size: 0.875rem;
  }

  .gaming-thumbnail {
    width: 64px !important;
    min-width: 64px !important;
    min-height: 48px !important;
    /* ENHANCED: Remove spacing for tight border fit */
    box-sizing: border-box !important;
    padding: 0 !important;
    margin: 0 !important;
    display: block !important;
  }
}

/* ===== TABLET RESPONSIVE DESIGN ===== */

@media (min-width: 641px) and (max-width: 1023px) {
  .gaming-attribute-grid {
    grid-template-columns: repeat(auto-fill, minmax(55px, 1fr));
    gap: 0.625rem;
  }
}

/* ===== DESKTOP RESPONSIVE DESIGN ===== */

@media (min-width: 1024px) {
  .gaming-attribute-grid {
    grid-template-columns: repeat(auto-fill, minmax(65px, 1fr));
    gap: 0.875rem;
  }

  .gaming-thumbnail img {
    /* ENHANCED: Fill container completely without spacing */
    -o-object-fit: cover !important;
       object-fit: cover !important;
    -o-object-position: center !important;
       object-position: center !important;
    width: 100% !important;
    height: 100% !important;
    max-width: 100% !important;
    max-height: 100% !important;
    /* ENHANCED: Absolute positioning for tight fit */
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    padding: 0 !important;
    margin: 0 !important;
  }

  .gaming-thumbnail-placeholder {
    /* ENHANCED: Fill container completely */
    width: 100% !important;
    height: 100% !important;
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    padding: 0 !important;
    margin: 0 !important;
  }

  .gaming-thumbnail-container {
    gap: 0.5rem !important;
    padding: 0.5rem 0.25rem !important;
    /* ENHANCED: Better mobile scrolling */
    overflow-x: auto !important;
    overflow-y: hidden !important;
    scroll-snap-type: x mandatory !important;
    /* ENHANCED: Align items to start for natural heights */
    align-items: flex-start !important;
    /* FIXED: Mobile container constraints */
    width: 100% !important;
    max-width: 100% !important;
    box-sizing: border-box !important;
  }

  .gaming-thumbnail {
    /* ENHANCED: Snap scrolling on mobile */
    scroll-snap-align: start !important;
  }

  .gaming-thumbnail-gallery {
    padding: 0.5rem;
  }

  .gaming-thumbnail-title {
    font-size: 0.8rem;
  }

  .gaming-thumbnail-count {
    font-size: 0.7rem;
    padding: 0.2rem 0.4rem;
  }
}

@media (min-width: 641px) and (max-width: 767px) {
  .gaming-thumbnail {
    width: 72px !important;
    min-width: 72px !important;
    min-height: 54px !important;
    box-sizing: border-box !important;
    padding: 0 !important;
    margin: 0 !important;
    display: block !important;
  }

  .gaming-thumbnail img {
    -o-object-fit: cover !important;
       object-fit: cover !important;
    -o-object-position: center !important;
       object-position: center !important;
    width: 100% !important;
    height: 100% !important;
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    padding: 0 !important;
    margin: 0 !important;
  }
}

@media (min-width: 768px) and (max-width: 1023px) {
  .gaming-thumbnail {
    width: 80px !important;
    min-width: 80px !important;
    min-height: 60px !important;
    box-sizing: border-box !important;
    padding: 0 !important;
    margin: 0 !important;
    display: block !important;
  }

  .gaming-thumbnail img {
    -o-object-fit: cover !important;
       object-fit: cover !important;
    -o-object-position: center !important;
       object-position: center !important;
    width: 100% !important;
    height: 100% !important;
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    padding: 0 !important;
    margin: 0 !important;
  }
}

@media (min-width: 1024px) and (max-width: 1279px) {
  .gaming-thumbnail {
    width: 88px !important;
    min-width: 88px !important;
    min-height: 66px !important;
    box-sizing: border-box !important;
    padding: 0 !important;
    margin: 0 !important;
    display: block !important;
  }

  .gaming-thumbnail img {
    -o-object-fit: cover !important;
       object-fit: cover !important;
    -o-object-position: center !important;
       object-position: center !important;
    width: 100% !important;
    height: 100% !important;
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    padding: 0 !important;
    margin: 0 !important;
  }

  .gaming-thumbnail-container {
    gap: 1rem;
  }
}

@media (min-width: 1280px) {
  .gaming-thumbnail {
    width: 96px !important;
    min-width: 96px !important;
    min-height: 72px !important;
    box-sizing: border-box !important;
    padding: 0 !important;
    margin: 0 !important;
    display: block !important;
  }

  .gaming-thumbnail img {
    -o-object-fit: cover !important;
       object-fit: cover !important;
    -o-object-position: center !important;
       object-position: center !important;
    width: 100% !important;
    height: 100% !important;
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    padding: 0 !important;
    margin: 0 !important;
  }

  .gaming-thumbnail-container {
    gap: 1.25rem;
  }
}

/* ===== ENHANCED RESPONSIVE GRID OVERRIDES FOR SMALLER ATTRIBUTE IMAGES ===== */

/* Mobile Skin Grid Override - Extra Small */

@media (max-width: 640px) {
  .gaming-attribute-grid[style*="minmax(50px"] {
    grid-template-columns: repeat(auto-fill, minmax(45px, 1fr)) !important;
    gap: 0.375rem !important;
  }
}

/* Tablet Skin Grid Override - Small */

@media (min-width: 641px) and (max-width: 1023px) {
  .gaming-attribute-grid[style*="minmax(50px"] {
    grid-template-columns: repeat(auto-fill, minmax(48px, 1fr)) !important;
    gap: 0.5rem !important;
  }
}

/* Desktop Skin Grid Override - Medium */

@media (min-width: 1024px) {
  .gaming-attribute-grid[style*="minmax(50px"] {
    grid-template-columns: repeat(auto-fill, minmax(55px, 1fr)) !important;
    gap: 0.625rem !important;
  }
}

/* ===== ENHANCED VISUAL DENSITY IMPROVEMENTS ===== */

/* Compact spacing for all attribute sections on mobile */

@media (max-width: 640px) {
  .gaming-attribute-section {
    padding: 1rem 1.25rem !important;
    margin-bottom: 1.25rem !important;
  }

  .gaming-attribute-header {
    margin-bottom: 1rem !important;
    padding-bottom: 0.75rem !important;
  }
}

/* Medium spacing for tablets */

@media (min-width: 641px) and (max-width: 1023px) {
  .gaming-attribute-section {
    padding: 1.25rem 1.75rem !important;
    margin-bottom: 1.5rem !important;
  }
}

/* ===== CORE STYLES ===== */

.gaming-ripple {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transform: scale(0);
  animation: gaming-ripple-animation 0.6s linear;
  pointer-events: none;
}

@keyframes gaming-ripple-animation {
  to {
    transform: scale(4);
    opacity: 0;
  }
}

/* ===== PAGINATION SYSTEM ===== */

.gaming-pagination-bullet {
  width: 16px;
  height: 16px;
  background: rgba(80, 129, 255, 0.4);
  border: 3px solid rgba(80, 129, 255, 0.6);
  border-radius: 50%;
  margin: 0 10px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 
    0 4px 15px rgba(0, 0, 0, 0.2),
    0 0 10px rgba(80, 129, 255, 0.3);
  display: inline-block;
  visibility: visible;
  z-index: 10;
}

.gaming-pagination-bullet:hover {
  background: rgba(80, 129, 255, 0.7);
  border-color: rgba(80, 129, 255, 0.8);
  transform: scale(1.2);
  box-shadow: 
    0 6px 20px rgba(0, 0, 0, 0.3),
    0 0 15px rgba(80, 129, 255, 0.5);
}

.gaming-pagination-bullet.swiper-pagination-bullet-active {
  background: linear-gradient(135deg, #5081FF 0%, #3463DB 100%);
  border-color: #5081FF;
  box-shadow: 
    0 8px 25px rgba(0, 0, 0, 0.3),
    0 0 20px rgba(80, 129, 255, 0.7);
  transform: scale(1.3);
}

/* ===== TOOLTIP THEME ===== */

.tippy-box[data-theme~='gaming'] {
  background: linear-gradient(135deg, 
    rgba(30, 41, 59, 0.95) 0%, 
    rgba(15, 23, 42, 0.98) 100%);
  border: 1px solid rgba(80, 129, 255, 0.3);
  border-radius: 0.5rem;
  -webkit-backdrop-filter: blur(8px);
          backdrop-filter: blur(8px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
}

.tippy-box[data-theme~='gaming'] .tippy-content {
  color: #ffffff;
  font-weight: 500;
  padding: 0.5rem 0.75rem;
}

/* ===== ADVANCED ANIMATIONS ===== */

@keyframes glowPulse {
  0%, 100% {
    opacity: 0.3;
    transform: scale(1);
  }

  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

#modalGlow {
  animation: glowPulse 3s ease-in-out infinite;
}

@keyframes fadeInImage {
  0% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}

/* ===== DEBUGGING HELPERS ===== */

.debug-mode .swiper-slide {
  border: 2px solid red;
}

.debug-mode .swiper-slide img {
  border: 2px solid green;
}

/* Critical CSS for gaming theme - loaded inline for performance */

.gaming-account-container {
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #0f172a 100%);
  min-height: 100vh;
  position: relative;
  overflow-x: hidden;
}

.gaming-account-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 20% 50%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(147, 51, 234, 0.1) 0%, transparent 50%);
  pointer-events: none;
  z-index: 1;
}

.gaming-account-card {
  background: rgba(30, 41, 59, 0.95);
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: 1rem;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.gaming-account-card:hover {
  border-color: rgba(59, 130, 246, 0.4);
  box-shadow: 0 25px 50px -12px rgba(59, 130, 246, 0.2);
}

.gaming-title {
  background: linear-gradient(135deg, #3b82f6, #8b5cf6, #3b82f6);
  background-size: 200% 200%;
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  animation: gradientShift 3s ease-in-out infinite;
}

.gaming-accent-text {
  color: #3b82f6;
  text-shadow: 0 0 10px rgba(59, 130, 246, 0.3);
}

.gaming-info-card {
  background: rgba(55, 65, 81, 0.6);
  border: 1px solid rgba(75, 85, 99, 0.3);
  border-radius: 0.5rem;
  padding: 0.75rem;
  transition: all 0.2s ease;
}

.gaming-info-card:hover {
  background: rgba(55, 65, 81, 0.8);
  border-color: rgba(59, 130, 246, 0.3);
}

.gaming-btn-primary {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  border: none;
  border-radius: 0.75rem;
  color: white;
  font-weight: 600;
  padding: 0.875rem 1.5rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  box-shadow: 0 10px 25px -5px rgba(59, 130, 246, 0.3);
}

.gaming-btn-primary:hover {
  background: linear-gradient(135deg, #1d4ed8, #1e40af);
  transform: translateY(-2px);
  box-shadow: 0 20px 40px -10px rgba(59, 130, 246, 0.4);
}

.gaming-btn-primary:active {
  transform: translateY(0);
}

@keyframes gradientShift {
  0%, 100% {
    background-position: 0% 50%;
  }

  50% {
    background-position: 100% 50%;
  }
}

/* Gaming gallery optimizations */

.gaming-main-gallery {
  background: rgba(17, 24, 39, 0.8);
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: 1rem;
  overflow: hidden;
}

.gaming-grid-header {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(147, 51, 234, 0.1));
  border-bottom: 1px solid rgba(59, 130, 246, 0.2);
  padding: 1rem 1.5rem;
}

.gaming-featured-display {
  position: relative;
  /* min-height: 400px; */
  background: rgba(0, 0, 0, 0.3);
}

/* Swiper optimizations */

.mySwiper {
  width: 100%;
  height: 100%;
  border-radius: 0.5rem;
}

.swiper-slide {
  display: flex;
  align-items: center;
  justify-content: center;
  background: transparent;
}

.swiper-slide img {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  border-radius: 0.75rem;
  transition: transform 0.3s ease, filter 0.3s ease;
}

.swiper-slide:hover img {
  transform: scale(1.02);
  filter: brightness(1.1);
}

/* Gaming attribute cards */

.gaming-attribute-card {
  position: relative;
  width: 50px;
  height: 50px;
  border-radius: 0.5rem;
  overflow: hidden;
  border: 2px solid rgba(59, 130, 246, 0.3);
  transition: all 0.3s ease;
  cursor: pointer;
}

.gaming-attribute-card:hover {
  border-color: rgba(59, 130, 246, 0.6);
  transform: scale(1.05);
  box-shadow: 0 10px 25px -5px rgba(59, 130, 246, 0.3);
}

.gaming-attribute-image {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  transition: transform 0.3s ease;
}

.gaming-attribute-card:hover .gaming-attribute-image {
  transform: scale(1.1);
}

/* Status indicators */

.gaming-status-sold {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
  padding: 0.75rem 1rem;
  border-radius: 0.75rem;
  text-align: center;
  font-weight: 600;
  box-shadow: 0 10px 25px -5px rgba(239, 68, 68, 0.3);
}

/* Responsive optimizations */

@media (max-width: 768px) {
  .gaming-account-container {
    padding: 1rem 0.5rem;
  }

  .gaming-title {
    font-size: 2rem;
  }

  .gaming-featured-display {
    /* min-height: 300px; */
  }
}

/* Performance optimizations */

.gaming-account-card,
  .gaming-main-gallery,
  .gaming-btn-primary {
  will-change: transform;
}

/* Image optimization */

.gaming-attribute-image,
  .swiper-slide img {
  image-rendering: -webkit-optimize-contrast;
  image-rendering: crisp-edges;
}

/* Lazy loading optimization */

img[loading="lazy"] {
  opacity: 0;
  transition: opacity 0.3s ease;
}

img[loading="lazy"].loaded,
  img[loading="eager"] {
  opacity: 1;
}

/* Reduce layout shifts */

.swiper-container {
  contain: layout style paint;
}

/* GPU acceleration for smooth animations */

.gaming-title,
  .gaming-btn-primary,
  .gaming-attribute-card {
  transform: translateZ(0);
}

@keyframes zoom {
  0% {
    transform: scale(.5);
    opacity: 0;
  }

  50% {
    opacity: 1;
  }

  to {
    opacity: 0;
    transform: scale(1);
  }
}

@keyframes lucidgentelegram {
  0%, to {
    transform: rotate(-25deg);
  }

  50% {
    transform: rotate(25deg);
  }
}

@media (max-width: 992px) {
  .mb-footer {
    padding-bottom: 100px;
  }
}

.th-social a{
  display:inline-block;
  width:46px;
  height:46px;
  line-height:46px;
  background-color:#0f1c23;
  color:#1778f2;
  font-size:20px;
  text-align:center;
  margin-right:5px;
  border-radius:0;
  position:relative
}

.hero-carousel {
  width: 100%;
  height: 100%;
}

.hero-carousel .swiper-wrapper {
  width: 100%;
  height: 100%;
}

.hero-carousel .swiper-slide {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: stretch;
}

/* Fix for mobile image display */

.hero-carousel .swiper-slide a {
  width: 100%;
  height: 100%;
  display: block;
  position: relative;
}

.hero-carousel .swiper-slide img {
  width: 100% !important;
  height: 100% !important;
  -o-object-fit: cover !important;
     object-fit: cover !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
}

/* Ensure proper aspect ratios on all devices */

@media (max-width: 767px) {
  .hero-carousel .swiper-slide {
    aspect-ratio: 375/156;
  }
}

@media (min-width: 768px) and (max-width: 1023px) {
  .hero-carousel .swiper-slide {
    aspect-ratio: 716/203;
  }
}

@media (min-width: 1024px) {
  .hero-carousel .swiper-slide {
    aspect-ratio: 986/280;
  }
}

/* Custom Pagination Dots Styling */

.hero-carousel-pagination .pagination-dot {
  position: relative;
  height: 4px;
  cursor: pointer;
  overflow: hidden;
  border-radius: 10px;
  background: rgba(255, 255, 255, 0.48);
  transition: all 0.3s ease;
  width: 18px;
}

.hero-carousel-pagination .pagination-dot.active {
  width: 36px;
}

.hero-carousel-pagination .pagination-dot::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 0;
  height: 100%;
  background: #fff;
  transition: width 4.5s linear;
}

.hero-carousel-pagination .pagination-dot.active::before {
  width: 100%;
}

/* Reset progress animation */

.hero-carousel-pagination .pagination-dot:not(.active)::before {
  width: 0;
  transition: none;
}

.header-element {
  display: flex;
  align-items: stretch;
}

/* Responsive adjustments */

@media (max-width: 768px) {
  .hero-carousel-prev,
    .hero-carousel-next {
    display: none;
  }
}

.mobile-nav-container {
  position: fixed;
  width: 100%;
  height: 64px;
  bottom: 0;
  left: 0;
  z-index: 999998;
}

.nav-section-left {
  float: left;
  width: calc(50% - 40px);
  height: 64px;
  background: #13112E;
  border-top: 1px solid #5081FF33;
  border-top-right-radius: 20px;
  box-shadow: 0 -4px 8px 0 rgba(80, 129, 255, 0.15);
  display: flex;
}

.nav-section-right {
  float: right;
  width: calc(50% - 40px);
  height: 64px;
  background: #13112E;
  border-top: 1px solid #5081FF33;
  border-top-left-radius: 20px;
  box-shadow: 0 -4px 8px 0 rgba(80, 129, 255, 0.15);
  display: flex;
}

.nav-item {
  width: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding-top: 8px;
  color: #9F9BAB;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.3s ease;
}

.nav-item:hover {
  color: #FFFFFFCC;
  transform: translateY(-1px);
}

.nav-icon {
  font-size: 26px;
  height: 26px;
  display: block;
  margin: 0 auto 6px;
  width: 26px;
  transition: transform 0.3s ease;
}

.nav-item:hover .nav-icon {
  transform: scale(1.1);
}

.nav-label {
  display: block;
  height: 16px;
  line-height: 16px;
  margin-top: 2px;
  font-size: 12px;
  font-weight: 500;
  letter-spacing: 0.3px;
}

.floating-home-button {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 80px;
  background: transparent;
  border-radius: 50%;
  top: -26px;
  box-sizing: border-box;
  padding: 5px;
  z-index: 999999;
  text-decoration: none;
  transition: transform 0.3s ease;
}

.floating-home-button:hover {
  transform: translateX(-50%) scale(1.05);
}

.floating-home-button::before {
  content: "";
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  background: #13112E;
  border-radius: 50%;
  box-shadow: 0 33px 0 10px #13112E;
  z-index: -1;
}

.home-button-inner {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #4B7DFF 0%, #5081FF 100%);
  border-radius: 50%;
  box-sizing: border-box;
  text-align: center;
  cursor: pointer;
  color: #fff;
  font-size: 24px;
  box-shadow:
        0 8px 16px rgba(75, 125, 255, 0.3),
        inset 0 -4px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.floating-home-button:hover .home-button-inner {
  background: linear-gradient(135deg, #5081FF 0%, #4B7DFF 100%);
  box-shadow:
        0 12px 24px rgba(75, 125, 255, 0.4),
        inset 0 -4px 8px rgba(0, 0, 0, 0.15);
}

.floating-home-button::after {
  content: "";
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  display: block;
  border-radius: 50%;
  box-shadow: inset 0 -10px 5px 0 rgba(5, 26, 40, 0.08);
  z-index: 1;
  pointer-events: none;
}

/* Clear floats */

.mobile-nav-container::after {
  content: "";
  display: table;
  clear: both;
}

/* Responsive adjustments */

@media (max-width: 480px) {
  .nav-section-left,
    .nav-section-right {
    width: calc(50% - 35px);
  }

  .floating-home-button {
    width: 70px;
    height: 70px;
    top: -23px;
  }

  .nav-icon {
    font-size: 24px;
    height: 24px;
    width: 24px;
    margin: 0 auto 5px;
  }

  .nav-label {
    font-size: 11px;
    height: 15px;
    line-height: 15px;
  }

  .nav-item {
    padding-top: 6px;
  }
}

/* Enhanced Gaming Account Card Styles - Matching game.php */

.clean-account-card {
  background: linear-gradient(145deg, #272450 0%, #1e293b 50%, #272450 100%);
  border: 2px solid transparent;
  border-radius: 0.75rem;
  padding: 1rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  display: flex;
  flex-direction: column;
  min-height: 360px;
  position: relative;
  overflow: hidden;
  transform-origin: center;
  will-change: transform, box-shadow, border-color;
}

.clean-account-card:hover {
  transform: scale(1.05) translateY(-8px);
  border-color: #5081FF !important;
  box-shadow:
      0 20px 40px rgba(80, 129, 255, 0.4),
      0 0 30px rgba(80, 129, 255, 0.3),
      0 0 60px rgba(75, 125, 255, 0.2);
  animation: gaming-card-glow 2s infinite;
}

.clean-account-card--selected {
  border-color: #5081FF;
  box-shadow: 0 0 0 1px rgba(80, 129, 255, 0.3);
}

/* Compact Card Header */

.clean-card-header {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 0.75rem;
}

.status-badge {
  display: flex;
  align-items: center;
  gap: 0.375rem;
  background: rgba(16, 185, 129, 0.1);
  border: 1px solid rgba(16, 185, 129, 0.3);
  border-radius: 0.375rem;
  padding: 0.125rem 0.5rem;
  font-size: 0.6875rem;
  font-weight: 500;
  color: #10b981;
}

.status-indicator {
  width: 4px;
  height: 4px;
  background: #10b981;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

/* Optimized Image Section for 850x500 aspect ratio */

.clean-image-section {
  position: relative;
  margin-bottom: 1rem;
}

.image-container {
  position: relative;
  width: 100%;
  height: 0;
  padding-bottom: 58.82%;
  /* 500/850 = 0.5882 for 850x500 aspect ratio */
  border-radius: 0.5rem;
  overflow: hidden;
  background: rgba(30, 41, 59, 0.5);
}

.account-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  -o-object-position: center;
     object-position: center;
  transition: transform 0.3s ease;
}

.clean-account-card:hover .account-image {
  transform: scale(1.03);
}

.image-overlay {
  position: absolute;
  inset: 0;
  background: rgba(0, 0, 0, 0.4);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: opacity 0.3s ease;
  color: white;
  font-size: 1.25rem;
}

.image-skeleton {
  position: absolute;
  inset: 0;
  background: linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

.account-code-badge {
  position: absolute;
  bottom: -6px;
  left: 50%;
  transform: translateX(-50%);
  background: linear-gradient(135deg, #5081FF, #3463DB);
  color: white;
  padding: 0.125rem 0.75rem;
  border-radius: 0.375rem;
  font-size: 0.6875rem;
  font-weight: 600;
  box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.1);
  z-index: 10;
}

/* Enhanced Mobile-First Account Title Styling */

.clean-account-title {
  margin-bottom: 0.75rem;
  text-align: center;
  padding: 0 0.5rem;
  min-height: 2.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.clean-account-title h3 {
  font-size: 1rem;
  font-weight: 600;
  color: #ffffff;
  margin: 0;
  font-family: 'Signika', sans-serif;
  line-height: 1.3;
  text-align: center;
}

/* Improved Account Title Truncation - Mobile First */

.account-title-truncated {
  display: block;
  width: 100%;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  cursor: help;
  transition: all 0.3s ease;
  padding: 0.25rem 0;
  border-radius: 0.375rem;
  background: rgba(255, 255, 255, 0.02);
  -webkit-backdrop-filter: blur(4px);
          backdrop-filter: blur(4px);
}

.account-title-truncated:hover {
  color: #5081FF;
  transform: scale(1.02);
  background: rgba(80, 129, 255, 0.1);
  box-shadow: 0 0 10px rgba(80, 129, 255, 0.2);
}

/* Simple Stats Grid */

.clean-stats-grid {
  margin-bottom: 1rem;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.stat-item:last-child {
  border-bottom: none;
}

.stat-label {
  font-size: 0.875rem;
  color: #9ca3af;
  font-weight: 400;
  line-height: 1.5;
}

.stat-value {
  font-size: 0.875rem;
  font-weight: 500;
  color: #ffffff;
  text-align: right;
  line-height: 1.5;
}

/* Compact Feature Sections */

.clean-feature-section {
  margin-bottom: 0.75rem;
}

.feature-header {
  margin-bottom: 0.5rem;
}

.feature-header h4 {
  font-size: 0.8125rem;
  font-weight: 600;
  color: #5081FF;
  margin: 0 0 0.375rem 0;
  text-align: center;
  line-height: 1.2;
}

.feature-dropdown {
  position: relative;
}

.feature-toggle-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.375rem;
  width: 100%;
  padding: 0.375rem 0.5rem;
  background: rgba(80, 129, 255, 0.1);
  border: 1px solid rgba(80, 129, 255, 0.3);
  border-radius: 0.375rem;
  color: #ffffff;
  font-size: 0.6875rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.feature-toggle-btn:hover {
  background: rgba(80, 129, 255, 0.2);
  border-color: rgba(80, 129, 255, 0.5);
}

.feature-dropdown-content {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  z-index: 50;
  background: #1e293b;
  border: 1px solid rgba(80, 129, 255, 0.3);
  border-radius: 0.5rem;
  margin-top: 0.25rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

.dropdown-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.count-label {
  font-size: 0.75rem;
  color: #9ca3af;
}

.close-btn {
  background: none;
  border: none;
  color: #9ca3af;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 0.25rem;
  transition: color 0.3s ease;
}

.close-btn:hover {
  color: #ffffff;
}

.feature-grid {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 0.75rem;
  padding: 1rem;
  max-height: 160px;
  overflow-y: auto;
}

.feature-item {
  width: 40px;
  height: 40px;
  border-radius: 0.5rem;
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: transform 0.3s ease;
  cursor: pointer;
}

.feature-item:hover {
  transform: scale(1.1);
}

.feature-image {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}

/* Marquee */

.clean-marquee-container {
  margin-top: 0.75rem;
}

.marquee-wrapper {
  position: relative;
  width: 100%;
  overflow: hidden;
  border-radius: 0.5rem;
  background: rgba(255, 255, 255, 0.05);
  padding: 0.5rem;
}

.marquee-content {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.marquee-item {
  flex-shrink: 0;
  width: 32px;
  height: 32px;
  border-radius: 0.375rem;
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.marquee-image {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}

/* Compact Purchase Section */

.clean-purchase-section {
  margin-top: auto;
  padding-top: 0.75rem;
}

.clean-purchase-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  width: 100%;
  padding: 0.625rem 0.75rem;
  background: linear-gradient(135deg, #5081FF, #3463DB);
  border: none;
  border-radius: 0.5rem;
  color: white;
  font-weight: 600;
  text-decoration: none;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px -1px rgba(80, 129, 255, 0.3);
}

.clean-purchase-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 6px -1px rgba(80, 129, 255, 0.4);
  background: linear-gradient(135deg, #3463DB, #1e40af);
}

.purchase-icon {
  font-size: 0.875rem;
}

.purchase-price {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.125rem;
}

.price-with-discount {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.0625rem;
}

.original-price {
  font-size: 0.6875rem;
  text-decoration: line-through;
  color: rgba(255, 255, 255, 0.6);
  line-height: 1;
}

.discounted-price,
  .regular-price {
  font-size: 0.8125rem;
  font-weight: 700;
  color: #ffffff;
  line-height: 1.2;
}

/* Animations */

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }

  50% {
    opacity: 0.5;
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }

  100% {
    background-position: 200% 0;
  }
}

@keyframes marquee {
  from {
    transform: translateX(0);
  }

  to {
    transform: translateX(calc(-1 * var(--move-distance)));
  }
}

.marquee-track {
  display: flex;
  white-space: nowrap;
  animation: marquee linear infinite;
}

.marquee-track:hover {
  animation-play-state: paused;
}

/* Responsive Design */

@media (max-width: 640px) {
  .clean-account-card {
    padding: 0.75rem;
    min-height: 320px;
  }

  .clean-card-header {
    margin-bottom: 0.5rem;
  }

  .clean-image-section {
    margin-bottom: 0.75rem;
  }

  .clean-account-title {
    margin-bottom: 0.5rem;
  }

  .clean-account-title h3 {
    font-size: 0.9375rem;
  }

  /* Mobile: Improved readability with balanced truncation */

  .clean-account-title {
    margin-bottom: 0.625rem;
    padding: 0 0.25rem;
    min-height: 3rem;
  }

  .clean-account-title h3 {
    font-size: 0.95rem;
    line-height: 1.4;
    font-weight: 600;
  }

  .account-title-truncated {
    max-width: calc(100% - 1rem);
    margin: 0 auto;
    padding: 0.375rem 0.5rem;
    font-size: 0.95rem;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(80, 129, 255, 0.2);
  }

  .account-title-truncated:hover {
    background: rgba(80, 129, 255, 0.15);
    border-color: rgba(80, 129, 255, 0.4);
    transform: scale(1.01);
  }

  .feature-header h4 {
    font-size: 0.75rem;
  }

  .feature-toggle-btn {
    padding: 0.25rem 0.375rem;
    font-size: 0.625rem;
  }

  .feature-grid {
    gap: 0.375rem;
    padding: 0.5rem;
    max-height: 120px;
  }

  .feature-item {
    width: 32px;
    height: 32px;
  }

  .marquee-item {
    width: 24px;
    height: 24px;
  }

  .clean-purchase-btn {
    padding: 0.5rem 0.625rem;
  }

  .purchase-icon {
    font-size: 0.75rem;
  }

  .original-price {
    font-size: 0.625rem;
  }

  .discounted-price,
    .regular-price {
    font-size: 0.75rem;
  }
}

@media (max-width: 480px) {
  .clean-account-card {
    padding: 0.625rem;
    min-height: 300px;
  }

  .clean-stats-grid {
    padding: 0.375rem;
  }

  .feature-grid {
    padding: 0.375rem;
  }

  /* Extra Small: Maintain readability while being compact */

  .clean-account-title {
    min-height: 2.75rem;
    padding: 0 0.125rem;
  }

  .clean-account-title h3 {
    font-size: 0.9rem;
    line-height: 1.35;
  }

  .account-title-truncated {
    max-width: calc(100% - 0.5rem);
    font-size: 0.9rem;
    padding: 0.3rem 0.4rem;
  }
}

/* Tablet responsive design */

@media (min-width: 641px) and (max-width: 1024px) {
  .account-title-truncated {
    max-width: 220px;
  }
}

/* Desktop responsive design */

@media (min-width: 1025px) {
  .account-title-truncated {
    max-width: 280px;
  }
}

/* Large desktop responsive design */

@media (min-width: 1440px) {
  .account-title-truncated {
    max-width: 320px;
  }
}

/* Gaming Card Animations and Effects */

@keyframes gaming-card-glow {
  0%, 100% {
    box-shadow:
        0 20px 40px rgba(80, 129, 255, 0.3),
        0 0 20px rgba(80, 129, 255, 0.2),
        0 0 40px rgba(75, 125, 255, 0.1);
  }

  50% {
    box-shadow:
        0 25px 50px rgba(80, 129, 255, 0.5),
        0 0 30px rgba(80, 129, 255, 0.4),
        0 0 60px rgba(75, 125, 255, 0.3);
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }

  100% {
    transform: translateX(100%);
  }
}

.animate-shimmer {
  animation: shimmer 2s infinite;
}

/* Responsive Gaming Card Effects */

@media (max-width: 768px) {
  .clean-account-card:hover {
    transform: scale(1.02) translateY(-4px);
  }
}

@media (max-width: 640px) {
  .clean-account-card:hover {
    transform: scale(1.01) translateY(-2px);
  }
}

/* Touch Device Optimizations */

@media (hover: none) and (pointer: coarse) {
  .clean-account-card:active {
    transform: scale(1.02) translateY(-2px);
    transition: all 0.15s ease;
  }
}

/* High Performance Mode for Lower-End Devices */

@media (prefers-reduced-motion: reduce) {
  .clean-account-card {
    transition: transform 0.2s ease, border-color 0.2s ease;
  }

  .clean-account-card:hover {
    transform: scale(1.02) translateY(-2px);
    animation: none;
  }
}

/* ===== GAMING PAGINATION STYLES ===== */

.gaming-pagination-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  /* padding: 1.5rem;
    background: linear-gradient(145deg, rgba(19, 17, 46, 0.8) 0%, rgba(30, 41, 59, 0.6) 50%, rgba(19, 17, 46, 0.8) 100%);
    border: 1px solid rgba(80, 129, 255, 0.2);
    border-radius: 1rem;
    backdrop-filter: blur(10px);
    box-shadow:
      0 8px 32px rgba(0, 0, 0, 0.3),
      0 0 20px rgba(80, 129, 255, 0.1); */
}

.gaming-pagination-list {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  list-style: none;
  margin: 0;
  padding: 0;
  min-height: 2.5rem;
}

.gaming-pagination-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  min-width: 2.5rem;
  height: 2.5rem;
  padding: 0.5rem 0.75rem;
  border: none;
  border-radius: 0.5rem;
  font-family: 'Montserrat', sans-serif;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  position: relative;
  overflow: hidden;
  box-sizing: border-box;
  vertical-align: middle;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
  -webkit-tap-highlight-color: transparent;
}

.gaming-pagination-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.gaming-pagination-btn:hover::before {
  left: 100%;
}

/* Number Buttons */

.gaming-pagination-btn--number {
  background: linear-gradient(145deg, rgba(39, 36, 80, 0.8) 0%, rgba(30, 41, 59, 0.6) 100%);
  color: #FFFFFF99;
  border: 1px solid rgba(80, 129, 255, 0.3);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.gaming-pagination-btn--number:hover {
  background: linear-gradient(145deg, rgba(80, 129, 255, 0.2) 0%, rgba(75, 125, 255, 0.3) 100%);
  color: #FFFFFF;
  border-color: rgba(80, 129, 255, 0.6);
  box-shadow:
      0 8px 16px rgba(80, 129, 255, 0.3),
      0 0 20px rgba(80, 129, 255, 0.2);
}

/* Active Page Button */

.gaming-pagination-btn--active {
  background: linear-gradient(135deg, #4B7DFF 0%, #5081FF 50%, #3463DB 100%);
  color: #FFFFFF;
  border: 1px solid rgba(80, 129, 255, 0.8);
  box-shadow:
      0 4px 12px rgba(80, 129, 255, 0.4),
      0 0 20px rgba(80, 129, 255, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.2);
  animation: gaming-pagination-pulse 2s infinite;
}

.gaming-pagination-btn--active:hover {
  transform: translateY(-1px) scale(1.02);
  box-shadow:
      0 6px 16px rgba(80, 129, 255, 0.5),
      0 0 30px rgba(80, 129, 255, 0.4),
      inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

/* Navigation Buttons (Previous/Next) */

.gaming-pagination-btn--nav {
  background: linear-gradient(145deg, rgba(75, 125, 255, 0.1) 0%, rgba(80, 129, 255, 0.2) 100%);
  color: #5081FF;
  border: 1px solid rgba(80, 129, 255, 0.4);
  min-width: 3rem;
}

.gaming-pagination-btn--nav:hover {
  background: linear-gradient(145deg, rgba(75, 125, 255, 0.3) 0%, rgba(80, 129, 255, 0.4) 100%);
  color: #FFFFFF;
  border-color: rgba(80, 129, 255, 0.8);
  box-shadow:
      0 8px 16px rgba(80, 129, 255, 0.3),
      0 0 20px rgba(80, 129, 255, 0.2);
}

/* Disabled Buttons */

.gaming-pagination-btn--disabled {
  background: linear-gradient(145deg, rgba(39, 36, 80, 0.3) 0%, rgba(30, 41, 59, 0.2) 100%);
  color: #FFFFFF33;
  border: 1px solid rgba(80, 129, 255, 0.1);
  cursor: not-allowed;
  opacity: 0.5;
}

.gaming-pagination-btn--disabled:hover {
  transform: none;
  box-shadow: none;
  background: linear-gradient(145deg, rgba(39, 36, 80, 0.3) 0%, rgba(30, 41, 59, 0.2) 100%);
}

.gaming-pagination-btn--disabled::before {
  display: none;
}

/* Ellipsis */

.gaming-pagination-ellipsis {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 2.5rem;
  height: 2.5rem;
  color: #FFFFFF66;
  font-size: 1rem;
}

/* Icons */

.gaming-pagination-icon {
  flex-shrink: 0;
  transition: transform 0.3s ease;
}

.gaming-pagination-btn:hover .gaming-pagination-icon {
  transform: scale(1.1);
}

.gaming-pagination-text {
  font-size: 0.875rem;
  font-weight: 600;
}

/* Pagination Info */

.gaming-pagination-info {
  text-align: center;
  margin-top: 0.5rem;
}

.gaming-pagination-info-text {
  font-size: 0.875rem;
  color: #FFFFFF99;
  font-family: 'Montserrat', sans-serif;
}

.gaming-pagination-info-current,
  .gaming-pagination-info-total {
  font-weight: 700;
  color: #5081FF;
  text-shadow: 0 0 8px rgba(80, 129, 255, 0.5);
}

/* Gaming Pagination Animations */

@keyframes gaming-pagination-pulse {
  0%, 100% {
    box-shadow:
        0 4px 12px rgba(80, 129, 255, 0.4),
        0 0 20px rgba(80, 129, 255, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
  }

  50% {
    box-shadow:
        0 6px 16px rgba(80, 129, 255, 0.6),
        0 0 30px rgba(80, 129, 255, 0.5),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
  }
}

@keyframes gaming-pagination-glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(80, 129, 255, 0.2);
  }

  50% {
    box-shadow: 0 0 30px rgba(80, 129, 255, 0.4);
  }
}

/* Mobile Layout Utilities */

.gaming-pagination-mobile-scroll {
  overflow-x: auto;
  overflow-y: hidden;
  -webkit-overflow-scrolling: touch;
  scroll-behavior: smooth;
}

.gaming-pagination-mobile-scroll::-webkit-scrollbar {
  height: 2px;
}

.gaming-pagination-mobile-scroll::-webkit-scrollbar-track {
  background: rgba(80, 129, 255, 0.1);
  border-radius: 1px;
}

.gaming-pagination-mobile-scroll::-webkit-scrollbar-thumb {
  background: rgba(80, 129, 255, 0.3);
  border-radius: 1px;
}

.gaming-pagination-mobile-scroll::-webkit-scrollbar-thumb:hover {
  background: rgba(80, 129, 255, 0.5);
}

/* ===== MOBILE RESPONSIVE DESIGN FOR GAMING PAGINATION ===== */

/* Tablet (769px - 1024px) */

@media (min-width: 769px) and (max-width: 1024px) {
  .gaming-pagination-container {
    padding: 1.5rem 1.25rem;
    gap: 1.125rem;
  }

  .gaming-pagination-list {
    gap: 0.625rem;
  }

  .gaming-pagination-btn {
    min-width: 3rem;
    height: 3rem;
    padding: 0.625rem 0.875rem;
    font-size: 0.9375rem;
  }

  .gaming-pagination-btn--nav {
    min-width: 3.5rem;
    padding: 0.625rem 1rem;
  }

  .gaming-pagination-text {
    font-size: 0.9375rem;
  }

  .gaming-pagination-info-text {
    font-size: 0.9375rem;
  }
}

/* Medium Mobile (481px - 768px) */

@media (max-width: 768px) {
  .gaming-pagination-container {
    padding: 1.25rem 1rem;
    gap: 1rem;
    margin: 0 0.5rem;
    border-radius: 0.875rem;
  }

  .gaming-pagination-list {
    gap: 0.5rem;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
    min-height: 2.75rem;
  }

  .gaming-pagination-btn {
    min-width: 2.75rem;
    height: 2.75rem;
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
    border-radius: 0.75rem;
  }

  .gaming-pagination-btn--nav {
    min-width: 3.25rem;
    padding: 0.5rem 0.875rem;
  }

  .gaming-pagination-text {
    font-size: 0.8125rem;
    font-weight: 600;
  }

  .gaming-pagination-ellipsis {
    min-width: 2.75rem;
    height: 2.75rem;
    margin: 0 0.25rem;
  }

  .gaming-pagination-info {
    margin-top: 0.75rem;
  }

  .gaming-pagination-info-text {
    font-size: 0.9375rem;
  }

  /* Better visual balance on mobile */

  .gaming-pagination-container {
    /* box-shadow:
        0 4px 16px rgba(0, 0, 0, 0.2),
        0 0 12px rgba(80, 129, 255, 0.1); */
  }

  /* Ensure proper spacing on mobile */

  .gaming-pagination-list li {
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

/* Small Mobile (≤640px) */

@media (max-width: 640px) {
  .gaming-pagination-container {
    padding: 1rem 0.75rem;
    gap: 0.875rem;
    margin: 0 0.25rem;
  }

  .gaming-pagination-list {
    gap: 0.375rem;
    max-width: 100%;
    overflow-x: auto;
    padding: 0.25rem 0;
    scrollbar-width: none;
    -ms-overflow-style: none;
  }

  .gaming-pagination-list::-webkit-scrollbar {
    display: none;
  }

  .gaming-pagination-btn {
    min-width: 2.5rem;
    height: 2.5rem;
    padding: 0.375rem 0.625rem;
    font-size: 0.8125rem;
    flex-shrink: 0;
  }

  .gaming-pagination-btn--nav {
    min-width: 2.875rem;
    padding: 0.375rem 0.75rem;
  }

  .gaming-pagination-text {
    font-size: 0.75rem;
  }

  .gaming-pagination-ellipsis {
    min-width: 2.5rem;
    height: 2.5rem;
  }

  .gaming-pagination-icon {
    width: 18px;
    height: 18px;
  }

  /* Mobile touch optimizations */

  .gaming-pagination-btn:active {
    transform: translateY(0) scale(0.98);
    transition: all 0.1s ease;
  }
}

/* Extra Small Mobile (≤480px) */

@media (max-width: 480px) {
  .gaming-pagination-container {
    padding: 0.875rem 0.5rem;
    gap: 0.75rem;
    margin: 0;
    border-radius: 0.75rem;
  }

  .gaming-pagination-list {
    gap: 0.25rem;
    justify-content: flex-start;
    padding: 0.125rem 0;
  }

  .gaming-pagination-text {
    display: none !important;
  }

  .gaming-pagination-btn {
    min-width: 2.25rem;
    height: 2.25rem;
    padding: 0.25rem;
    font-size: 0.75rem;
    border-radius: 0.5rem;
    flex-shrink: 0;
  }

  .gaming-pagination-btn--nav {
    min-width: 2.5rem;
    padding: 0.25rem 0.375rem;
  }

  .gaming-pagination-ellipsis {
    min-width: 2.25rem;
    height: 2.25rem;
    font-size: 0.875rem;
  }

  .gaming-pagination-icon {
    width: 16px;
    height: 16px;
  }

  .gaming-pagination-info-text {
    font-size: 0.8125rem;
  }
}

/* Ultra Small Mobile (≤360px) */

@media (max-width: 360px) {
  .gaming-pagination-container {
    padding: 0.75rem 0.375rem;
    gap: 0.625rem;
  }

  .gaming-pagination-list {
    gap: 0.125rem;
    padding: 0;
  }

  .gaming-pagination-btn {
    min-width: 2rem;
    height: 2rem;
    padding: 0.125rem;
    font-size: 0.6875rem;
  }

  .gaming-pagination-btn--nav {
    min-width: 2.25rem;
    padding: 0.125rem 0.25rem;
  }

  .gaming-pagination-ellipsis {
    min-width: 2rem;
    height: 2rem;
    font-size: 0.75rem;
  }

  .gaming-pagination-icon {
    width: 14px;
    height: 14px;
  }

  .gaming-pagination-info-text {
    font-size: 0.75rem;
  }
}

/* Large Screen Enhancements (≥1024px) */

@media (min-width: 1024px) {
  .gaming-pagination-container {
    padding: 2rem;
    gap: 1.25rem;
    /* animation: gaming-pagination-glow 3s infinite; */
  }

  .gaming-pagination-list {
    gap: 0.75rem;
  }

  .gaming-pagination-btn {
    min-width: 3rem;
    height: 3rem;
    padding: 0.75rem 1rem;
    font-size: 1rem;
  }

  .gaming-pagination-btn--nav {
    min-width: 3.5rem;
  }

  .gaming-pagination-text {
    font-size: 1rem;
  }

  .gaming-pagination-info-text {
    font-size: 1rem;
  }

  .gaming-pagination-btn--number:hover {
  }

  .gaming-pagination-btn--nav:hover {
    background: linear-gradient(145deg, rgba(75, 125, 255, 0.4) 0%, rgba(80, 129, 255, 0.6) 100%);
    box-shadow:
        0 12px 24px rgba(80, 129, 255, 0.4),
        0 0 30px rgba(80, 129, 255, 0.3);
  }
}

/* Accessibility and Performance */

@media (prefers-reduced-motion: reduce) {
  .gaming-pagination-btn {
    transition: background-color 0.2s ease, color 0.2s ease;
  }

  .gaming-pagination-btn:hover {
    transform: none;
    animation: none;
  }

  .gaming-pagination-btn--active {
    animation: none;
  }

  .gaming-pagination-container {
    animation: none;
  }

  .gaming-pagination-btn::before {
    display: none;
  }
}

/* Focus States for Accessibility */

.gaming-pagination-btn:focus {
  outline: none;
  box-shadow:
      0 0 0 2px rgba(80, 129, 255, 0.5),
      0 4px 12px rgba(80, 129, 255, 0.3);
}

.gaming-pagination-btn:focus-visible {
  outline: 2px solid #5081FF;
  outline-offset: 2px;
}

/* Prevent layout shift on mobile */

.gaming-pagination-list {
  min-height: 2.5rem;
}

@media (max-width: 768px) {
  .gaming-pagination-list {
    min-height: 2.75rem;
  }
}

@media (max-width: 480px) {
  .gaming-pagination-list {
    min-height: 2.25rem;
  }
}

/* Force gaming background on all Select2 containers */

.select2-container .select2-selection--single,
  .select2-container .select2-selection--multiple,
  .select2-container--gaming .select2-selection--single,
  .select2-container--gaming .select2-selection--multiple,
  .select2-container--default .select2-selection--single,
  .select2-container--default .select2-selection--multiple {
  height: 48px !important;
  min-height: 48px !important;
  background-color: rgba(30, 41, 59, 0.5) !important;
  background: rgba(30, 41, 59, 0.5) !important;
  border: 1px solid rgba(80, 129, 255, 0.3) !important;
  border-radius: 0.5rem !important;
  padding: 0.75rem 1rem !important;
  color: #ffffff !important;
  font-family: 'Signika', ui-sans-serif, system-ui, sans-serif !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  box-sizing: border-box !important;
}

/* Additional override for any white background */

.select2-container .select2-selection {
  background-color: rgba(30, 41, 59, 0.5) !important;
  background: rgba(30, 41, 59, 0.5) !important;
}

.select2-container .select2-selection--multiple,
  .select2-container--gaming .select2-selection--multiple,
  .select2-container--default .select2-selection--multiple {
  min-height: 48px !important;
  max-height: 120px !important;
  /* Limit height for better UX */
  overflow-y: auto !important;
  /* Allow scrolling for many selections */
  padding: 0.375rem 0.75rem !important;
  background-color: rgba(30, 41, 59, 0.5) !important;
  background: rgba(30, 41, 59, 0.5) !important;
  display: flex !important;
  flex-wrap: wrap !important;
  align-items: flex-start !important;
  gap: 0.25rem !important;
}

/* Enhanced scrollbar for multiple selection container */

.select2-container .select2-selection--multiple::-webkit-scrollbar {
  width: 4px !important;
}

.select2-container .select2-selection--multiple::-webkit-scrollbar-track {
  background: rgba(30, 41, 59, 0.3) !important;
  border-radius: 2px !important;
}

.select2-container .select2-selection--multiple::-webkit-scrollbar-thumb {
  background: #5081FF !important;
  border-radius: 2px !important;
}

/* Text and content styling */

.select2-container .select2-selection__rendered,
  .select2-container--gaming .select2-selection__rendered,
  .select2-container--default .select2-selection__rendered {
  color: #ffffff !important;
  line-height: 1.5 !important;
  padding: 0 !important;
  margin: 0 !important;
  display: flex !important;
  align-items: center !important;
  height: 100% !important;
}

/* Force override any inherited backgrounds */

.select2-container .select2-selection__rendered,
  .select2-container .select2-selection__rendered * {
  background-color: transparent !important;
  background: transparent !important;
}

/* Placeholder styling */

.select2-container .select2-selection__placeholder,
  .select2-container--gaming .select2-selection__placeholder,
  .select2-container--default .select2-selection__placeholder {
  color: #9CA3AF !important;
}

/* Arrow styling */

.select2-container .select2-selection__arrow,
  .select2-container--gaming .select2-selection__arrow,
  .select2-container--default .select2-selection__arrow {
  height: 46px !important;
  right: 1rem !important;
  top: 1px !important;
}

.select2-container .select2-selection__arrow b,
  .select2-container--gaming .select2-selection__arrow b,
  .select2-container--default .select2-selection__arrow b {
  border-color: #9CA3AF transparent transparent transparent !important;
  border-style: solid !important;
  border-width: 5px 4px 0 4px !important;
  height: 0 !important;
  left: 50% !important;
  margin-left: -4px !important;
  margin-top: -2px !important;
  position: absolute !important;
  top: 50% !important;
  width: 0 !important;
}

/* Focus State - Enhanced */

.select2-container.select2-container--focus .select2-selection,
  .select2-container--gaming.select2-container--focus .select2-selection,
  .select2-container--default.select2-container--focus .select2-selection,
  .select2-container.select2-container--focus .select2-selection--single,
  .select2-container.select2-container--focus .select2-selection--multiple {
  border-color: #5081FF !important;
  box-shadow: 0 0 0 2px rgba(80, 129, 255, 0.2) !important;
  outline: none !important;
  background-color: rgba(30, 41, 59, 0.5) !important;
  background: rgba(30, 41, 59, 0.5) !important;
}

/* Hover State - Enhanced */

.select2-container .select2-selection:hover,
  .select2-container .select2-selection--single:hover,
  .select2-container .select2-selection--multiple:hover {
  border-color: #5081FF !important;
  background-color: rgba(30, 41, 59, 0.5) !important;
  background: rgba(30, 41, 59, 0.5) !important;
}

/* Enhanced Multiple Selection Tags */

.select2-container .select2-selection__choice,
  .select2-container--gaming .select2-selection__choice,
  .select2-container--default .select2-selection__choice {
  background: linear-gradient(135deg, #5081FF, #3463DB) !important;
  border: 1px solid #3463DB !important;
  border-radius: 0.5rem !important;
  color: #ffffff !important;
  font-size: 0.875rem !important;
  font-weight: 500 !important;
  padding: 0.375rem 0.75rem !important;
  margin: 0.125rem !important;
  display: inline-flex !important;
  align-items: center !important;
  gap: 0.375rem !important;
  transition: all 0.2s ease-in-out !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
  max-width: 200px !important;
  /* Prevent overly long tags */
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  white-space: nowrap !important;
}

.select2-container .select2-selection__choice:hover,
  .select2-container--gaming .select2-selection__choice:hover,
  .select2-container--default .select2-selection__choice:hover {
  background: linear-gradient(135deg, #4B7DFF, #2A52BE) !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 8px rgba(80, 129, 255, 0.3) !important;
}

/* Enhanced Remove Button Styling */

.select2-container .select2-selection__choice__remove,
  .select2-container--gaming .select2-selection__choice__remove,
  .select2-container--default .select2-selection__choice__remove {
  color: #ffffff !important;
  font-size: 1rem !important;
  font-weight: bold !important;
  margin-right: 0 !important;
  margin-left: 0.25rem !important;
  padding: 0.125rem !important;
  border-radius: 50% !important;
  width: 18px !important;
  height: 18px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  transition: all 0.2s ease-in-out !important;
  background: rgba(255, 255, 255, 0.1) !important;
}

.select2-container .select2-selection__choice__remove:hover,
  .select2-container--gaming .select2-selection__choice__remove:hover,
  .select2-container--default .select2-selection__choice__remove:hover {
  color: #ffffff !important;
  background: #EF4444 !important;
  transform: scale(1.1) !important;
}

/* Enhanced Dropdown Styling */

.gaming-select2-dropdown {
  background: linear-gradient(145deg, #1E293B 0%, #0F172A 100%) !important;
  border: 2px solid rgba(80, 129, 255, 0.4) !important;
  border-radius: 0.75rem !important;
  box-shadow:
      0 20px 25px -5px rgba(0, 0, 0, 0.1),
      0 10px 10px -5px rgba(0, 0, 0, 0.04),
      0 0 30px rgba(80, 129, 255, 0.4),
      inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
  margin-top: 0.5rem !important;
  z-index: 99999 !important;
  -webkit-backdrop-filter: blur(10px) !important;
          backdrop-filter: blur(10px) !important;
  animation: dropdown-appear 0.2s ease-out !important;
}

@keyframes dropdown-appear {
  from {
    opacity: 0;
    transform: translateY(-10px) scale(0.95);
  }

  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.gaming-select2-dropdown .select2-results__options {
  max-height: 250px !important;
  overflow-y: auto !important;
  scrollbar-width: thin !important;
  scrollbar-color: #5081FF #1E293B !important;
  padding: 0.25rem !important;
}

.gaming-select2-dropdown .select2-results__options::-webkit-scrollbar {
  width: 6px !important;
}

.gaming-select2-dropdown .select2-results__options::-webkit-scrollbar-track {
  background: #1E293B !important;
}

.gaming-select2-dropdown .select2-results__options::-webkit-scrollbar-thumb {
  background: #5081FF !important;
  border-radius: 3px !important;
}

.gaming-select2-dropdown .select2-results__option {
  color: #ffffff !important;
  padding: 0.875rem 1.25rem !important;
  font-family: 'Signika', ui-sans-serif, system-ui, sans-serif !important;
  font-size: 0.9rem !important;
  font-weight: 500 !important;
  border-radius: 0.5rem !important;
  margin: 0.125rem 0 !important;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1) !important;
  cursor: pointer !important;
  position: relative !important;
  overflow: hidden !important;
}

.gaming-select2-dropdown .select2-results__option::before {
  content: '' !important;
  position: absolute !important;
  left: 0 !important;
  top: 0 !important;
  height: 100% !important;
  width: 3px !important;
  background: transparent !important;
  transition: all 0.2s ease-in-out !important;
}

.gaming-select2-dropdown .select2-results__option--highlighted {
  background: linear-gradient(135deg, rgba(80, 129, 255, 0.3), rgba(52, 99, 219, 0.2)) !important;
  color: #ffffff !important;
  transform: translateX(4px) !important;
}

.gaming-select2-dropdown .select2-results__option--highlighted::before {
  background: #5081FF !important;
}

.gaming-select2-dropdown .select2-results__option[aria-selected="true"] {
  background: linear-gradient(135deg, #5081FF, #3463DB) !important;
  color: #ffffff !important;
  font-weight: 600 !important;
  box-shadow: 0 2px 4px rgba(80, 129, 255, 0.3) !important;
}

.gaming-select2-dropdown .select2-results__option[aria-selected="true"]::before {
  background: #ffffff !important;
}

.gaming-select2-dropdown .select2-results__option[aria-selected="true"]::after {
  content: '✓' !important;
  position: absolute !important;
  right: 1rem !important;
  top: 50% !important;
  transform: translateY(-50%) !important;
  color: #ffffff !important;
  font-weight: bold !important;
  font-size: 1rem !important;
}

/* Enhanced Search Input */

.gaming-select2-dropdown .select2-search {
  padding: 0.75rem !important;
  border-bottom: 1px solid rgba(80, 129, 255, 0.2) !important;
  margin-bottom: 0.5rem !important;
}

.gaming-select2-dropdown .select2-search__field {
  background: linear-gradient(145deg, rgba(30, 41, 59, 0.9), rgba(15, 23, 42, 0.9)) !important;
  border: 2px solid rgba(80, 129, 255, 0.3) !important;
  border-radius: 0.5rem !important;
  color: #ffffff !important;
  padding: 0.75rem 1rem !important;
  margin: 0 !important;
  width: 100% !important;
  font-family: 'Signika', ui-sans-serif, system-ui, sans-serif !important;
  font-size: 0.9rem !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

.gaming-select2-dropdown .select2-search__field::-moz-placeholder {
  color: #9CA3AF !important;
  font-style: italic !important;
}

.gaming-select2-dropdown .select2-search__field::placeholder {
  color: #9CA3AF !important;
  font-style: italic !important;
}

.gaming-select2-dropdown .select2-search__field:focus {
  border-color: #5081FF !important;
  outline: none !important;
  box-shadow:
      0 0 0 3px rgba(80, 129, 255, 0.2),
      inset 0 2px 4px rgba(0, 0, 0, 0.1),
      0 4px 6px rgba(80, 129, 255, 0.1) !important;
  background: linear-gradient(145deg, rgba(30, 41, 59, 1), rgba(15, 23, 42, 1)) !important;
  transform: translateY(-1px) !important;
}

/* Clear Button */

.select2-container--gaming .select2-selection__clear {
  color: #9CA3AF !important;
  font-size: 1.2em !important;
  font-weight: bold !important;
  margin-right: 0.5rem !important;
}

.select2-container--gaming .select2-selection__clear:hover {
  color: #EF4444 !important;
}

/* Enhanced Responsive Adjustments */

@media (max-width: 768px) {
  .select2-container .select2-selection--single,
    .select2-container .select2-selection--multiple,
    .select2-container--gaming .select2-selection--single,
    .select2-container--gaming .select2-selection--multiple,
    .select2-container--default .select2-selection--single,
    .select2-container--default .select2-selection--multiple {
    min-height: 44px !important;
    padding: 0.625rem 0.875rem !important;
    font-size: 0.875rem !important;
  }

  .select2-container .select2-selection--multiple,
    .select2-container--gaming .select2-selection--multiple,
    .select2-container--default .select2-selection--multiple {
    max-height: 100px !important;
    gap: 0.125rem !important;
  }

  .select2-container .select2-selection__choice,
    .select2-container--gaming .select2-selection__choice,
    .select2-container--default .select2-selection__choice {
    font-size: 0.8125rem !important;
    padding: 0.25rem 0.5rem !important;
    max-width: 150px !important;
  }

  .select2-container .select2-selection__choice__remove,
    .select2-container--gaming .select2-selection__choice__remove,
    .select2-container--default .select2-selection__choice__remove {
    width: 16px !important;
    height: 16px !important;
    font-size: 0.875rem !important;
  }

  .select2-container .select2-selection__arrow,
    .select2-container--gaming .select2-selection__arrow,
    .select2-container--default .select2-selection__arrow {
    height: 42px !important;
    right: 0.875rem !important;
  }

  .gaming-select2-dropdown {
    border-radius: 0.5rem !important;
    margin-top: 0.25rem !important;
  }

  .gaming-select2-dropdown .select2-results__options {
    max-height: 200px !important;
  }

  .gaming-select2-dropdown .select2-results__option {
    padding: 0.75rem 1rem !important;
    font-size: 0.875rem !important;
  }

  .gaming-select2-dropdown .select2-search__field {
    padding: 0.625rem 0.875rem !important;
    font-size: 0.875rem !important;
  }
}

/* Extra small devices */

@media (max-width: 480px) {
  .select2-container .select2-selection--multiple,
    .select2-container--gaming .select2-selection--multiple,
    .select2-container--default .select2-selection--multiple {
    max-height: 80px !important;
  }

  .select2-container .select2-selection__choice,
    .select2-container--gaming .select2-selection__choice,
    .select2-container--default .select2-selection__choice {
    max-width: 120px !important;
    font-size: 0.75rem !important;
  }

  .gaming-select2-dropdown .select2-results__options {
    max-height: 180px !important;
  }
}

/* Additional Gaming Animations */

@keyframes gaming-card-glow {
  0%, 100% {
    box-shadow: 0 20px 40px rgba(80, 129, 255, 0.4), 0 0 30px rgba(80, 129, 255, 0.3), 0 0 60px rgba(75, 125, 255, 0.2);
  }

  50% {
    box-shadow: 0 20px 40px rgba(80, 129, 255, 0.6), 0 0 30px rgba(80, 129, 255, 0.5), 0 0 60px rgba(75, 125, 255, 0.4);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }

  100% {
    background-position: 200% 0;
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }

  50% {
    opacity: 0.5;
  }
}

/* Select2 Loading State */

.select2-container--gaming.select2-container--loading .select2-selection::after {
  content: '' !important;
  position: absolute !important;
  right: 2.5rem !important;
  top: 50% !important;
  transform: translateY(-50%) !important;
  width: 16px !important;
  height: 16px !important;
  border: 2px solid #5081FF !important;
  border-top: 2px solid transparent !important;
  border-radius: 50% !important;
  animation: spin 1s linear infinite !important;
}

@keyframes spin {
  0% {
    transform: translateY(-50%) rotate(0deg);
  }

  100% {
    transform: translateY(-50%) rotate(360deg);
  }
}

/* Marquee Animation */

@keyframes marquee {
  from {
    transform: translateX(0);
  }

  to {
    transform: translateX(calc(-1 * var(--move-distance)));
  }
}

.marquee-track {
  display: flex;
  white-space: nowrap;
  animation: marquee linear infinite;
}

.marquee-track:hover {
  animation-play-state: paused;
}

/* Enhanced Focus Management */

.select2-container--gaming.select2-container--focus .select2-selection,
  .select2-container--gaming.select2-container--open .select2-selection {
  border-color: #5081FF !important;
  box-shadow: 0 0 0 3px rgba(80, 129, 255, 0.2) !important;
}

/* Improved Clear Button */

.select2-container .select2-selection__clear,
  .select2-container--gaming .select2-selection__clear,
  .select2-container--default .select2-selection__clear {
  color: #9CA3AF !important;
  font-size: 1.1em !important;
  font-weight: bold !important;
  margin-right: 0.5rem !important;
  padding: 0.25rem !important;
  border-radius: 50% !important;
  transition: all 0.2s ease-in-out !important;
  background: rgba(255, 255, 255, 0.1) !important;
  width: 20px !important;
  height: 20px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.select2-container .select2-selection__clear:hover,
  .select2-container--gaming .select2-selection__clear:hover,
  .select2-container--default .select2-selection__clear:hover {
  color: #ffffff !important;
  background: #EF4444 !important;
  transform: scale(1.1) !important;
}

/* ===== COMPACT GAMING PAGINATION STYLES ===== */

.gaming-pagination-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.75rem;
  /* padding: 1rem;
    background: linear-gradient(145deg, rgba(19, 17, 46, 0.8) 0%, rgba(30, 41, 59, 0.6) 50%, rgba(19, 17, 46, 0.8) 100%);
    border: 1px solid rgba(80, 129, 255, 0.2);
    border-radius: 0.75rem;
    backdrop-filter: blur(10px);
    box-shadow:
      0 4px 16px rgba(0, 0, 0, 0.2),
      0 0 12px rgba(80, 129, 255, 0.1); */
}

.gaming-pagination-list {
  display: flex;
  align-items: center;
  gap: 0.375rem;
  list-style: none;
  margin: 0;
  padding: 0;
  min-height: 2rem;
}

.gaming-pagination-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.25rem;
  min-width: 1.75rem;
  height: 1.75rem;
  padding: 0.25rem 0.375rem;
  border: none;
  border-radius: 0.375rem;
  font-family: 'Montserrat', sans-serif;
  font-size: 0.75rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  position: relative;
  overflow: hidden;
  box-sizing: border-box;
  vertical-align: middle;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
  -webkit-tap-highlight-color: transparent;
}

.gaming-pagination-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.gaming-pagination-btn:hover::before {
  left: 100%;
}

/* Number Buttons */

.gaming-pagination-btn--number {
  background: linear-gradient(145deg, rgba(39, 36, 80, 0.8) 0%, rgba(30, 41, 59, 0.6) 100%);
  color: #FFFFFF99;
  border: 1px solid rgba(80, 129, 255, 0.3);
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
}

.gaming-pagination-btn--number:hover {
  background: linear-gradient(145deg, rgba(80, 129, 255, 0.2) 0%, rgba(75, 125, 255, 0.3) 100%);
  color: #FFFFFF;
  border-color: rgba(80, 129, 255, 0.6);
  box-shadow:
      0 4px 8px rgba(80, 129, 255, 0.3),
      0 0 12px rgba(80, 129, 255, 0.2);
}

/* Active Page Button */

.gaming-pagination-btn--active {
  background: linear-gradient(135deg, #4B7DFF 0%, #5081FF 50%, #3463DB 100%);
  color: #FFFFFF;
  border: 1px solid rgba(80, 129, 255, 0.8);
  box-shadow:
      0 2px 8px rgba(80, 129, 255, 0.4),
      0 0 12px rgba(80, 129, 255, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.2);
  animation: gaming-pagination-pulse 2s infinite;
}

.gaming-pagination-btn--active:hover {
  box-shadow:
      0 3px 10px rgba(80, 129, 255, 0.5),
      0 0 16px rgba(80, 129, 255, 0.4),
      inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

/* Navigation Buttons (Previous/Next) */

.gaming-pagination-btn--nav {
  background: linear-gradient(145deg, rgba(75, 125, 255, 0.1) 0%, rgba(80, 129, 255, 0.2) 100%);
  color: #5081FF;
  border: 1px solid rgba(80, 129, 255, 0.4);
  min-width: 2rem;
}

.gaming-pagination-btn--nav:hover {
  background: linear-gradient(145deg, rgba(75, 125, 255, 0.3) 0%, rgba(80, 129, 255, 0.4) 100%);
  color: #FFFFFF;
  border-color: rgba(80, 129, 255, 0.8);
  box-shadow:
      0 4px 8px rgba(80, 129, 255, 0.3),
      0 0 12px rgba(80, 129, 255, 0.2);
}

/* Disabled Buttons */

.gaming-pagination-btn--disabled {
  background: linear-gradient(145deg, rgba(39, 36, 80, 0.3) 0%, rgba(30, 41, 59, 0.2) 100%);
  color: #FFFFFF33;
  border: 1px solid rgba(80, 129, 255, 0.1);
  cursor: not-allowed;
  opacity: 0.5;
}

.gaming-pagination-btn--disabled:hover {
  transform: none;
  box-shadow: none;
  background: linear-gradient(145deg, rgba(39, 36, 80, 0.3) 0%, rgba(30, 41, 59, 0.2) 100%);
}

.gaming-pagination-btn--disabled::before {
  display: none;
}

/* Ellipsis */

.gaming-pagination-ellipsis {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 1.75rem;
  height: 1.75rem;
  color: #FFFFFF66;
  font-size: 0.875rem;
}

/* Icons */

.gaming-pagination-icon {
  flex-shrink: 0;
  transition: transform 0.3s ease;
  width: 14px;
  height: 14px;
}

.gaming-pagination-btn:hover .gaming-pagination-icon {
  transform: scale(1.1);
}

.gaming-pagination-text {
  font-size: 0.75rem;
  font-weight: 600;
}

/* Pagination Info */

.gaming-pagination-info {
  text-align: center;
  margin-top: 0.5rem;
}

.gaming-pagination-info-text {
  font-size: 0.8125rem;
  color: #FFFFFF99;
  font-family: 'Montserrat', sans-serif;
}

.gaming-pagination-info-current,
  .gaming-pagination-info-total {
  font-weight: 700;
  color: #5081FF;
  text-shadow: 0 0 6px rgba(80, 129, 255, 0.5);
}

/* ===== AUTO-ADVANCE FUNCTIONALITY STYLES ===== */

.gaming-pagination-controls {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 0.5rem;
  flex-wrap: wrap;
}

.gaming-auto-advance-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.375rem;
  padding: 0.5rem 0.75rem;
  border: 1px solid rgba(80, 129, 255, 0.3);
  border-radius: 0.5rem;
  background: linear-gradient(145deg, rgba(39, 36, 80, 0.6) 0%, rgba(30, 41, 59, 0.4) 100%);
  color: #FFFFFF99;
  font-family: 'Montserrat', sans-serif;
  font-size: 0.75rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  position: relative;
  overflow: hidden;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
}

.gaming-auto-advance-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s ease;
}

.gaming-auto-advance-btn:hover::before {
  left: 100%;
}

.gaming-auto-advance-btn:hover {
  background: linear-gradient(145deg, rgba(80, 129, 255, 0.2) 0%, rgba(75, 125, 255, 0.3) 100%);
  color: #FFFFFF;
  border-color: rgba(80, 129, 255, 0.6);
  box-shadow:
      0 4px 8px rgba(80, 129, 255, 0.3),
      0 0 12px rgba(80, 129, 255, 0.2);
}

.gaming-auto-advance-btn--active {
  background: linear-gradient(135deg, #10B981 0%, #059669 50%, #047857 100%);
  color: #FFFFFF;
  border-color: rgba(16, 185, 129, 0.8);
  box-shadow:
      0 2px 8px rgba(16, 185, 129, 0.4),
      0 0 12px rgba(16, 185, 129, 0.3);
  animation: gaming-auto-advance-pulse 2s infinite;
}

.gaming-auto-advance-btn--active:hover {
  background: linear-gradient(135deg, #059669 0%, #047857 50%, #065f46 100%);
  box-shadow:
      0 4px 12px rgba(16, 185, 129, 0.5),
      0 0 16px rgba(16, 185, 129, 0.4);
}

.gaming-auto-advance-icon {
  flex-shrink: 0;
  transition: transform 0.3s ease;
}

.gaming-auto-advance-btn:hover .gaming-auto-advance-icon {
  transform: scale(1.1);
}

.gaming-auto-advance-btn--active .gaming-auto-advance-icon {
  animation: gaming-auto-advance-spin 2s linear infinite;
}

.gaming-auto-advance-text {
  font-size: 0.75rem;
  font-weight: 600;
  white-space: nowrap;
}

/* Auto-Advance Countdown */

.gaming-auto-advance-countdown {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.25rem;
  padding: 0.5rem;
  background: linear-gradient(145deg, rgba(80, 129, 255, 0.1) 0%, rgba(75, 125, 255, 0.2) 100%);
  border: 1px solid rgba(80, 129, 255, 0.4);
  border-radius: 0.5rem;
  -webkit-backdrop-filter: blur(5px);
          backdrop-filter: blur(5px);
}

.gaming-countdown-circle {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.gaming-countdown-svg {
  transform: rotate(-90deg);
}

.gaming-countdown-progress {
  transition: stroke-dashoffset 1s linear;
}

.gaming-countdown-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-family: 'Montserrat', sans-serif;
  font-size: 0.875rem;
  font-weight: 700;
  color: #5081FF;
  text-shadow: 0 0 4px rgba(80, 129, 255, 0.5);
}

.gaming-countdown-label {
  font-size: 0.6875rem;
  color: #FFFFFF99;
  font-family: 'Montserrat', sans-serif;
  text-align: center;
  white-space: nowrap;
}

/* Gaming Pagination Animations */

@keyframes gaming-pagination-pulse {
  0%, 100% {
    box-shadow:
        0 2px 8px rgba(80, 129, 255, 0.4),
        0 0 12px rgba(80, 129, 255, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
  }

  50% {
    box-shadow:
        0 3px 10px rgba(80, 129, 255, 0.6),
        0 0 16px rgba(80, 129, 255, 0.5),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
  }
}

@keyframes gaming-auto-advance-pulse {
  0%, 100% {
    box-shadow:
        0 2px 8px rgba(16, 185, 129, 0.4),
        0 0 12px rgba(16, 185, 129, 0.3);
  }

  50% {
    box-shadow:
        0 4px 12px rgba(16, 185, 129, 0.6),
        0 0 20px rgba(16, 185, 129, 0.5);
  }
}

@keyframes gaming-auto-advance-spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

/* Mobile Layout Utilities */

.gaming-pagination-mobile-scroll {
  overflow-x: auto;
  overflow-y: hidden;
  -webkit-overflow-scrolling: touch;
  scroll-behavior: smooth;
}

.gaming-pagination-mobile-scroll::-webkit-scrollbar {
  height: 2px;
}

.gaming-pagination-mobile-scroll::-webkit-scrollbar-track {
  background: rgba(80, 129, 255, 0.1);
  border-radius: 1px;
}

.gaming-pagination-mobile-scroll::-webkit-scrollbar-thumb {
  background: rgba(80, 129, 255, 0.3);
  border-radius: 1px;
}

/* ===== COMPACT MOBILE RESPONSIVE DESIGN ===== */

/* Tablet (769px - 1024px) */

@media (min-width: 769px) and (max-width: 1024px) {
  .gaming-pagination-container {
    /* padding: 1.25rem;
      gap: 0.875rem; */
  }

  .gaming-pagination-list {
    gap: 0.5rem;
  }

  .gaming-pagination-btn {
    min-width: 2rem;
    height: 2rem;
    padding: 0.375rem 0.5rem;
    font-size: 0.8125rem;
  }

  .gaming-pagination-btn--nav {
    min-width: 2.25rem;
  }

  .gaming-pagination-icon {
    width: 16px;
    height: 16px;
  }

  .gaming-pagination-text {
    font-size: 0.8125rem;
  }

  .gaming-pagination-info-text {
    font-size: 0.875rem;
  }
}

/* Medium Mobile (481px - 768px) */

@media (max-width: 768px) {
  .gaming-pagination-container {
    padding: 1rem 0.75rem;
    gap: 0.75rem;
    margin: 0 0.25rem;
  }

  .gaming-pagination-list {
    gap: 0.375rem;
    min-height: 2.25rem;
  }

  .gaming-pagination-btn {
    min-width: 2rem;
    height: 2rem;
    padding: 0.375rem 0.5rem;
    font-size: 0.8125rem;
  }

  .gaming-pagination-btn--nav {
    min-width: 2.25rem;
  }

  .gaming-pagination-ellipsis {
    min-width: 2rem;
    height: 2rem;
  }

  .gaming-pagination-icon {
    width: 16px;
    height: 16px;
  }

  .gaming-pagination-text {
    font-size: 0.75rem;
  }

  .gaming-pagination-info-text {
    font-size: 0.875rem;
  }

  /* Auto-advance mobile styles */

  .gaming-pagination-controls {
    gap: 0.75rem;
    flex-direction: column;
  }

  .gaming-auto-advance-btn {
    padding: 0.375rem 0.625rem;
    font-size: 0.6875rem;
  }

  .gaming-auto-advance-text {
    font-size: 0.6875rem;
  }

  .gaming-auto-advance-countdown {
    padding: 0.375rem;
  }

  .gaming-countdown-text {
    font-size: 0.75rem;
  }

  .gaming-countdown-label {
    font-size: 0.625rem;
  }
}

/* Small Mobile (≤640px) */

@media (max-width: 640px) {
  .gaming-pagination-container {
    padding: 0.875rem 0.5rem;
    gap: 0.625rem;
  }

  .gaming-pagination-list {
    gap: 0.25rem;
    overflow-x: auto;
    padding: 0.125rem 0;
    scrollbar-width: none;
    -ms-overflow-style: none;
  }

  .gaming-pagination-list::-webkit-scrollbar {
    display: none;
  }

  .gaming-pagination-btn {
    min-width: 1.875rem;
    height: 1.875rem;
    padding: 0.25rem 0.375rem;
    font-size: 0.75rem;
    flex-shrink: 0;
  }

  .gaming-pagination-btn--nav {
    min-width: 2rem;
  }

  .gaming-pagination-ellipsis {
    min-width: 1.875rem;
    height: 1.875rem;
  }

  .gaming-pagination-icon {
    width: 14px;
    height: 14px;
  }

  .gaming-pagination-text {
    font-size: 0.6875rem;
  }

  /* Mobile touch optimizations */

  .gaming-pagination-btn:active {
    transform: translateY(0) scale(0.98);
    transition: all 0.1s ease;
  }
}

/* Extra Small Mobile (≤480px) */

@media (max-width: 480px) {
  .gaming-pagination-container {
    padding: 0.75rem 0.375rem;
    gap: 0.5rem;
    margin: 0;
    /* border-radius: 0.5rem; */
  }

  .gaming-pagination-list {
    gap: 0.125rem;
    padding: 0;
  }

  .gaming-pagination-text {
    display: none !important;
    /* Hide text labels on very small screens */
  }

  .gaming-pagination-btn {
    min-width: 1.75rem;
    height: 1.75rem;
    padding: 0.125rem 0.25rem;
    font-size: 0.6875rem;
    border-radius: 0.25rem;
  }

  .gaming-pagination-btn--nav {
    min-width: 1.875rem;
    padding: 0.125rem 0.25rem;
  }

  .gaming-pagination-ellipsis {
    min-width: 1.75rem;
    height: 1.75rem;
    font-size: 0.75rem;
  }

  .gaming-pagination-icon {
    width: 12px;
    height: 12px;
  }

  .gaming-pagination-info-text {
    font-size: 0.75rem;
  }

  /* Auto-advance extra small mobile styles */

  .gaming-pagination-controls {
    gap: 0.5rem;
    flex-direction: column;
  }

  .gaming-auto-advance-btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.625rem;
    gap: 0.25rem;
  }

  .gaming-auto-advance-text {
    font-size: 0.625rem;
  }

  .gaming-auto-advance-icon {
    width: 12px;
    height: 12px;
  }

  .gaming-auto-advance-countdown {
    padding: 0.25rem;
    gap: 0.125rem;
  }

  .gaming-countdown-svg {
    width: 24px;
    height: 24px;
  }

  .gaming-countdown-text {
    font-size: 0.6875rem;
  }

  .gaming-countdown-label {
    font-size: 0.5625rem;
  }
}

/* Ultra Small Mobile (≤360px) */

@media (max-width: 360px) {
  .gaming-pagination-container {
    padding: 0.625rem 0.25rem;
    gap: 0.375rem;
  }

  .gaming-pagination-list {
    gap: 0.0625rem;
    /* 1px gap */
  }

  .gaming-pagination-btn {
    min-width: 1.5rem;
    height: 1.5rem;
    padding: 0.0625rem 0.125rem;
    font-size: 0.625rem;
  }

  .gaming-pagination-btn--nav {
    min-width: 1.625rem;
  }

  .gaming-pagination-ellipsis {
    min-width: 1.5rem;
    height: 1.5rem;
    font-size: 0.6875rem;
  }

  .gaming-pagination-icon {
    width: 10px;
    height: 10px;
  }

  .gaming-pagination-info-text {
    font-size: 0.6875rem;
  }
}

/* Large Screen Enhancements (≥1024px) */

@media (min-width: 1024px) {
  .gaming-pagination-container {
    padding: 1.5rem;
    gap: 1rem;
  }

  .gaming-pagination-list {
    gap: 0.5rem;
  }

  .gaming-pagination-btn {
    min-width: 2.25rem;
    height: 2.25rem;
    padding: 0.5rem 0.625rem;
    font-size: 0.875rem;
  }

  .gaming-pagination-btn--nav {
    min-width: 2.75rem;
  }

  .gaming-pagination-text {
    font-size: 0.875rem;
  }

  .gaming-pagination-icon {
    width: 18px;
    height: 18px;
  }

  .gaming-pagination-info-text {
    font-size: 0.9375rem;
  }

  .gaming-pagination-btn--number:hover {
  }
}

/* Accessibility and Performance */

@media (prefers-reduced-motion: reduce) {
  .gaming-pagination-btn {
    transition: background-color 0.2s ease, color 0.2s ease;
  }

  .gaming-pagination-btn:hover {
    transform: none;
    animation: none;
  }

  .gaming-pagination-btn--active {
    animation: none;
  }

  .gaming-pagination-btn::before {
    display: none;
  }
}

/* Focus States for Accessibility */

.gaming-pagination-btn:focus {
  outline: none;
  box-shadow:
      0 0 0 2px rgba(80, 129, 255, 0.5),
      0 2px 6px rgba(80, 129, 255, 0.3);
}

.gaming-pagination-btn:focus-visible {
  outline: 2px solid #5081FF;
  outline-offset: 2px;
}

/* Prevent layout shift */

.gaming-pagination-list {
  min-height: 2rem;
}

@media (max-width: 768px) {
  .gaming-pagination-list {
    min-height: 2.25rem;
  }
}

@media (max-width: 480px) {
  .gaming-pagination-list {
    min-height: 1.75rem;
  }
}

@media (max-width: 360px) {
  .gaming-pagination-list {
    min-height: 1.5rem;
  }
}

@media not all and (min-width: 768px) {
  .max-md\:-mx-\[16px\] {
    margin-left: -16px;
    margin-right: -16px;
  }
}

.before\:absolute::before {
  content: var(--tw-content);
  position: absolute;
}

.before\:inset-0::before {
  content: var(--tw-content);
  inset: 0px;
}

.before\:top-0::before {
  content: var(--tw-content);
  top: 0px;
}

.before\:block::before {
  content: var(--tw-content);
  display: block;
}

.before\:h-full::before {
  content: var(--tw-content);
  height: 100%;
}

.before\:w-0::before {
  content: var(--tw-content);
  width: 0px;
}

.before\:translate-x-\[-100\%\]::before {
  content: var(--tw-content);
  --tw-translate-x: -100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.before\:bg-\[\#fff\]::before {
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}

.before\:bg-gradient-to-r::before {
  content: var(--tw-content);
  background-image: linear-gradient(to right, var(--tw-gradient-stops));
}

.before\:from-transparent::before {
  content: var(--tw-content);
  --tw-gradient-from: transparent var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(0 0 0 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.before\:via-white\/20::before {
  content: var(--tw-content);
  --tw-gradient-to: rgb(255 255 255 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(255 255 255 / 0.2) var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.before\:to-transparent::before {
  content: var(--tw-content);
  --tw-gradient-to: transparent var(--tw-gradient-to-position);
}

.before\:transition-transform::before {
  content: var(--tw-content);
  transition-property: transform;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.before\:duration-700::before {
  content: var(--tw-content);
  transition-duration: 700ms;
}

.before\:ease-in-out::before {
  content: var(--tw-content);
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

.before\:content-\[\"\"\]::before {
  --tw-content: "";
  content: var(--tw-content);
}

.hover\:-translate-y-1:hover {
  --tw-translate-y: -0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:-translate-y-2:hover {
  --tw-translate-y: -0.5rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:rotate-90:hover {
  --tw-rotate: 90deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:scale-105:hover {
  --tw-scale-x: 1.05;
  --tw-scale-y: 1.05;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:scale-110:hover {
  --tw-scale-x: 1.1;
  --tw-scale-y: 1.1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:scale-\[1\.02\]:hover {
  --tw-scale-x: 1.02;
  --tw-scale-y: 1.02;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:scale-\[1\.05\]:hover {
  --tw-scale-x: 1.05;
  --tw-scale-y: 1.05;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:border-\[\#3463DB\]:hover {
  --tw-border-opacity: 1;
  border-color: rgb(52 99 219 / var(--tw-border-opacity, 1));
}

.hover\:border-\[\#4B7DFF\]:hover {
  --tw-border-opacity: 1;
  border-color: rgb(75 125 255 / var(--tw-border-opacity, 1));
}

.hover\:border-\[\#4B7DFF\]\/60:hover {
  border-color: rgb(75 125 255 / 0.6);
}

.hover\:border-\[\#4B7DFF\]\/80:hover {
  border-color: rgb(75 125 255 / 0.8);
}

.hover\:border-\[\#5081FF\]:hover {
  --tw-border-opacity: 1;
  border-color: rgb(80 129 255 / var(--tw-border-opacity, 1));
}

.hover\:border-\[\#6366F1\]:hover {
  --tw-border-opacity: 1;
  border-color: rgb(99 102 241 / var(--tw-border-opacity, 1));
}

.hover\:border-\[\#ff062e\]:hover {
  --tw-border-opacity: 1;
  border-color: rgb(255 6 46 / var(--tw-border-opacity, 1));
}

.hover\:border-gaming-blue:hover {
  --tw-border-opacity: 1;
  border-color: rgb(80 129 255 / var(--tw-border-opacity, 1));
}

.hover\:border-gaming-blue\/40:hover {
  border-color: rgb(80 129 255 / 0.4);
}

.hover\:border-opacity-40:hover {
  --tw-border-opacity: 0.4;
}

.hover\:bg-\[\#0052CC\]:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(0 82 204 / var(--tw-bg-opacity, 1));
}

.hover\:bg-\[\#2A2D4F\]:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(42 45 79 / var(--tw-bg-opacity, 1));
}

.hover\:bg-\[\#3463DB\]:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(52 99 219 / var(--tw-bg-opacity, 1));
}

.hover\:bg-\[\#4752C4\]:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(71 82 196 / var(--tw-bg-opacity, 1));
}

.hover\:bg-\[\#4B7DFF\]:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(75 125 255 / var(--tw-bg-opacity, 1));
}

.hover\:bg-\[\#FFFFFF1F\]:hover {
  background-color: #FFFFFF1F;
}

.hover\:bg-\[\#ff062e\]\/20:hover {
  background-color: rgb(255 6 46 / 0.2);
}

.hover\:bg-gaming-blue\/40:hover {
  background-color: rgb(80 129 255 / 0.4);
}

.hover\:bg-gray-600\/50:hover {
  background-color: rgb(75 85 99 / 0.5);
}

.hover\:bg-mandy-500:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(229 62 62 / var(--tw-bg-opacity, 1));
}

.hover\:bg-red-400\/10:hover {
  background-color: rgb(248 113 113 / 0.1);
}

.hover\:bg-opacity-80:hover {
  --tw-bg-opacity: 0.8;
}

.hover\:from-\[\#4B7DFF\]:hover {
  --tw-gradient-from: #4B7DFF var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(75 125 255 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.hover\:from-\[\#5081FF\]:hover {
  --tw-gradient-from: #5081FF var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(80 129 255 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.hover\:from-amber-600:hover {
  --tw-gradient-from: #d97706 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(217 119 6 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.hover\:from-red-600:hover {
  --tw-gradient-from: #dc2626 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(220 38 38 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.hover\:via-\[\#4B7DFF\]:hover {
  --tw-gradient-to: rgb(75 125 255 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #4B7DFF var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.hover\:to-\[\#2A52BE\]:hover {
  --tw-gradient-to: #2A52BE var(--tw-gradient-to-position);
}

.hover\:to-\[\#5081FF\]:hover {
  --tw-gradient-to: #5081FF var(--tw-gradient-to-position);
}

.hover\:to-\[\#7C3AED\]:hover {
  --tw-gradient-to: #7C3AED var(--tw-gradient-to-position);
}

.hover\:to-orange-600:hover {
  --tw-gradient-to: #ea580c var(--tw-gradient-to-position);
}

.hover\:to-red-700:hover {
  --tw-gradient-to: #b91c1c var(--tw-gradient-to-position);
}

.hover\:text-\[\#4B7DFF\]:hover {
  --tw-text-opacity: 1;
  color: rgb(75 125 255 / var(--tw-text-opacity, 1));
}

.hover\:text-\[\#FFFFFFCC\]:hover {
  color: #FFFFFFCC;
}

.hover\:text-\[\#fff\]:hover {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.hover\:underline:hover {
  text-decoration-line: underline;
}

.hover\:shadow:hover {
  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.hover\:shadow-2xl:hover {
  --tw-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);
  --tw-shadow-colored: 0 25px 50px -12px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.hover\:shadow-lg:hover {
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.hover\:shadow-md:hover {
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.hover\:shadow-xl:hover {
  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.hover\:shadow-\[\#0068FF\]\/30:hover {
  --tw-shadow-color: rgb(0 104 255 / 0.3);
  --tw-shadow: var(--tw-shadow-colored);
}

.hover\:shadow-\[\#4B7DFF\]\/30:hover {
  --tw-shadow-color: rgb(75 125 255 / 0.3);
  --tw-shadow: var(--tw-shadow-colored);
}

.hover\:shadow-\[\#4B7DFF\]\/40:hover {
  --tw-shadow-color: rgb(75 125 255 / 0.4);
  --tw-shadow: var(--tw-shadow-colored);
}

.hover\:shadow-\[\#5081FF\]\/40:hover {
  --tw-shadow-color: rgb(80 129 255 / 0.4);
  --tw-shadow: var(--tw-shadow-colored);
}

.hover\:shadow-\[\#5081FF\]\/50:hover {
  --tw-shadow-color: rgb(80 129 255 / 0.5);
  --tw-shadow: var(--tw-shadow-colored);
}

.hover\:shadow-\[\#5865F2\]\/30:hover {
  --tw-shadow-color: rgb(88 101 242 / 0.3);
  --tw-shadow: var(--tw-shadow-colored);
}

.hover\:shadow-\[\#6366F1\]\/50:hover {
  --tw-shadow-color: rgb(99 102 241 / 0.5);
  --tw-shadow: var(--tw-shadow-colored);
}

.hover\:shadow-mandy-500:hover {
  --tw-shadow-color: #E53E3E;
  --tw-shadow: var(--tw-shadow-colored);
}

.hover\:before\:translate-x-\[100\%\]:hover::before {
  content: var(--tw-content);
  --tw-translate-x: 100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.focus\:border-\[\#00FF88\]:focus {
  --tw-border-opacity: 1;
  border-color: rgb(0 255 136 / var(--tw-border-opacity, 1));
}

.focus\:border-\[\#5081FF\]:focus {
  --tw-border-opacity: 1;
  border-color: rgb(80 129 255 / var(--tw-border-opacity, 1));
}

.focus\:border-\[\#9C27B0\]:focus {
  --tw-border-opacity: 1;
  border-color: rgb(156 39 176 / var(--tw-border-opacity, 1));
}

.focus\:border-\[\#FFD700\]:focus {
  --tw-border-opacity: 1;
  border-color: rgb(255 215 0 / var(--tw-border-opacity, 1));
}

.focus\:outline-none:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.focus\:ring-2:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus\:ring-\[\#00FF88\]\/20:focus {
  --tw-ring-color: rgb(0 255 136 / 0.2);
}

.focus\:ring-\[\#5081FF\]\/20:focus {
  --tw-ring-color: rgb(80 129 255 / 0.2);
}

.focus\:ring-\[\#9C27B0\]\/20:focus {
  --tw-ring-color: rgb(156 39 176 / 0.2);
}

.focus\:ring-\[\#FFD700\]\/20:focus {
  --tw-ring-color: rgb(255 215 0 / 0.2);
}

.focus\:ring-red-500:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(239 68 68 / var(--tw-ring-opacity, 1));
}

.focus\:ring-offset-2:focus {
  --tw-ring-offset-width: 2px;
}

.focus\:ring-offset-gray-800:focus {
  --tw-ring-offset-color: #1f2937;
}

.focus-visible\:outline-none:focus-visible {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.focus-visible\:ring-2:focus-visible {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus-visible\:ring-\[\#0068FF\]:focus-visible {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(0 104 255 / var(--tw-ring-opacity, 1));
}

.focus-visible\:ring-\[\#4B7DFF\]:focus-visible {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(75 125 255 / var(--tw-ring-opacity, 1));
}

.focus-visible\:ring-\[\#5865F2\]:focus-visible {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(88 101 242 / var(--tw-ring-opacity, 1));
}

.focus-visible\:ring-offset-2:focus-visible {
  --tw-ring-offset-width: 2px;
}

.disabled\:pointer-events-none:disabled {
  pointer-events: none;
}

.disabled\:cursor-not-allowed:disabled {
  cursor: not-allowed;
}

.disabled\:opacity-50:disabled {
  opacity: 0.5;
}

.group\/btn:hover .group-hover\/btn\:-translate-x-full {
  --tw-translate-x: -100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group\/btn:hover .group-hover\/btn\:translate-x-1 {
  --tw-translate-x: 0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group\/btn:hover .group-hover\/btn\:translate-x-full {
  --tw-translate-x: 100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group:hover .group-hover\:translate-x-1 {
  --tw-translate-x: 0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group:hover .group-hover\:scale-105 {
  --tw-scale-x: 1.05;
  --tw-scale-y: 1.05;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group:hover .group-hover\:scale-110 {
  --tw-scale-x: 1.1;
  --tw-scale-y: 1.1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group:hover .group-hover\:scale-\[1\.02\] {
  --tw-scale-x: 1.02;
  --tw-scale-y: 1.02;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group\/stat:hover .group-hover\/stat\:scale-x-100 {
  --tw-scale-x: 1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

@keyframes ping {
  75%, 100% {
    transform: scale(2);
    opacity: 0;
  }
}

.group\/btn:hover .group-hover\/btn\:animate-ping {
  animation: ping 1s cubic-bezier(0, 0, 0.2, 1) infinite;
}

@keyframes pulse {
  50% {
    opacity: .5;
  }
}

.group\/btn:hover .group-hover\/btn\:animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.group:hover .group-hover\:border-blue-400\/60 {
  border-color: rgb(96 165 250 / 0.6);
}

.group:hover .group-hover\:text-white {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.group\/btn:hover .group-hover\/btn\:opacity-100 {
  opacity: 1;
}

.group\/stat:hover .group-hover\/stat\:opacity-100 {
  opacity: 1;
}

.group:hover .group-hover\:opacity-100 {
  opacity: 1;
}

.group:hover .group-hover\:opacity-20 {
  opacity: 0.2;
}

.group:hover .group-hover\:opacity-\[1\] {
  opacity: 1;
}

.group\/btn:hover .group-hover\/btn\:shadow-\[\#00FFFF\]\/50 {
  --tw-shadow-color: rgb(0 255 255 / 0.5);
  --tw-shadow: var(--tw-shadow-colored);
}

.group:hover .group-hover\:shadow-blue-500\/25 {
  --tw-shadow-color: rgb(59 130 246 / 0.25);
  --tw-shadow: var(--tw-shadow-colored);
}

.group:hover .group-hover\:brightness-110 {
  --tw-brightness: brightness(1.1);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.peer:checked ~ .peer-checked\:translate-x-full {
  --tw-translate-x: 100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.peer:checked ~ .peer-checked\:bg-blue-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(59 130 246 / var(--tw-bg-opacity, 1));
}

@media not all and (min-width: 1024px) {
  .max-lg\:col-span-3 {
    grid-column: span 3 / span 3;
  }

  .max-lg\:mb-\[55px\] {
    margin-bottom: 55px;
  }

  .max-lg\:hidden {
    display: none;
  }

  .max-lg\:gap-y-\[12px\] {
    row-gap: 12px;
  }

  .max-lg\:text-\[14px\] {
    font-size: 14px;
  }
}

@media not all and (min-width: 768px) {
  .max-md\:\!-mx-\[16px\] {
    margin-left: -16px !important;
    margin-right: -16px !important;
  }

  .max-md\:-mx-\[16px\] {
    margin-left: -16px;
    margin-right: -16px;
  }

  .max-md\:mt-0 {
    margin-top: 0px;
  }
}

@media (min-width: 640px) {
  .sm\:inline {
    display: inline;
  }

  .sm\:hidden {
    display: none;
  }

  .sm\:max-w-sm {
    max-width: 24rem;
  }

  .sm\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .sm\:p-0 {
    padding: 0px;
  }

  .sm\:text-\[14px\] {
    font-size: 14px;
  }
}

@media (min-width: 768px) {
  .md\:col-span-9 {
    grid-column: span 9 / span 9;
  }

  .md\:row-span-2 {
    grid-row: span 2 / span 2;
  }

  .md\:mt-\[24px\] {
    margin-top: 24px;
  }

  .md\:block {
    display: block;
  }

  .md\:flex {
    display: flex;
  }

  .md\:hidden {
    display: none;
  }

  .md\:aspect-\[228\/94\] {
    aspect-ratio: 228/94;
  }

  .md\:aspect-\[716\/203\] {
    aspect-ratio: 716/203;
  }

  .md\:h-16 {
    height: 4rem;
  }

  .md\:h-\[44px\] {
    height: 44px;
  }

  .md\:h-full {
    height: 100%;
  }

  .md\:max-h-\[180px\] {
    max-height: 180px;
  }

  .md\:w-\[44px\] {
    width: 44px;
  }

  .md\:w-auto {
    width: auto;
  }

  .md\:w-full {
    width: 100%;
  }

  .md\:max-w-\[345px\] {
    max-width: 345px;
  }

  .md\:flex-1 {
    flex: 1 1 0%;
  }

  .md\:grid-cols-12 {
    grid-template-columns: repeat(12, minmax(0, 1fr));
  }

  .md\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .md\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .md\:grid-cols-7 {
    grid-template-columns: repeat(7, minmax(0, 1fr));
  }

  .md\:justify-center {
    justify-content: center;
  }

  .md\:justify-between {
    justify-content: space-between;
  }

  .md\:gap-2 {
    gap: 0.5rem;
  }

  .md\:gap-5 {
    gap: 1.25rem;
  }

  .md\:rounded-\[16px\] {
    border-radius: 16px;
  }

  .md\:border-none {
    border-style: none;
  }

  .md\:bg-\[\#ffffff14\] {
    background-color: #ffffff14;
  }

  .md\:bg-transparent {
    background-color: transparent;
  }

  .md\:\!px-\[0\.65rem\] {
    padding-left: 0.65rem !important;
    padding-right: 0.65rem !important;
  }

  .md\:px-10 {
    padding-left: 2.5rem;
    padding-right: 2.5rem;
  }

  .md\:px-20 {
    padding-left: 5rem;
    padding-right: 5rem;
  }

  .md\:px-3 {
    padding-left: 0.75rem;
    padding-right: 0.75rem;
  }

  .md\:px-\[32px\] {
    padding-left: 32px;
    padding-right: 32px;
  }

  .md\:py-2 {
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
  }

  .md\:py-\[16px\] {
    padding-top: 16px;
    padding-bottom: 16px;
  }

  .md\:text-3xl {
    font-size: 1.875rem;
    line-height: 2.25rem;
  }

  .md\:text-4xl {
    font-size: 2.25rem;
    line-height: 2.5rem;
  }

  .md\:text-5xl {
    font-size: 3rem;
    line-height: 1;
  }

  .md\:text-\[16px\] {
    font-size: 16px;
  }

  .md\:text-sm {
    font-size: 0.875rem;
    line-height: 1.25rem;
  }

  .md\:text-xl {
    font-size: 1.25rem;
    line-height: 1.75rem;
  }

  .md\:leading-\[15px\] {
    line-height: 15px;
  }

  .md\:text-\[\#fff9\] {
    color: #fff9;
  }

  .md\:transition-all {
    transition-property: all;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms;
  }

  .md\:duration-300 {
    transition-duration: 300ms;
  }

  .group:hover .md\:group-hover\:scale-\[1\.25\] {
    --tw-scale-x: 1.25;
    --tw-scale-y: 1.25;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .group:hover .md\:group-hover\:text-\[\#fff\] {
    --tw-text-opacity: 1;
    color: rgb(255 255 255 / var(--tw-text-opacity, 1));
  }
}

@media (min-width: 1024px) {
  .lg\:bottom-0 {
    bottom: 0px;
  }

  .lg\:end-auto {
    inset-inline-end: auto;
  }

  .lg\:z-30 {
    z-index: 30;
  }

  .lg\:order-1 {
    order: 1;
  }

  .lg\:order-2 {
    order: 2;
  }

  .lg\:col-span-1 {
    grid-column: span 1 / span 1;
  }

  .lg\:col-span-3 {
    grid-column: span 3 / span 3;
  }

  .lg\:block {
    display: block;
  }

  .lg\:inline-block {
    display: inline-block;
  }

  .lg\:inline {
    display: inline;
  }

  .lg\:flex {
    display: flex;
  }

  .lg\:hidden {
    display: none;
  }

  .lg\:aspect-\[318\/132\] {
    aspect-ratio: 318/132;
  }

  .lg\:aspect-\[986\/280\] {
    aspect-ratio: 986/280;
  }

  .lg\:\!w-1\/3 {
    width: 33.333333% !important;
  }

  .lg\:\!w-2\/3 {
    width: 66.666667% !important;
  }

  .lg\:\!w-2\/5 {
    width: 40% !important;
  }

  .lg\:\!w-3\/5 {
    width: 60% !important;
  }

  .lg\:w-1\/3 {
    width: 33.333333%;
  }

  .lg\:w-2\/3 {
    width: 66.666667%;
  }

  .lg\:w-2\/5 {
    width: 40%;
  }

  .lg\:w-3\/5 {
    width: 60%;
  }

  .lg\:w-80 {
    width: 20rem;
  }

  .lg\:translate-x-0 {
    --tw-translate-x: 0px;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .lg\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .lg\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .lg\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .lg\:flex-row {
    flex-direction: row;
  }

  .lg\:items-center {
    align-items: center;
  }

  .lg\:gap-6 {
    gap: 1.5rem;
  }

  .lg\:gap-\[24px\] {
    gap: 24px;
  }

  .lg\:p-6 {
    padding: 1.5rem;
  }

  .lg\:px-10 {
    padding-left: 2.5rem;
    padding-right: 2.5rem;
  }

  .lg\:px-\[16px\] {
    padding-left: 16px;
    padding-right: 16px;
  }

  .lg\:py-0 {
    padding-top: 0px;
    padding-bottom: 0px;
  }

  .lg\:text-5xl {
    font-size: 3rem;
    line-height: 1;
  }

  .lg\:text-\[24px\] {
    font-size: 24px;
  }

  .lg\:leading-\[22px\] {
    line-height: 22px;
  }
}

@media (min-width: 1280px) {
  .xl\:h-12 {
    height: 3rem;
  }

  .xl\:max-h-\[170px\] {
    max-height: 170px;
  }

  .xl\:w-96 {
    width: 24rem;
  }

  .xl\:min-w-\[200px\] {
    min-width: 200px;
  }

  .xl\:max-w-\[320px\] {
    max-width: 320px;
  }

  .xl\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .xl\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .xl\:gap-5 {
    gap: 1.25rem;
  }

  .xl\:gap-6 {
    gap: 1.5rem;
  }

  .xl\:px-20 {
    padding-left: 5rem;
    padding-right: 5rem;
  }

  .xl\:px-40 {
    padding-left: 10rem;
    padding-right: 10rem;
  }

  .xl\:text-4xl {
    font-size: 2.25rem;
    line-height: 2.5rem;
  }
}

@media (min-width: 1536px) {
  .\32xl\:gap-5 {
    gap: 1.25rem;
  }
}

.ltr\:right-0:where([dir="ltr"], [dir="ltr"] *) {
  right: 0px;
}

.ltr\:right-6:where([dir="ltr"], [dir="ltr"] *) {
  right: 1.5rem;
}

.ltr\:ml-1:where([dir="ltr"], [dir="ltr"] *) {
  margin-left: 0.25rem;
}

.rtl\:left-0:where([dir="rtl"], [dir="rtl"] *) {
  left: 0px;
}

.rtl\:left-6:where([dir="rtl"], [dir="rtl"] *) {
  left: 1.5rem;
}

.rtl\:mr-1:where([dir="rtl"], [dir="rtl"] *) {
  margin-right: 0.25rem;
}

.rtl\:translate-x-full:where([dir="rtl"], [dir="rtl"] *) {
  --tw-translate-x: 100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.rtl\:rotate-180:where([dir="rtl"], [dir="rtl"] *) {
  --tw-rotate: 180deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

@media (min-width: 1024px) {
  .rtl\:lg\:translate-x-0:where([dir="rtl"], [dir="rtl"] *) {
    --tw-translate-x: 0px;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }
}

@media (prefers-color-scheme: dark) {
  .dark\:flex {
    display: flex;
  }

  .dark\:hidden {
    display: none;
  }

  .dark\:bg-\[\#060818\] {
    --tw-bg-opacity: 1;
    background-color: rgb(6 8 24 / var(--tw-bg-opacity, 1));
  }

  .dark\:bg-\[\#121c2c\] {
    --tw-bg-opacity: 1;
    background-color: rgb(18 28 44 / var(--tw-bg-opacity, 1));
  }

  .dark\:bg-black {
    --tw-bg-opacity: 1;
    background-color: rgb(0 0 0 / var(--tw-bg-opacity, 1));
  }

  .dark\:bg-success-dark-light {
    --tw-bg-opacity: 1;
    background-color: rgb(21 87 36 / var(--tw-bg-opacity, 1));
  }

  .dark\:text-white {
    --tw-text-opacity: 1;
    color: rgb(255 255 255 / var(--tw-text-opacity, 1));
  }
}

@media print {
  .print\:hidden {
    display: none;
  }
}